<?xml version="1.0" encoding="utf-8"?>
<!--
	美国 California 落地 +15593445095
-->
<include>
	<extension name="California">
		<condition field="destination_number" expression="^0(1)(209|213|279|310|323|341|350|408|415|424|442|510|530|559|562|619|626|628|650|657|661|669|707|714|747|760|805|818|820|831|840|858|909|916|925|949|951)(\d+)$">
			<action application="set" data="effective_caller_id_number=+15593445095"/>  <!--(a Twilio number) -->
			<action application="set" data="effective_caller_id_name=+15593445095"/>  <!--(a Twilio number) -->
			<action application="set" data="destination_number_country=$1"/>  <!--取国家号位 -->
			<action application="set" data="destination_number_region=$2"/> <!--取地区号位 -->
			<action application="set" data="destination_number_code=$3"/> <!--取号码位 -->
			<action application="set" data="destination_number=$1$2$3"/> <!--设置被叫 -->
			<action application="set" data="landline_calling=1"/><!--对应落地呼叫时支持RTP混淆 -->
			<action application="set" data="execute_on_answer=sched_hangup +360 alloted_timeout" />
			<action application="lua" data="akcs_phonecall.lua"/>
		</condition>
	</extension>
</include>

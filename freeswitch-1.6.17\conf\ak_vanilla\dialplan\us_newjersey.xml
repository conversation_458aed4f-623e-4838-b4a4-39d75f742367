<?xml version="1.0" encoding="utf-8"?>
<!--
	美国 New Jersey 落地 +16092935396
-->
<include>
    <extension name="NewJersey">
        <condition field="destination_number" expression="^0(1)(201|551|609|640|732|848|856|862|908|973)(\d+)$">
            <action application="set" data="effective_caller_id_number=+17379779003"/>  <!--(a Twilio number) -->
            <action application="set" data="effective_caller_id_name=+17379779003"/>  <!--(a Twilio number) -->
            <action application="set" data="destination_number_country=$1"/>  <!--取国家号位 -->
            <action application="set" data="destination_number_region=$2"/> <!--取地区号位 -->
            <action application="set" data="destination_number_code=$3"/> <!--取号码位 -->
            <action application="set" data="destination_number=$1$2$3"/> <!--设置被叫 -->
            <action application="set" data="landline_calling=1"/><!--对应落地呼叫时支持RTP混淆 -->
            <action application="set" data="execute_on_answer=sched_hangup +360 alloted_timeout" />
            <action application="lua" data="akcs_phonecall_telnyx.lua"/>
        </condition>
    </extension>
</include>

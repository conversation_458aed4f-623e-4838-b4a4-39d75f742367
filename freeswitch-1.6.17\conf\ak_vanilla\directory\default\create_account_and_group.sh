# !/bin/sh
#create csv file
if [ -f "./accountInfo.csv" ];then
echo "accountInfo.csv has exit"
else
touch ./accountInfo.csv
echo "\"account\",\"passwd\"" >> accountInfo.csv
fi
#create account file
k=10
i=$1
LINENUM=$(sed -n '/'name=\"sales\"'/=' /usr/local/freeswitch/conf/directory/default.xml)
let LINENUM=LINENUM-1
while [ $i -le $2 ]
do
a=$(( $RANDOM % k ))
b=$(( $RANDOM % k ))
c=$(( $RANDOM % k ))
d=$(( $RANDOM % k ))
e=$(( $RANDOM % k ))
f=$(( $RANDOM % k ))
passwd=abc$a$b$c$d$e$f!
value=$(( $i % 100 ))
quot=$(( $i / 100 ))
if [ $value -ne 0 ];then
sed "s/10001/$i/" 10001.xml > $i.xml
sed -i "s/abc123456!/$passwd/" $i.xml
#input account info to csv file
echo "\"$i\",\"$passwd\"" >> accountInfo.csv
fi
#configure default.xml
if [ $value -eq 0 ];then
sed -i "${LINENUM}a\ \n\t\t\t<group name=\"group_$i\">\n\t\t\t\t\t<users>\n\t\t\t\t\t\t\t\t<X-PRE-PROCESS cmd=\"include\" data=\"default/${quot}[0-4][0-9].xml\"/>\n\t\t\t\t\t</users>\n\t\t\t</group>" /usr/local/freeswitch/conf/directory/default.xml
let LINENUM=LINENUM+6
fi
let i=i+1
done

/*
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR <PERSON><PERSON>EQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

package com.adobe.webapis
{
	import flash.events.IOErrorEvent;
	import flash.events.SecurityErrorEvent;
	import flash.events.ProgressEvent;
	
	import com.adobe.net.DynamicURLLoader;
	
		/**
		*  	Dispatched when data is 
		*  	received as the download operation progresses.
		*	 
		* 	@eventType flash.events.ProgressEvent.PROGRESS
		* 
		* @langversion ActionScript 3.0
		* @playerversion Flash 9.0
		*/
		[Event(name="progress", type="flash.events.ProgressEvent")]		
	
		/**
		*	Dispatched if a call to the server results in a fatal 
		*	error that terminates the download.
		* 
		* 	@eventType flash.events.IOErrorEvent.IO_ERROR
		* 
		* @langversion ActionScript 3.0
		* @playerversion Flash 9.0
		*/
		[Event(name="ioError", type="flash.events.IOErrorEvent")]		
		
		/**
		*	A securityError event occurs if a call attempts to
		*	load data from a server outside the security sandbox.
		* 
		* 	@eventType flash.events.SecurityErrorEvent.SECURITY_ERROR
		* 
		* @langversion ActionScript 3.0
		* @playerversion Flash 9.0
		*/
		[Event(name="securityError", type="flash.events.SecurityErrorEvent")]	
	
	/**
	*	Base class for services that utilize URLLoader
	*	to communicate with remote APIs / Services.
	* 
	* @langversion ActionScript 3.0
	* @playerversion Flash 9.0
	*/
	public class URLLoaderBase extends ServiceBase
	{	
		protected function getURLLoader():DynamicURLLoader
		{
			var loader:DynamicURLLoader = new DynamicURLLoader();
				loader.addEventListener("progress", onProgress);
				loader.addEventListener("ioError", onIOError);
				loader.addEventListener("securityError", onSecurityError);
			
			return loader;			
		}		
		
		private function onIOError(event:IOErrorEvent):void
		{
			dispatchEvent(event);
		}			
		
		private function onSecurityError(event:SecurityErrorEvent):void
		{
			dispatchEvent(event);
		}	
		
		private function onProgress(event:ProgressEvent):void
		{
			dispatchEvent(event);
		}	
	}
}
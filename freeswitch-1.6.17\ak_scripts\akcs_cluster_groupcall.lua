package.path = package.path..";/usr/local/freeswitch/scripts/?.lua"
local akcs_util = require("akcs_util")

-- 1欧洲 3美国 9中国 
gateway_num = 0	--安装时替换
function SipDisableLog(msg)
  local date=os.date("%Y-%m-%d %H:%M:%S");
  local file = io.open("/usr/local/freeswitch/sip_enable.log","a")
  file:write(date.." "..msg)
  file:close()
end

-- 生成trace_id
trace_id = akcs_util.generateTraceId(18)
session:setVariable("session_trace_id", trace_id);

-- callkit唯一标识,INVITE时带给app
session:setVariable("sip_h_X-Trace-ID", trace_id)

--freeswitch.consoleLog("NOTICE","lua akcs group call dialplan...\n");
caller = session:getVariable("caller_id_number");
--多套房场景下用主站点的sip发起呼叫
session:setVariable("main_site_caller", caller);
callee = session:getVariable("destination_number");
caller_name = session:getVariable("caller_id_name");

--APP不会作为主叫群呼，暂注释
--caller_site = session:getVariable("akcs_caller_site");
--if(caller_site == "" or caller_site == nil) then
    --caller_site = caller;
--end
--caller = caller_site;

if(gateway_num == 9) then --中国
	session:setVariable("is_landline_final","true");
else
	session:setVariable("is_landline_final","false");
end

local dbh = freeswitch.Dbh("freeswitch","dbuser01","Ak@56@<EMAIL>");
-- 群呼叫不包含发起方
local my_query_callee = string.format("select sipEnable,username,devicenode,opensipsNode,communityid,communityType,groupname,type from userinfo where groupname='%s' and groupring='1' and deleted='0' and username!='%s'", callee, caller);
local my_query_caller = string.format("select sipEnable,devicenode,opensipsNode,groupname,type,communityid,communityType,deleted from userinfo where username='%s'", caller);
local my_query_smart_callee = string.format("select username,opensipsNode from smarthome_userinfo where groupname='%s' and groupring='1'", callee);
local my_query_smart_caller = string.format("select groupname from smarthome_userinfo where username='%s'", caller);
local groupcallstring = ""
local caller_groupname = ""
local smart_groupcallstring = ""
local caller_type = ""
local group_each_sip_list = ""
req_callee_groupname = callee
--主叫可能是纯家居的sip  被叫一定是对讲的sip
check_smarthome = 1
--查询数据库找到对应的节点信息
assert(dbh:connected());

dbh:query(my_query_caller,function(row)
   check_smarthome = 0
   req_caller_opensips_node=string.format("%s",row.opensipsNode);
   req_caller_groupname=string.format("%s",row.groupname);
   caller_type=string.format("%s",row.type);
   req_caller_deleted=string.format("%s",row.deleted);
   req_caller_communityid=string.format("%s",row.communityid);
   req_caller_communityType=string.format("%s",row.communityType);
end);

dbh:query(my_query_callee,function(row)
   req_callee_username=string.format("%s",row.username);
   req_callee_opensips_node=string.format("%s",row.opensipsNode);
   req_callee_communityid=string.format("%s",row.communityid);
   req_callee_communityType=string.format("%s",row.communityType);
   req_callee_type=string.format("%s",row.type);
   

   if(req_callee_type == "6" or req_callee_type == "2" or req_callee_type == "3") then
        group_each_sip_list = group_each_sip_list..req_callee_username..",";
   end
   
   if(req_caller_opensips_node == "" or req_callee_opensips_node == "" or req_callee_opensips_node  == req_caller_opensips_node) then
        if(req_callee_type == "6") then           
            callee_fake = freeswitch.request_main_site_sip(req_callee_username, trace_id);
            freeswitch.consoleLog("notice", "request_main_site_sip callee is:"..req_callee_username.." callee_fake is "..callee_fake.."\n");
            if(callee_fake ~= "" and callee_fake ~= nil) then
                session:setVariable("original_"..callee_fake, req_callee_username);
                req_callee_username = callee_fake;
            end
        end      
        groupcallstring = groupcallstring.."user/"..req_callee_username..",";
    else
        groupcallstring = groupcallstring.."sofia/gateway/"..req_callee_opensips_node.."/"..req_callee_username..",";
    end
end);

--群呼时候,只有家庭内部app和室内机呼叫时候 才需要加入无对讲功能的家居子账号
if caller_type == "2" or caller_type == "6" then
    dbh:query(my_query_smart_callee,function(row)
        req_callee_username=string.format("%s",row.username);
        req_callee_opensips_node=string.format("%s",row.opensipsNode);
        if(req_caller_opensips_node == "" or req_callee_opensips_node == "" or req_callee_opensips_node  == req_caller_opensips_node) then
	        smart_groupcallstring = smart_groupcallstring.."user/"..req_callee_username..",";
        else
	        smart_groupcallstring = smart_groupcallstring.."sofia/gateway/"..req_callee_opensips_node.."/"..req_callee_username..",";
        end
    end);
end

--获取主叫的群组
if check_smarthome then
    dbh:query(my_query_smart_caller,function(row)
        req_caller_groupname=string.format("%s",row.groupname);
    end);
end

dbh:release();

--akcs呼叫规则

if (req_caller_deleted == "1") then	
    freeswitch.consoleLog("notice", " call failed. caller sip deleted. sip is:" .. caller .. "\n");
    return
end	

call_enable = 0;
if (req_caller_communityType == "1" and req_callee_groupname == req_caller_groupname) then
    call_enable = 1;--单住户同个家庭能互呼
elseif (req_caller_communityType == "0" and req_callee_communityid == req_caller_communityid) then
	call_enable = 1;--同个社区下能互通
elseif (check_smarthome and req_callee_groupname == req_caller_groupname) then    
    call_enable = 1;
end	

-- 欧洲特殊处理：2019年我们产品还没有社区的，用单住户扩展为社区来用遗留的数据
if (gateway_num == 1) then
    arr = {1001,1024,1048,1063,1071,1079,108,1081,1147,171,177,179,189,190,202,218,242,243,355,371,376,386,417,
    429,433,448,480,484,499,507,511,539,55,555,558,570,607,664,668,690,696,705,707,711,742,752,83,844,847,849,887,889,919,979}
    for i = 0, #arr do
        if arr[i] == tonumber(req_callee_communityid) then
            call_enable = 1
        end
    end  
end    

if(call_enable == 0) then
    freeswitch.consoleLog("notice", " call reject. cross family or project\n");
    return 1;
end

callee_landline = "";
if(gateway_num == 9) then --中国
	callee_landline = freeswitch.request_land_number(callee,1);	--0代表通过sip号查询 1代表通过sip群组号查询
	freeswitch.consoleLog("notice", "china callee_land is:"..callee_landline.."\n");
	if(callee_landline ~= "") then
		data = akcs_util.split(callee_landline, '-');        
		phone_code = data[1];	
		phone_number = data[2];	
		callee_landline = "sofia/gateway/ippbx/"..phone_number;
	end
end


--session:setVariable("initial_callee_id_number",callee);  --部分机型不支持群组号作为dtmf白名单
--session:setVariable("caller_id_number", caller);
--session:setVariable("effective_caller_id_number", caller);

session:setVariable("groupcall_list", group_each_sip_list);
callstring = groupcallstring..callee_landline..smart_groupcallstring;
freeswitch.consoleLog("NOTICE","groupcallstring="..callstring.."\n");
session:execute("bridge",callstring);



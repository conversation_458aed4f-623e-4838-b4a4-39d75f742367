<configuration name="enum.conf" description="ENUM Module">
  <settings>
    <param name="default-root" value="e164.org"/>
    <param name="default-isn-root" value="freenum.org"/>
    <param name="auto-reload" value="true"/>

    <param name="query-timeout-ms" value="200"/>
    <param name="query-timeout-retry" value="2"/>
    <param name="random-nameserver" value="false"/>

    <!-- If you have specific (non-recursive) servers for your enum queries, specify them here ( up to 10 ) -->
    <!-- <param name="nameserver" value="x.x.x.x"/> -->
    <!-- <param name="nameserver" value="y.y.y.y"/> -->
  </settings>

  <routes>
    <route service="E2U+SIP" regex="sip:(.*)" replace="sofia/${use_profile}-ipv6/$1;transport=udp|sofia/${use_profile}/$1;transport=udp"/>
    <route service="E2T+SIP" regex="sip:(.*)" replace="sofia/${use_profile}-ipv6/$1;transport=tcp|sofia/${use_profile}/$1;transport=tcp"/>
    <route service="E2T+SIPS" regex="sip:(.*)" replace="sofia/${use_profile}-ipv6/$1;transport=tls|sofia/${use_profile}/$1;transport=tls"/>
  </routes>
</configuration>

# Makefile.in generated by automake 1.14.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2013 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@
VPATH = @srcdir@
am__is_gnu_make = test -n '$(MAKEFILE_LIST)' && test -n '$(MAKELEVEL)'
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
subdir = build
DIST_COMMON = $(srcdir)/Makefile.in $(srcdir)/Makefile.am \
	$(srcdir)/getsounds.sh.in $(srcdir)/getlib.sh.in \
	$(srcdir)/getg729.sh.in $(srcdir)/freeswitch.pc.in \
	$(srcdir)/modmake.rules.in
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/acinclude.m4 \
	$(top_srcdir)/build/config/ax_compiler_vendor.m4 \
	$(top_srcdir)/build/config/ax_cflags_warn_all_ansi.m4 \
	$(top_srcdir)/build/config/ax_cc_maxopt.m4 \
	$(top_srcdir)/build/config/ax_check_compiler_flags.m4 \
	$(top_srcdir)/build/config/ac_gcc_archflag.m4 \
	$(top_srcdir)/build/config/ac_gcc_x86_cpuid.m4 \
	$(top_srcdir)/build/config/ax_lib_mysql.m4 \
	$(top_srcdir)/build/config/ax_check_java.m4 \
	$(top_srcdir)/build/config/uuid.m4 \
	$(top_srcdir)/build/config/erlang.m4 \
	$(top_srcdir)/build/config/odbc.m4 \
	$(top_srcdir)/build/config/sched_setaffinity.m4 \
	$(top_srcdir)/libs/apr/build/apr_common.m4 \
	$(top_srcdir)/libs/sofia-sip/m4/sac-pkg-config.m4 \
	$(top_srcdir)/libs/sofia-sip/m4/sac-openssl.m4 \
	$(top_srcdir)/libs/iksemel/build/libgnutls.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/src/include/switch_private.h \
	$(top_builddir)/libs/esl/src/include/esl_config_auto.h \
	$(top_builddir)/libs/xmlrpc-c/xmlrpc_amconfig.h
CONFIG_CLEAN_FILES = getsounds.sh getlib.sh getg729.sh freeswitch.pc \
	modmake.rules
CONFIG_CLEAN_VPATH_FILES =
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
SOURCES =
DIST_SOURCES =
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMQP_CFLAGS = @AMQP_CFLAGS@
AMQP_LIBS = @AMQP_LIBS@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AM_MAKEFLAGS = @AM_MAKEFLAGS@
AM_MOD_AVMD_CXXFLAGS = @AM_MOD_AVMD_CXXFLAGS@
AR = @AR@
ATTR_UNUSED = @ATTR_UNUSED@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AVCODEC_CFLAGS = @AVCODEC_CFLAGS@
AVCODEC_LIBS = @AVCODEC_LIBS@
AVFORMAT_CFLAGS = @AVFORMAT_CFLAGS@
AVFORMAT_LIBS = @AVFORMAT_LIBS@
AVRESAMPLE_CFLAGS = @AVRESAMPLE_CFLAGS@
AVRESAMPLE_LIBS = @AVRESAMPLE_LIBS@
AVUTIL_CFLAGS = @AVUTIL_CFLAGS@
AVUTIL_LIBS = @AVUTIL_LIBS@
AWK = @AWK@
BROADVOICE_CFLAGS = @BROADVOICE_CFLAGS@
BROADVOICE_LIBS = @BROADVOICE_LIBS@
BROTLIDEC_CFLAGS = @BROTLIDEC_CFLAGS@
BROTLIDEC_LIBS = @BROTLIDEC_LIBS@
BROTLIENC_CFLAGS = @BROTLIENC_CFLAGS@
BROTLIENC_LIBS = @BROTLIENC_LIBS@
BZIP = @BZIP@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CC_FOR_BUILD = @CC_FOR_BUILD@
CFLAGS = @CFLAGS@
CODEC2_CFLAGS = @CODEC2_CFLAGS@
CODEC2_LIBS = @CODEC2_LIBS@
CONF_DISABLED_MODULES = @CONF_DISABLED_MODULES@
CONF_MODULES = @CONF_MODULES@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CURL = @CURL@
CURL_CFLAGS = @CURL_CFLAGS@
CURL_LIBS = @CURL_LIBS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DIRECTORY_SEPARATOR = @DIRECTORY_SEPARATOR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
ERLANG = @ERLANG@
ERLANG_CFLAGS = @ERLANG_CFLAGS@
ERLANG_LDFLAGS = @ERLANG_LDFLAGS@
ESL_LDFLAGS = @ESL_LDFLAGS@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
FLITE_CFLAGS = @FLITE_CFLAGS@
FLITE_LIBS = @FLITE_LIBS@
FREETYPE_CFLAGS = @FREETYPE_CFLAGS@
FREETYPE_LIBS = @FREETYPE_LIBS@
G7221_CFLAGS = @G7221_CFLAGS@
G7221_LIBS = @G7221_LIBS@
GETG729 = @GETG729@
GETLIB = @GETLIB@
GETSOUNDS = @GETSOUNDS@
GREP = @GREP@
H2O_CFLAGS = @H2O_CFLAGS@
H2O_LIBS = @H2O_LIBS@
HAVE_LIBWWW_SSL_DEFINE = @HAVE_LIBWWW_SSL_DEFINE@
HAVE_SYS_FILIO_H_DEFINE = @HAVE_SYS_FILIO_H_DEFINE@
HAVE_SYS_IOCTL_H_DEFINE = @HAVE_SYS_IOCTL_H_DEFINE@
HAVE_SYS_SELECT_H_DEFINE = @HAVE_SYS_SELECT_H_DEFINE@
HAVE_WCHAR_H_DEFINE = @HAVE_WCHAR_H_DEFINE@
HIREDIS_CFLAGS = @HIREDIS_CFLAGS@
HIREDIS_LIBS = @HIREDIS_LIBS@
ILBC_CFLAGS = @ILBC_CFLAGS@
ILBC_LIBS = @ILBC_LIBS@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
JACK_CFLAGS = @JACK_CFLAGS@
JACK_LIBS = @JACK_LIBS@
JAVA_FLAGS = @JAVA_FLAGS@
JAVA_HOME = @JAVA_HOME@
LD = @LD@
LDFLAGS = @LDFLAGS@
LDNS_CFLAGS = @LDNS_CFLAGS@
LDNS_LIBS = @LDNS_LIBS@
LIBEDIT_CFLAGS = @LIBEDIT_CFLAGS@
LIBEDIT_LIBS = @LIBEDIT_LIBS@
LIBOBJS = @LIBOBJS@
LIBPNG_CFLAGS = @LIBPNG_CFLAGS@
LIBPNG_LIBS = @LIBPNG_LIBS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIBTOOL_LIB_EXTEN = @LIBTOOL_LIB_EXTEN@
LIBUUID_CFLAGS = @LIBUUID_CFLAGS@
LIBUUID_LIBS = @LIBUUID_LIBS@
LIB_JAVA = @LIB_JAVA@
LIB_SUBDIR = @LIB_SUBDIR@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LUA_CFLAGS = @LUA_CFLAGS@
LUA_LIBS = @LUA_LIBS@
MAGICK_CFLAGS = @MAGICK_CFLAGS@
MAGICK_LIBS = @MAGICK_LIBS@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MEMCACHED_CFLAGS = @MEMCACHED_CFLAGS@
MEMCACHED_LIBS = @MEMCACHED_LIBS@
MKDIR_P = @MKDIR_P@
MONGOC_CFLAGS = @MONGOC_CFLAGS@
MONGOC_LIBS = @MONGOC_LIBS@
MP3LAME_CFLAGS = @MP3LAME_CFLAGS@
MP3LAME_LIBS = @MP3LAME_LIBS@
MPG123_CFLAGS = @MPG123_CFLAGS@
MPG123_LIBS = @MPG123_LIBS@
NET_SNMP_CONFIG = @NET_SNMP_CONFIG@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
ODBC_INC_DIR = @ODBC_INC_DIR@
ODBC_INC_FLAGS = @ODBC_INC_FLAGS@
ODBC_LIB_DIR = @ODBC_LIB_DIR@
ODBC_LIB_FLAGS = @ODBC_LIB_FLAGS@
OPENCV_CFLAGS = @OPENCV_CFLAGS@
OPENCV_LIBS = @OPENCV_LIBS@
OPENLDAP_LIBS = @OPENLDAP_LIBS@
OPUS_CFLAGS = @OPUS_CFLAGS@
OPUS_LIBS = @OPUS_LIBS@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
OUR_CLEAN_MODS = @OUR_CLEAN_MODS@
OUR_DISABLED_CLEAN_MODS = @OUR_DISABLED_CLEAN_MODS@
OUR_DISABLED_INSTALL_MODS = @OUR_DISABLED_INSTALL_MODS@
OUR_DISABLED_MODS = @OUR_DISABLED_MODS@
OUR_DISABLED_UNINSTALL_MODS = @OUR_DISABLED_UNINSTALL_MODS@
OUR_INSTALL_MODS = @OUR_INSTALL_MODS@
OUR_MODS = @OUR_MODS@
OUR_UNINSTALL_MODS = @OUR_UNINSTALL_MODS@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PA_LIBS = @PA_LIBS@
PCRE_CFLAGS = @PCRE_CFLAGS@
PCRE_LIBS = @PCRE_LIBS@
PERL = @PERL@
PERL_CFLAGS = @PERL_CFLAGS@
PERL_INC = @PERL_INC@
PERL_LDFLAGS = @PERL_LDFLAGS@
PERL_LIBDIR = @PERL_LIBDIR@
PERL_LIBS = @PERL_LIBS@
PERL_SITEDIR = @PERL_SITEDIR@
PG_CONFIG = @PG_CONFIG@
PHP = @PHP@
PHP_CFLAGS = @PHP_CFLAGS@
PHP_CONFIG = @PHP_CONFIG@
PHP_EXT_DIR = @PHP_EXT_DIR@
PHP_INC_DIR = @PHP_INC_DIR@
PHP_INI_DIR = @PHP_INI_DIR@
PHP_LDFLAGS = @PHP_LDFLAGS@
PHP_LIBS = @PHP_LIBS@
PKG_CONFIG = @PKG_CONFIG@
PLATFORM_CORE_LDFLAGS = @PLATFORM_CORE_LDFLAGS@
PLATFORM_CORE_LIBS = @PLATFORM_CORE_LIBS@
PORTAUDIO_CFLAGS = @PORTAUDIO_CFLAGS@
PORTAUDIO_LIBS = @PORTAUDIO_LIBS@
PRTDIAG = @PRTDIAG@
PYTHON = @PYTHON@
PYTHON_CFLAGS = @PYTHON_CFLAGS@
PYTHON_LDFLAGS = @PYTHON_LDFLAGS@
PYTHON_SITE_DIR = @PYTHON_SITE_DIR@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
SHOUT_CFLAGS = @SHOUT_CFLAGS@
SHOUT_LIBS = @SHOUT_LIBS@
SILK_CFLAGS = @SILK_CFLAGS@
SILK_LIBS = @SILK_LIBS@
SMPP34_CFLAGS = @SMPP34_CFLAGS@
SMPP34_LIBS = @SMPP34_LIBS@
SNDFILE_CFLAGS = @SNDFILE_CFLAGS@
SNDFILE_LIBS = @SNDFILE_LIBS@
SNMP_LIBS = @SNMP_LIBS@
SOLINK = @SOLINK@
SOUNDTOUCH_CFLAGS = @SOUNDTOUCH_CFLAGS@
SOUNDTOUCH_LIBS = @SOUNDTOUCH_LIBS@
SPANDSP_LA_JBIG = @SPANDSP_LA_JBIG@
SPANDSP_LA_LZMA = @SPANDSP_LA_LZMA@
SPEEX_CFLAGS = @SPEEX_CFLAGS@
SPEEX_LIBS = @SPEEX_LIBS@
SQLITE_CFLAGS = @SQLITE_CFLAGS@
SQLITE_LIBS = @SQLITE_LIBS@
STRIP = @STRIP@
SWITCH_AM_CFLAGS = @SWITCH_AM_CFLAGS@
SWITCH_AM_CPPFLAGS = @SWITCH_AM_CPPFLAGS@
SWITCH_AM_CXXFLAGS = @SWITCH_AM_CXXFLAGS@
SWITCH_AM_LDFLAGS = @SWITCH_AM_LDFLAGS@
SWITCH_ANSI_CFLAGS = @SWITCH_ANSI_CFLAGS@
SWITCH_VERSION_MAJOR = @SWITCH_VERSION_MAJOR@
SWITCH_VERSION_MICRO = @SWITCH_VERSION_MICRO@
SWITCH_VERSION_MINOR = @SWITCH_VERSION_MINOR@
SWITCH_VERSION_REVISION = @SWITCH_VERSION_REVISION@
SWITCH_VERSION_REVISION_HUMAN = @SWITCH_VERSION_REVISION_HUMAN@
SWSCALE_CFLAGS = @SWSCALE_CFLAGS@
SWSCALE_LIBS = @SWSCALE_LIBS@
SYS_XMLRPC_CFLAGS = @SYS_XMLRPC_CFLAGS@
SYS_XMLRPC_LDFLAGS = @SYS_XMLRPC_LDFLAGS@
TAP_CFLAGS = @TAP_CFLAGS@
TAP_LIBS = @TAP_LIBS@
TAR = @TAR@
TOUCH_TARGET = @TOUCH_TARGET@
VA_LIST_IS_ARRAY_DEFINE = @VA_LIST_IS_ARRAY_DEFINE@
VERSION = @VERSION@
VISIBILITY_FLAG = @VISIBILITY_FLAG@
VLC_CFLAGS = @VLC_CFLAGS@
VLC_LIBS = @VLC_LIBS@
WGET = @WGET@
X264_CFLAGS = @X264_CFLAGS@
X264_LIBS = @X264_LIBS@
XZ = @XZ@
YAML_CFLAGS = @YAML_CFLAGS@
YAML_LIBS = @YAML_LIBS@
ZCAT = @ZCAT@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
cachedir = @cachedir@
certsdir = @certsdir@
confdir = @confdir@
datadir = @datadir@
datarootdir = @datarootdir@
dbdir = @dbdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
fontsdir = @fontsdir@
grammardir = @grammardir@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htdocsdir = @htdocsdir@
htmldir = @htmldir@
imagesdir = @imagesdir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
int64_t_fmt = @int64_t_fmt@
int64_value = @int64_value@
int_value = @int_value@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
logfiledir = @logfiledir@
long_value = @long_value@
mandir = @mandir@
mkdir_p = @mkdir_p@
modulesdir = @modulesdir@
oldincludedir = @oldincludedir@
openssl_CFLAGS = @openssl_CFLAGS@
openssl_LIBS = @openssl_LIBS@
pdfdir = @pdfdir@
pkgconfigdir = @pkgconfigdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
recordingsdir = @recordingsdir@
runtimedir = @runtimedir@
sbindir = @sbindir@
scriptdir = @scriptdir@
sharedstatedir = @sharedstatedir@
short_value = @short_value@
size_t_fmt = @size_t_fmt@
size_t_value = @size_t_value@
soundsdir = @soundsdir@
srcdir = @srcdir@
ssize_t_fmt = @ssize_t_fmt@
ssize_t_value = @ssize_t_value@
storagedir = @storagedir@
subdirs = @subdirs@
switch_builddir = @switch_builddir@
switch_srcdir = @switch_srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
uint64_t_fmt = @uint64_t_fmt@
voidp_size = @voidp_size@
MK = `echo $(MAKE) | $(AWK) '{printf "%5s\n", $$0}' `
all: all-am

.SUFFIXES:
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu build/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu build/Makefile
.PRECIOUS: Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
getsounds.sh: $(top_builddir)/config.status $(srcdir)/getsounds.sh.in
	cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@
getlib.sh: $(top_builddir)/config.status $(srcdir)/getlib.sh.in
	cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@
getg729.sh: $(top_builddir)/config.status $(srcdir)/getg729.sh.in
	cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@
freeswitch.pc: $(top_builddir)/config.status $(srcdir)/freeswitch.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@
modmake.rules: $(top_builddir)/config.status $(srcdir)/modmake.rules.in
	cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
tags TAGS:

ctags CTAGS:

cscope cscopelist:


distdir: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile
installdirs:
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool mostlyclean-am

distclean: distclean-am
	-rm -f Makefile
distclean-am: clean-am distclean-generic

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-generic mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am:

.MAKE: install-am install-strip

.PHONY: all all-am check check-am clean clean-generic clean-libtool \
	cscopelist-am ctags-am distclean distclean-generic \
	distclean-libtool distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am install-man \
	install-pdf install-pdf-am install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	maintainer-clean maintainer-clean-generic mostlyclean \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags-am uninstall uninstall-am


all:
	@echo " +---------- FreeSWITCH Build Complete ----------+"
	@echo " + FreeSWITCH has been successfully built.       +"
	@echo " + Install by running:                           +"
	@echo " +                                               +"
	@echo " +               $(MK) install                   +"
	@echo " +                                               +"
	@echo " + While you're waiting, register for ClueCon!   +"
	@echo " + https://www.cluecon.com                       +"
	@echo " +                                               +"
	@echo " +-----------------------------------------------+"
	@cat $(switch_srcdir)/cluecon2.tmpl

install:
	@echo " +---------- FreeSWITCH install Complete ----------+"
	@echo " + FreeSWITCH has been successfully installed.     +"
	@echo " +                                                 +"
	@echo " +       Install sounds:                           +"
	@echo " +       (uhd-sounds includes hd-sounds, sounds)   +"
	@echo " +       (hd-sounds includes sounds)               +"
	@echo " +       ------------------------------------      +"
	@echo " +               $(MK) cd-sounds-install           +"
	@echo " +               $(MK) cd-moh-install              +"
	@echo " +                                                 +"
	@echo " +               $(MK) uhd-sounds-install          +"
	@echo " +               $(MK) uhd-moh-install             +"
	@echo " +                                                 +"
	@echo " +               $(MK) hd-sounds-install           +"
	@echo " +               $(MK) hd-moh-install              +"
	@echo " +                                                 +"
	@echo " +               $(MK) sounds-install              +"
	@echo " +               $(MK) moh-install                 +"
	@echo " +                                                 +"
	@echo " +       Install non english sounds:               +"
	@echo " +       replace XX with language                  +"
	@echo " +       (ru : Russian)                            +"
	@echo " +       (fr : French)                             +"
	@echo " +       ------------------------------------      +"
	@echo " +               $(MK) cd-sounds-XX-install        +"
	@echo " +               $(MK) uhd-sounds-XX-install       +"
	@echo " +               $(MK) hd-sounds-XX-install        +"
	@echo " +               $(MK) sounds-XX-install           +"
	@echo " +                                                 +"
	@echo " +       Upgrade to latest:                        +"
	@echo " +       ----------------------------------        +"
	@echo " +               $(MK) current                     +"
	@echo " +                                                 +"
	@echo " +       Rebuild all:                              +"
	@echo " +       ----------------------------------        +"
	@echo " +               $(MK) sure                        +"
	@echo " +                                                 +"
	@echo " +       Install/Re-install default config:        +"
	@echo " +       ----------------------------------        +"
	@echo " +               $(MK) samples                     +"
	@echo " +                                                 +"
	@echo " +                                                 +"
	@echo " +       Additional resources:                     +"
	@echo " +       ----------------------------------        +"
	@echo " +       https://www.freeswitch.org                +"
	@echo " +       https://freeswitch.org/confluence         +"
	@echo " +       https://freeswitch.org/jira               +"
	@echo " +       http://lists.freeswitch.org               +"
	@echo " +                                                 +"
	@echo " +       irc.freenode.net / #freeswitch            +"
	@echo " +                                                 +"
	@echo " +       Register For ClueCon:                     +"
	@echo " +       ----------------------------------        +"
	@echo " +       https://www.cluecon.com                   +"
	@echo " +                                                 +"
	@echo " +-------------------------------------------------+"
	@cat $(switch_srcdir)/cluecon2.tmpl
	@sh $(switch_srcdir)/build/modcheck.sh $(DESTDIR)$(modulesdir)

.PHONY: check dvi html info install-data \
        install-dvi install-exec install-html install-info install-pdf install-ps installcheck installdirs pdf \
        ps uninstall mostlyclean clean distclean maintainer-clean

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

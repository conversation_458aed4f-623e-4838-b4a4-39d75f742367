<?xml version="1.0" encoding="utf-8"?>
<!--
	美国 Texas 落地 +13256663651
-->
<include>
    <extension name="Texas">
        <condition field="destination_number" expression="^0(1)(210|214|254|281|325|346|361|409|430|432|469|512|682|713|726|737|806|817|830|832|903|915|936|940|945|956|972|979)(\d+)$">
            <action application="set" data="effective_caller_id_number=+15513158120"/>  <!--(a Twilio number) -->
            <action application="set" data="effective_caller_id_name=+15513158120"/>  <!--(a Twilio number) -->
            <action application="set" data="destination_number_country=$1"/>  <!--取国家号位 -->
            <action application="set" data="destination_number_region=$2"/> <!--取地区号位 -->
            <action application="set" data="destination_number_code=$3"/> <!--取号码位 -->
            <action application="set" data="destination_number=$1$2$3"/> <!--设置被叫 -->
            <action application="set" data="landline_calling=1"/><!--对应落地呼叫时支持RTP混淆 -->
            <action application="set" data="execute_on_answer=sched_hangup +360 alloted_timeout" />
            <action application="lua" data="akcs_phonecall_telnyx.lua"/>
        </condition>
    </extension>
</include>

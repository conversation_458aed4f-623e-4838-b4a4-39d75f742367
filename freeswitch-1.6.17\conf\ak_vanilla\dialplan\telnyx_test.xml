<?xml version="1.0" encoding="utf-8"?>
<!--
    telnyx线程测试，呼叫到twilio的账号到test84
-->
<include>
    <extension name="TelnyxTest">
        <condition field="destination_number" expression="015016510678">
            <action application="set" data="effective_caller_id_number=+15513158120"/>  <!-- telnyx线路号码-->
            <action application="set" data="effective_caller_id_name=+15513158120"/>  
            <action application="set" data="destination_number=15016510678"/> <!-- twilio号码-->
            <action application="set" data="landline_calling=1"/> 
            <action application="set" data="execute_on_answer=sched_hangup +360 alloted_timeout" />
            <action application="lua" data="akcs_phonecall_telnyx.lua"/>
        </condition>
    </extension>
</include>

<?xml version="1.0" encoding="utf-8"?>
<!--
    canada落地  +19027019367
-->
<include>
	<extension name="canada">
        <condition field="destination_number" expression="^0(1)(647|226|437|548|742|289|365|683|753|249|343|780|403|250|604|807|519|204|506|709|867|902|705|613|416|905|514|450|418|819|306|867|581|367)(\d+)$"> 
            <action application="set" data="effective_caller_id_number=+19027019367"/>  <!--(a Twilio number) -->
            <action application="set" data="effective_caller_id_name=+19027019367"/>  <!--(a Twilio number) -->
            <action application="set" data="destination_number_country=$1"/>  <!--取国家号位 -->
            <action application="set" data="destination_number_region=$2"/> <!--取地区号位 -->
            <action application="set" data="destination_number_code=$3"/> <!--取号码位 -->
            <action application="set" data="destination_number=$1$2$3"/> <!--设置被叫 -->
            <action application="set" data="landline_calling=1"/><!--对应落地呼叫时支持RTP混淆 -->
            <action application="set" data="execute_on_answer=sched_hangup +360 alloted_timeout" />
            <action application="lua" data="akcs_phonecall.lua"/>
        </condition>
    </extension>
</include>

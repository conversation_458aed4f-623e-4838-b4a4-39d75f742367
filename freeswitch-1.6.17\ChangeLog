freeswitch (1.2.7)
	change fs_encode to support raw files to wav or to other raw
	check for vm caller id info later so if its transfered it gets the updated details
freeswitch (1.2.6)
	core_sqldb: sql tuning by changing 2 Indices
	freeswitch-core: database corruption handling
	freeswitch-core: use system time as reference for FS uptime, instead of monotonic
	freeswitch-core: hide sensitive dtmfs
	freeswitch-core: record g729 calls to g729 wav files, playback g729 wav files
	freeswitch-core: STUN: XOR_MAPPED_ADDRESS attribute not handled
	freeswitch-core: option to record session when bridged only 
	mod_callcenter: simple periodic announcement
	mod_callcenter: New members joining queue don't ring agents when max-wait-time-with-no-agent-time-reached is 0
	mod_curl: add curl content-type option
	mod_curl: adding ability to transmit a file using CURL from API or dialplan app
	mod_directory: option to search by first and last name
	mod_dptools: add option to allow any key to terminate playback or record
	mod_event_socket: enable dual-stack (IPv4/IPv6) listening in mod_event_socket on Windows
	mod_lcr: Add 'round-robin' feature to carrer gateways
	mod_local_stream: add an event (or filter) for mod_localstream PLAYBACK_START and PLAYBACK_STOP
	mod_nibblebill: fix mod_nibblebill to use pgsql in core
	mod_sofia: pass User-to-User header to variable-space
	mod_sofia: option for freeswitch to trust users authed by proxy
	mod_spandsp: Spandsp directory for making modem links
	mod_voicemail: added voicemail call paging
	build: building works on Visual Studio 2012
	config: updating mod_cidlookup default configuration to use OpenCNAM as an example 
freeswitch (1.2.5)
	mod_lua: Enable mod_lua to use native pgsql dbh support  (r:2cea7f0f)
	mod_sofia: Add att_xfer_destination_number variable to indicate the original destination number of the attended transfer leg on REFER for semi-attended transfer scenarios.  (r:893cd7be)
	sounds: Bump Callie sounds ver to 1.0.22  (r:41e00c78)
freeswitch (1.2.4)
	core: Add Postgres core db support (r:0c1180d5)
	mod_cdr_mongodb: update MongoDB driver to v0.6  (r:10093b44)
	mod_dingaling: do lookup in dingaling when an address is specified as host:foo.bar.com like sofia does  (r:fbfe830a)
freeswitch (1.2.3)
	core: add hold_events variable with start and stop times for each hold (r:9a193a9c)
	core: update json lib in core and ESL and re-apply old patches (r:5a956890)
	core: add skip_cdr_causes variable to list call hangup causes that should not trigger cdr processing (r:3cf238fc)
	formats: add mod_mp4v2 for mp4 recording, most things are hardcodec, but it should work with 8000hz audio and H264 video (r:7e01bd10)
	freetdm: M2UA Signaling gateway support (SIGTRAN)
        freetdm: New TDM endpoint for raw IO access to boards (controllable channel)
        freetdm: New dialplan paramters and SIP X headers to set ISUP message information elements
        freetdm: Misc ISUP bug fixes
	libsofia: Fix DoS vulnerability in processing Route Header (r:016550f2/FS-4627)
	libtpl: add tpl to tree (r:4985a41f)
	mod_sofia: add transfer_to variable for call processing (r:1b2b4565)

freeswitch (1.2.2)
	configuration: Add language config files for es-ES,es-MX,pt-BR,pt-PT (Thanks Francois Delawarde) (r:9cde99b4/FS-3003)
	core: move recovery engine up into the core (r:66677c94)
	core: add ignore_early_media=consume to ivr_originate (r:13dab11c)
	core: add timestamps for on and off hold times to put in xml cdrs (r:b0e49d3e)
	libtiff: update to 4.0.2 (r:a2b5af56)
	mod_commands: add uuid_answer and uuid_pre_answer (r:ce88d572)
	mod_commands: add uuid_early_ok (r:35f0e2ff)
	mod_commands: add uuid_media_reneg api command to tell a channel to send a re-invite with optional list of new codecs (r:bfc46567)
	mod_dptools: mutex app (r:212953bc)
	mod_httapi: add cache param (r:a8b89bcc)
	mod_sofia: change mod_sofia to use new core based recovery engine (r:2a8841ab)
	mod_sofia: fire-message-events profile param (r:9c06cb34)
	mod_sofia: add send-display-update profile param to disable the update method (r:8f0c726b)
	mod_spidermonkey: add email function to js (r:37b36aea)

freeswitch (1.2.1)
	libapr: Updating in tree apr for 1.4.6 (r:4029614e)
	libwebsockets: Initial commit (r:6fa2fd36)
	mod_sofia: add rtp endpoint contributed by sangoma (r:ef5c1256)
	mod_spidermonkey: add javascript chatplan app (r:61839229)

freeswitch (1.2.0)
	build: Automated builds for CI testing/Jenkins  (r:2aff535a)
	build: add debian source package building script  (r:c164aae9)
	build: add convenience script to set version in configure.in  (r:efc6f16c)
	config: add screen_confirm macro to lang/en/ivr/sounds.xml  (r:9ea3ce66)
	config: support for --with-logfilesdir, --with-dbdir, --with-htdocsdir, --with-soundsdir, --with-grammardir, --with-scriptdir and --with-recordin
	config: Redo config files, example - mv conf/* -> conf/vanilla/*  (r:ee71daa1)
	config: clean up switch.conf and add all the missing options needs more docs  (r:65fc2f8a)
	config: add cdr_csv template for OpenCDRRate  (r:ca60afaa)
	config: add conference_flags to config; mention 'audio-always' option - FreeSWITCH Jira
	gsdir (r:7ae3f5b7/FS-302)
	core: Add RECORD_STEREO_SWAP to reverse the record channels (r:d5042f2c/FS-3069)
	core: api_on_startup (r:e164b76c)
	core: allow dmachine and input callback to co-exist  (r:e185ff00)
	core: add support for configurable timeout and passing of args to play_and_detect_speech  (r:410e523c)
	core: partial with renaming  (r:24288832/FS-2216)
	core: only flush on break when its a blocking situation  (r:3a076786)
	core: In tone_detect all specs work, not just the first one detected  (r:d5ad8670/FS-4023)
	core: add origination_nested_vars=true to allow vars within vars in originate strings e.g. [originate {origination_nested_vars=true,TEST=,var=,recur=W00t}user/1004 3000]  will end up as w00t  (r:385a92ce)
	core: add core-db-pre-trans-execute and core-db-post-trans-execute to switch.conf.xml to wrap sql stmts around the core transactions  (r:21b1ffbf)
	core: abstract out originate_signal_bond to a function to avoid confustion and regressions  (r:8bb55ed4)
	core: update stun to more modern spec  (r:c3094046)
	core: add initial-event-threads to switch.conf.xml  (r:7ec8fb43)
	core: add enable-use-system-time param to switch.conf.xml (r:b1ae9746)
	core: add execute_on_post_originate and api_on_post_originate to run on chosen newly originated channels vs execute_on_originate which runs on all candidates  (r:bf20f524)
	core: have sql thread manually subscribe to each event it cares about instead of every event and filtering it  (r:6ea4c42c)
	core: Add FreeSWITCH-Version string and Uptime-msec (uptime in *milliseconds*) to heartbeat events  (r:a8efae99)
	core: add core_uuid global variable to expose the runtime uuid  (r:1d5b5f21)
	core: add core-uuid attr to xml cdr tag  (r:1dd4bd49)
	libesl: shutdown socket before closing to avoid blocking  (r:c41a16d4)
	libesl: add wait handler to forking code in ivrd  (r:6c406aa0)
	libzrtp: FreeSWITCH is now the official home of ZRTP code
	fs_cli: set session loglevel as well in fs_cli when doing 'console loglevel info' also now implies '/log info' locally  (r:b80a3a34)
	mod_commands: Add "file_exists" API (r:7eba3f2b/FS-3927)
	mod_commands: add fsctl debug sql  (r:d655ceec)
	mod_commands: show current process stack size from status command where supported  (r:d32a72bc)
	mod_commands: add fsctl sql start/stop for standby controls  (r:46fee25d)
	mod_commands: add api-expansion to switch.conf.xml and fsctl api_expansion command to control allowing apis to be expanded via variable expansion  (r:293429f7)
	mod_conference: Add auto-record filename to conference_recording channel variable and ability to pull filename from API (r:b9295fd5/FS-1573)
	mod_conference: Convenience feature in mod_conference: apply sub cmds to non_moderator members (r:04295ac9/FS-3249)
	mod_conference: New conference commands: get and set (r:ab5f3f28/FS-3254)
	mod_conference: Mirrored video when alone in conference (r:086cbf1b/FS-4176)
	mod_conference: add outcall_flags  (r:090345de)
	mod_conference: add flags to conference xml_list and show also on conference list  (r:8d6b64e0)
	mod_commands: allow system API command to capture output from the executed command  (r:d7a37e97)
	mod_dingaling: enable srtp for dingaling  (r:68d9a83e)
	mod_dptools: add video_refresh dp and uuid_video_refresh fsapi  (r:99bac0d0)
	mod_dptools: allow silence for moh type on campon  (r:13068f17)
	mod_erlang_event: accept binary in api and bgapi  (r:f55f15c8)
	mod_event_socket: add uuid to event socket apps  (r:3708c962)
	mod_fax: Deprecated and now removed from tree
	mod_httapi:  mod_httapi.c -- HT-TAPI Hypertext Telephony API (Twillio FreeSWITCH style)  (r:bc8cbee1)
	mod_isac: add mod_iSAC (extracted from webRTC) 16hz @30ms,60ms 32khz @30ms  (r:a0473cda)
	mod_loopback: allow mod loopback bowout to reach out across stacked loopback bridges and remove all of them  (r:ad7149bf)
	mod_posix_timer: New timer module mod_posix_timer (r:9d7e9e67/FS-3731)
	mod_say_fa: Initial commit of Farsi say module  (r:18ee7ce3)
	mod_sofia: Add presence-privacy parameter to exclude extension numbers from the distributed presence string (r:c6633fa3/FS-849)
	mod_sofia: Reverse data in dialog-info so the proto is in the params not in the user because polycom uses the target uri for what to dial and stips the params  (r:cdb4b29a)
	mod_sofia: add registration-thread-frequency param  (r:5b7e2013)
	mod_sofia: add support for yealink display update  (r:39c4e7a3)
	mod_sofia: added a new param to the gateway config called options_user_agent to set something specific  (r:f25c5aaf/FS-3842)
	mod_sofia: produce sip_full_via var  (r:23dcdbd8)
	mod_sofia: add presence-disable-early sofia option to send non-specifc presence messages w/o special case for early  (r:9b023152)
	mod_sofia: add sdp_secure_savp_only channel variable for silly asterisk srtp that only has the SAVP or cries  (r:50727f56)
	mod_sofia: allow publish with no contact  (r:b843911c)
	mod_sofia: add some defensive code to allow support for yealink in SCA mode even when its broken  (r:17cb6a22)
	mod_sofia: add user_via to ack in case with track calls  (r:f558247d)
	mod_sofia: one msg thread per cpu by default  (r:6f6765b8)
	mod_sofia: mirror back record-route header in options  (r:ef9dfe42)
	mod_sofia: add sip_require_timer=true variable to enable require timer on session refresh that breaks finicky endpoints  (r:b553d62f)
	mod_sofia: Channel var now takes precedence over the profile setting  (r:ca39f15a)
	mod_sofia: re-implement sla barge using eavesdrop backend  (r:a511ff30)
	mod_sofia: add inbound-reg-in-new-thread sofia param to launch a new thread to process each new inbound register when using heavier backends  (r:0a5a057c)
	mod_sofia: add mwi-use-reg-callid  (r:792b004f)
	mod_sofia: block any inbound messages when queue is full; add debounce for mwi and pres on register; fix missing detach attr on new mode to process reg in new thread  (r:fb790bc3)
	mod_sofia: add metadata col to internal registrations table  (r:192030c5)
	mod_sofia: allow ep_codec_string to draw from absolute_codec_string before the profile prefs  (r:f685e4c5)
	mod_sofia: put presence data in state events  (r:75aab0ee)
	mod_sofia: add sip_recovery_break_rfc variable to set globally or per channel to not reverse the from and 2 on uas re-invites  (r:5f09b403)
	mod_sofia: add tags to allow crypto in avp  (r:9fe08675)
	mod_spandsp:  Add T31 modem support to mod_spandsp - similar to iaxmodem only wired into FS.  Also merge configs into single spandsp.conf.xml --see in tree example--  (r:d91f67d0)
	mod_spandsp: add v18_mode var  (r:e941a61f)
	mod_valet_parking: add orbit feature to valet_parking similar to mod_fifo (r:09725e2b)
	mod_vlc: Adding mod_vlc initial working version  (r:77f3bd24)
	mod_voicemail: vm_read should mark messages as read, but not saved  (r:693f2986)
	mod_voicemail: Change vm_announce_cid to be true/false and utilize vm_play_phone_number_macro  (r:18289fa0)
	mod_voicemail: fire event with result of vm auth attempt  (r:0a6dde7b)
	mod_voicemail: Improved feedback to user when chaning vm passwords.  (r:7b54701d)
	mod_xml_scgi: add mod_xml_scgi (like xml_curl minus the web server direct to an application backend that supports the SCGI protocol)  (r:d7cf0bbe)

freeswitch (1.0.7)

	build: Add mod_silk to windows build
	build: fix mod_json_cdr build in windows (MODEVENT-63)
	build: fix build error due to missing zlib linking when using libtool 2.2 or later
	build: Enable mod_curl in windows build
	build: apply fix for MODSOFIA-71 to windows build
	build: Add more excludes to .gitignore (for Windows) (r:b6628d26/FSBUILD-269)
	build: Bump version of en-us-callie sounds to 1.0.13 (r:ca0a69a3)
	build: change build to use mod_spandsp instead of mod_fax and mod_voipcodecs (r:988147a7)
	build: add mod_spandsp to windows build (r:4fa8be62)
	build: merge -j option for bootstrap (r:abb7d2e5/FSBUILD-237)
	build: dont fail on bootstrap due to missing libs (r:ff960d78)
	build: numerous tweaks to allow building on VS2010
	build: Fix build with --with-curl (r:e704f021/FSBUILD-285)
	build: VS 2010 - Change to V4 framework, add SWIG v2.0 files to fix release build exceptions(temp fix till we upgrade all SWIG files) (r:812f4309)
	build: Windows VS2010 build - remove strange characters (r:ba1546e0/FSBUILD-297)
	build: Make bootstrap.sh Bourne shell compatible (r:8dbd62ff/FSBUILD-301)
	build: add mod_osp Makefile to configure generated Makefiles (r:dc06a039/FS-122)
	build: Remove mod_spidermonkey from windows 2008 x64 builds - does not work (r:280e894d)
	build: fix warnings on windows x64 builds src and mods projects - only libsofia included on the libs side (r:45ecbc2f)
	build: Patch debian init.d script to set ulimit values (r:0eb33e57/FS-2844)
	build: add plc to core (r:b7c80a84)
	build: VS2010 libportaudio project improvements for DirectX builds and switch to build DirectX by default (r:e9e33f51/FS-3033)
	build: add make targets for mod_com_g729 mod_com_g729-activate mod_com_g729-install mod_com_g729-clean mod_com_g729-uninstall (r:17d52112)
	build: add support for bz2 to getlibs (r:b61fc396)
	build: Bump callie sounds to 1.0.15 (r:c8eaef60)
	build: always use our includes first so we use our srcdir headers over installed versions (r:15c79424)
	build:  pocketsphinx build for 0.7 windows vs2008 (r:a7613c06/FS-3348)
	build: They no longer ship the wsj model in pocketsphinx... and seems the dictionary has moved a bit. (r:********)
	build: unimrcp vs2010 build fixes for new version (r:2dcca5f4)
	build: add sqlite to clean on make current or update-clean (r:2366f429)
	build: Update windows to use Lame 3.98.4 (r:4349ec00)
	build: fix mod_silk build issue (r:6fe6d8d7/FS-3649)
	build: Rename AUTOMAKE_OPTS to AUTOMAKE_OPTIONS in several places (r:992eafd0)
	build: don't allow using system libcurl on systems that strip out Curl_setopt, we use it (r:36f0a5b8/FS-2936)
	codec2: working prototype, still for testing only (r:04ca0751)
	config: move limit.conf to db.conf
	config: Update VM phrase macros to voice option then action on main, config menus
	config: Remove 99xx extension numbers to avoid dp conflicts (r:0c9bb174/DP-17)
	config: update config example for caller-id-type (r:8f03a7cd)
	config: default to 48k since most sound cards can do that (r:170404a4)
	config: Create RFC2822_DATE for use in emails. Some clients fail to sort emails properly without a Date: line. (r:a1f19d91)
	config: move enum to the bottom of default. (r:4d448c97)
	config: Add att_xfer example to default dialplan (r:20ec962a)
	config: default example to resolve some issues with SCA in cases where host and ip are mixed causing the phone to be confused. (r:0279261b)
	config: Fix phrase files, still missing a sound file (r:6741f350/FS-2742)
	config: Disallow global-intercept and group-intercept can intercept an outbound call in default dialplan (r:890871ba/FS-2777)
	config: fix single domain assumption in default config to be more portable *cough* bkw *cough* (r:f987903e)
	config: Bump Doxygen.conf version to 1.0.6... belatedly (r:cfeae1ba)
	config: docs for acl (r:57b410eb)
	config: add default to conf to demonstrate min-idle-cpu (r:b8b7266a)
	config: change min/max enforcements to >= instead of > (r:0d5fcf65)
	config: Add README_IMPORTANT.txt to default configuration (r:6cd5ce72)
	config: Talking clock dialplan example (Thanks AviMarcus) (r:ffb4a3ae)
	config: fix talking clock regexes (need ^ and $ so they don't match only 917x) (r:8529ba33)
	config: Update phrase_en.xml to reflect 1.0.16 sounds version (r:7499dfb2)
	config: bump en sounds version to 1.0.16 (r:50ce2cae)
	config: Add ivr/ subdir to conf/lang/en/en.xml (r:42f10a48)
	config: Fix mod_directory phrase file references to 'dir-press.wav' (correct: vm-press) (r:3ef2692f)
	config: bump ru sounds version to 1.0.13 (r:2b1b19bf)
	config: Fix eavesdrop so that *0 works as well as 88 as the access code (r:cbfe83cc)
	config: change default to short date-time in VM sounds (r:3603a69d)
	core: Add RTCP support (FSRTP-14)
	core: handle some errors on missing db handle conditions
	core: add ... and shutdown as a fail-safe when no modules are loaded
	core: add apr func to disable loopback on multicast to simplify mod_multicast_event
	core: free mem on shutdown (thanks Jalsot) (JANITOR-4)
	core: try harder to get db handle in sql thread and fail out if it can't get one
	core: always export 'export_vars'
	core: add sanity check to launch threads that catch hangup and are not in a thread to make sure they clean up
	core: Tweak bridge_early_media to support passthrough codecs
	core: cleanup C reserved identifier violation (JANITOR-3)
	core: add sound_prefix support in uuid_displace (FSCORE-550)
	core: add 'critical' param on modules.conf to abort on mod load failure from Moc
	core: add 'direction' chan var
	core: fix race condition where 'delete' would beat 'insert' in the reporting db (FSCORE-597)
	core: add callee id to events and add global param verbose-channel-events and fsctl verbose_events [on|off] to globally configure verbose events
	core: fix potential excess cpu usage during originate
	core: fix switch_get_addr to work with v6 properly
	core: fix ZRTP compile issue (MODENDP-305)
	core: fix Hangup on bridge event using switch_caller_profile_dup causes crash (FSCORE-601)
	core: fix race condition when hangup happens after answer indication but before the session thread is started
	core: fix switch_core_sqldb: library routine called out of sequence (sqlite)
	core: attempt to address exotic scenario:  FS rewriting SDP when reattempting to bridge a call to second route in proxy_media=true
	core: reset codecs after media bugs
	core: add intercept_unbridged_only=true to only intercept if the channel is not bridged to anyone
	core: Fix switch_url_encode() does not handle properly UTF-8 sequences (FSCORE-605)
	core: add bind method to EventConsumer takes same args as constructor to bind more events to an existing consumer
	core: Fix parsing empty XML files (FSCORE-608)
	core: Initialize when no console (Windows) (r:909ad642/FSCORE-610)
	core: (Win) bridge fails because session read lock failure (r:f8f91362/FSCORE-606)
	core: Add option to hangup channel if record fails (r:a3e6bead/FSBUILD-591)
	core: Crash when using tab completion on uuid_ commands (r:9637b89e/FSCORE-613)
	core: fix uuid_media state change (r:2cc59f1e/FSCORE-615)
	core: add new callstate field to channels table (r:0f133eae)
	core: fix leg_timeout issue (r:3fbd9e21/MODAPP-433)
	core: Add alternate timing method support for Windows XP and 2003 - must be conditionally compiled(default is original timing) (r:30d2e7fd/FSCORE-626)
	core: Fix fail to parse variable absolute_codec_string when inside [] (r:bfe31288/FSCORE-631)
	core: Fix limit not decrementing properly (r:f10eebf8/FSCORE-632)
	core: Fix crash when receiving RTP packet w/ invalid length (r:26358d67/FSCORE-635)
	core: Fix incorrect variable assignment in switch_channel_set_timestamp (r:8a7f38c6/FSCORE-636)
	core: add api_reporting_hook (like api_hangup_hook but after reporting state) both honor session_in_hangup_hook (r:ed7ccc14)
	core: only let force_transfer_* vars work when an explicit value was not supplied (r:91a87e9d)
	core: Make switch_ivr_session_echo stop when CF_BREAK is set (r:2b120311/DP-19)
	core: change channel app_flags to be realm specific and default old version to use __FILE__ as the realm name to avoid cross fire between apps using app flags (r:09c1815c)
	core: preanswer before getting variables to avoid crash (r:25fe16df)
	core: Windows: Don't report "unknown command" on console when empty command has been given (r:c8f9fb56/FSCORE-641)
	core: Windows: Add start parameter -monotonic-clock, replaces build flag WIN32_MONOTONIC (r:3515c7a0/FSCORE-643)
	core: Improve RTP timing on playback of files (r:d6d7773c/FSCORE-639)
	core: Allows bind_meta_app to use chars other than * (r:fd254766/FSCORE-630)
	core: Fixed core lib won't build for win32 (r:9327c994/FSCORE-646)
	core: add last_bridge_to var to keep uuid of last bridged channel and fix race in show calls on hangup of bypass_media channels (r:77e2dccf)
	core: Phrase "speak-text" application returns on first key press in phrase file on Windows (r:6d74d7ab/MODAPP-448)
	core: pass originate flags into session_request so we can selectivly skip throttling (r:46c6650a)
	core: Implemented 'Block Fork' and removed possibility for "-nc -nf" potential issue. (r:f26a6972/FSCORE-652)
	core: Add console callback for listing loaded/available modules for load/unload/reload commands (r:d68a1218/FSCORE-662)
	core: strip trailing and leading whitespace in api execute args and commands (r:ca481842)
	core: Fix SQLLEN to prevent queue buffer overrun (r:68d1c32a/FS-2149)
	core: add origination_caller_profile to log all attempted calls for a paticular leg (r:977a8ad7)
	core: Add attribute "path" to autoload_configs/modules.conf.xml <load> entry. (r:1a75821d)
	core: add tone2wav (r:6f2c455f)
	core: add speed boost to sql thread (r:ef79535c)
	core: reverse the linked list in ivr menus to support accidental feature of multiple entries for the same keys (r:d4a01324)
	core: Add time of day string compare function switch_tod_cmp.  It usable in XML dialplan with time-of-day.  String format is hh:mm:ss you can define a range like this : 09:00-17:00  (Second are not optional) (r:4ab8fa13)
	core: Add date time range string compare function switch_fulldate_cmp. It usable in XML dialplan with date-time. String format example : 2009-10-10 14:33:22~2009-11-10 17:32:31. (r:c9fcce08)
	core: Add day of week 3 letter initial usage in "wday" field in the dialplan.  Example : mon-fri.  Using number as before is still supported.  Several public switch function are available. (r:59ec8ced)
	core: set conditionals to only fire when the mutex can be obtained (r:07ec7867)
	core: avoid segfault when sofia tries to update the callee id at the same time as the outbound call is transferred (r:df63657e)
	core: make code more automagic to shut up the dude on the list (r:d093a4a4)
	core: Fix memory leak if we fail to enqueue new event to EVENT_QUEUE in switch_event.c (r:ef773e07/FS-2148)
	core: fix endless loop on startup when specifying -nosql (r:b6a533ee)
	core: Buffer for url encode in switch_ivr_set_xml_chan_vars() too small by 1 (r:0cc28f37/FS-2167)
	core: fix switch_ivr_collect_digits_callback to allow an args pointer with null callback to work like other apis (r:89d99a91)
	core: ERROR_PARTIAL and BAD_PARTIAL are regarded as PARTIAL in switch_regex_match_partial (r:b4548a60/FS-2238)
	core: sprinkle digit_timeout into switch_ivr_read and switch_ivr_play_and_get_digits and the higher level variants (r:cfa30468)
	core: Fix parse of variable absolute_codec_string when inside [] (r:54bf6575/FS-2126)
	core: Fix SWITCH_IO_FLAG_NOBLOCK needed for mod_sangoma_codec (r:bc304153/FS-2017)
	core: fix coredump in rtcp socket handling (r:6c1070ea/FS-2009)
	core: add bitrate patch from moc with some extra stuff for late neg mode (r:633f193d)
	core: refactor fmtp parser as a core func (r:56f8c11f)
	core: add switch_ivr_dmachine async digit parser to core (r:7f3319dc)
	core: make parens optional on functions as vars ${foo(bar)} is now the same as ${foo bar} (r:fdba0e07)
	core: fix default ptime for iLBC and make new configurable global map in switch.conf.xml (r:e3c427ad)
	core: only pause recording media bugs in fifo (r:c1d41dd9)
	core: Allow IPv6 in proxy mode (r:4e5911c2/FS-2776)
	core: "Silent recovery" (r:93c2ed94)
	core: fix switch_find_local_ip to properly handle force_local_ip_v4 and _v6 (r:f42c9036/FS-2778)
	core: fix att_xfer/3-way scenario (r:0559cc50)
	core: dmachine - timeout instantly when you have exact match and are equal to max digits (r:81a9f8ef)
	core: Core ODBC support for transactions with MSSQL (r:d2ca8d40/FS-2050)
	core: Show the UUID of the session in the log when sending RFC2833 DTMF packet and when receiving a RTP DTMF (r:9241a35e)
	core: add record_post_process_exec_app and record_post_process_exec_api both can have <app|cmd>:<args> (r:07adca56)
	core:  add -ncwait for Unix based implememtations to make the backgrounder wait to make sure the process starts before detaching (r:23d5fc19)
	core: fire DTMF event when converting dtmf to inband with start_dtmf_generate (r:dcdd3811)
	core: fail calls with uninitialized frame (r:7dafe4e2)
	core: allow switch_process_import to specify a prefix (r:e0c37c1f)
	core: add additional info when fail (r:4bbd9a4c/FS-2825)
	core: avoid hypothetical problem with flushing queue with delayed events (r:49b6237e)
	core: add send_silence_when_idle and dmachine honoring to park loop (r:3be3cd76)
	core: ivr_enterprise_originate: Fix export of variable from the originator channel to the called channels (r:025c82e7)
	core: Better handling of progress and answering to prevent sip profile from locking up at higher volumes (r:04e57577/FS-2801)
	core: ACL for IPv6 address and swigall to boot (r:db91f0e8/FS-2842)
	core: add intercept_unanswered_only var akin to intercept_unbridged_only (r:68f18efe)
	core: switch_odbc_handle_exec_string duplication SQLExecute (r:8b0e7d24/FS-2880)
	core: Fix timeout while bridge is waiting for CF_BRIDGED flag (r:2572621b/FS-2368)
	core: don't parse events for b legs from a leg thread in case they are using a monolothic python script as a group_confirm exec over socket to send it messages while the call is ringing (r:ed5266d3)
	core: add new function to check when messages need parsing to improve performance on parsing messages during originate (r:8b0421ff)
	core: run execute_on_answer on_media _on_ring apps async (r:ef4a4ed0)
	core: add switch_ivr_insert_file to insert one file into another at an arbitrary sample point (r:82394b37)
	core: Slow reload cause calls to hang (r:1ba98b02/FS-2852)
	core: Application intercept causes FS to stop processing calls (r:12fc65f7/FS-2872)
	core: fix edge cases for endless loop in sql thread (r:5d7c09ed)
	core: prevent race while changing codecs mid call (r:7aa72b67)
	core: Fix crash in ODBC when SQL Server kills TCP connection (r:5aba96e3/FS-2910)
	core: Fix fallback to CORE_DB when MSSQL fails init (r:3406d05b)
	core: add new function to init an empty switch_sockaddr_t to avoid an unnecessary dns lookup on 0.0.0.0 (r:009c41d4)
	core: fix endless RTP loop in Windows (r:cb2d0736/FS-2924)
	core: play_and_get_digits should actually timeout, not last forever... (r:28cab5ed/FS-2923)
	core: Fix crash w/o core dump (r:00046ee0/FS-2933)
	core: normalize tests for outbound channels to use switch_channel_direction instead of testing for CF_OUTBOUND (r:93cc3dc5)
	core: add CF_DIALPLAN (r:3ff07445)
	core: tweak on calle[re] id (r:9db4a826)
	core: cid logic changes for calle[re] (r:8f452bc5)
	core: change switch_strip_spaces to specify if you want it to dup the string or destroy the current buffer (r:4d7e4f1e)
	core: fix secondary issue with min_digits = 0 and terminator key pressed to cancel (r:fe005bdd/FS-2789)
	core: fix dtmf issue with jb on (r:90e58696)
	core: fix ignore_early_media=ring_ready (r:5b752c54)
	core: prevent race on execute_on_answer called from the B-leg of a call (r:751e0291)
	core: drop rtp frame that was already replaced with a cng frame (r:34a0ca50)
	core: fix partial match counting as exact match in dmachine (r:5eb951aa)
	core: try to adjust the timer to be ok with the horrible 10000 microsecond kernel resolution on amazon ec3 but that doesn't mean it's not horribly wrong to run the kernel that slow (r:903b2901)
	core: make exact matches return sooner in dmachine (r:e897646e)
	core: don't let inherit_codec work when we have ep_codec_string set and the B-leg codec is not in that list since it will lead to failure (r:f79f9766)
	core: set maximum query run time to 30 seconds at least on drivers that support SQL_ATTR_QUERY_TIMEOUT (r:5bb525e1)
	core: dd switch_cache_db_affected_rows() to switch_core_sqldb (and switch_odbc) and expose it through Lua dbh:affected_rows() (r:d79cf484/FS-2962)
	core: add bind meta on A-D and refactor (r:27869d7a)
	core: add temp_hold_music var that is only valid until you transfer the call and finishing touches on bind meta to A-D (r:b262f44c)
	core: add function to help set session read codec to slinear (r:1a08df9b)
	core: add rtp_bug IGNORE_DTMF_DURATION to speed up dtmf detection of RFC2833 on strange carriers (r:b3fc001e)
	core: Fix crash when re-connecting to non-working database server (r:29daaa07/FS-2960)
	core: treat EINTR returns as a BREAK (now mapped to SWITCH_STATUS_INTR), we appriciate the interrupted syscalls but we would like to continue working properly (r:316963c5)
	core: eat rtp frames with the wrong payload type number (r:fe1711fd)
	core: up assert vaule on header loop detection to 1 meeeeelyonne for hmmhesegs (r:d9c56345)
	core: Fix race condition in originate where USER_BUSY is reported as a no answer (r:cc06fdb5/FS-2992)
	core: Allow check ip change to be optional (r:1cf79386/FS-2917)
	core: handle 2833 in do_flush instead of dropping valid dtmf (r:3fa3e11c/FS-3002)
	core: add record_restart_time_limit_on_dtmf var (r:7a1dcb69)
	core: fix unreachable condition with a null args to make any key stop playback/record etc without dequing and remove hard-coded flush digits in play_and_get_digits be sure to flush it yourself before using (r:976859bb)
	core: Fix a lock on reloadxml when stderr write is blocked.  Also remove an error parsing print since reason generated were wrong and duplicate. (r:2d6161e8)
	core: fix || where it should be or in sql stmt that may cause stray records in the calls table
	core: Add CS_NONE and correct variable name (r:3fd7b8f2)
	core: Fix SQL issue (r:04bb74fc/FS-3050,FS-3051)
	core: fix race with media bug exiting from write frame while read frame is trying to use it (r:1341a75a)
	core: fix regression in rtp stack trying to avoid broken clients who send the wrong payload type, we were eating the stun packets in jingle calls (r:0bce777a)
	core: Add capability to specify core-db-name in switch.conf.xml to have sqlite in a different location.  This is important for everyone with relatively 'high' sip registration since the addition of sip registration to the core require sqlite db to be moved to a faster location (Ramdisk for example). Useful for everyone who moved their sqlite db for sofia to ramdisk because of performance issue. (r:500e9acd)
	core: Index column name wrong on table registrations.  (This wont create the index for people who already have the table) (r:1096e673)
	core: allow uuid bridge on unaswered channels as long as there is media available on at least one (r:4f93ea25)
	core: add multiple-registrations flag to the core similar to mod_sofia (r:b36a7c0b)
	core: tolerate dtmf where there is no start packet and only end packets (r:097caed4)
	core: Fix RTP auto flush during bridge w/Windows causing packet loss (r:f8d326de/FS-3057)
	core: possible ill placed assert() before a NULL check in soa_static.c (r:91a5e776/FS-2803)
	core: prevent crash on double call to asr_close (r:4f5ca9e8/FS-3077)
	core: fix bug in switch_itodtmf (r:b53a6848)
	core: use strdup instead of core_session_strdup in hangup hook (r:3a10d6a1)
	core: fix jb + no timer situations (r:61d3c56f)
	core: Add events PLAYBACK_START and PLAYBACK_STOP, plus some minor improvments for RECORD_STOP (r:bc397ab6/FS-2971)
	core: Fix event queue from needlessly filling up (r:2044a749/FS-3105)
	core: Fix issue that was preventing the sqlite handles from being recycled properly (r:11451c10/FS-3106)
	core: clear timestamp when generating a fake empty frame to fix edge-case sending the same timestamp over and over (r:08496cd7)
	core: wait for state change to avoid race (r:f33e9c6e/FS-2966)
	core: Fix freeswitch.session in Lua, etc. (r:0ba25358/FS-3119)
	core: try to reduce contention by not creating handles with the global mutex locked (r:b3a2fa1c)
	core: add limits to simo open sql handles (r:61cdf0da)
	core: Fix db locks affecting mod_callcenter (r:8da371c7/FS-3127)
	core: improve flow of dtmf through a bridge when timer is disabled (r:59da356d)
	core: Fix Freeswitch crash on Debian ARM (r:a80fae92/FS-3126)
	core: switch_xml: reloadxml will(should) never lock again.  It will load the XML structure into a new XML structure, and just replace the currently available ROOT XML.  It then the job of the last user of the switch_xml structure to free it. (r:471bd6df)
	core: switch_xml: Remove commented out mmap.  With the changes in the past 2 year, mmap can't really be put back in it current state. (r:34bd0e5e)
	core: Fix jitterbuffer with SRTP enabled (r:069f5f7d/FS-3075)
	core: this will remove the reported symptom but does not change the fact that 1khz resolution is ideal for proper performance (r:5f18ec94/FS-3168)
	core: this was specific to the user channel which is not a real channel in every sense of the word as it has no running thread or any usable state changes so this new line of code in 233d3164be4412aaaf8f9f42d8042e48279a018a to wait for the state machine to stabilize before returning from originate caused an issue with user/ channels (r:88a6ac2f/FS-3170)
	core: this also fixes the incorrect usage of L16 on payload 10 which may or may not break interop with other sip devices if we do it right.  also added rtp_disable_byteswap variable that can be set to false to disable byteswap when a device is encountered that is incompat (including all previous version of FS up till now) (r:e657e32f/FS-3172)
	core: dont calibrate clock when timerfd enabled (r:26f5ebd4)
	core: fix DTMF in SRTP/ZRTP (r:fd608901/FS-3165)
	core: add switch_atomic_* type and functions switch_apr.c and switch_apr.h (r:3b56c119/FS-3173)
	core: improve some defaults to tune performance if you use -heavy_timer, try not using it (r:5d783134)
	core: Fix api_hangup_hook with no args (r:484a397d/FS-3194)
	core: allow 100 microsecond tolerance on timer loop (r:6388e03d)
	core: Fix X-PREPROCESS exec to wait pid (r:dae2cb4a)
	core: Ability to use mod_say with native files; native is a special case so use the extension native e.g. en.native (r:3a2e1d03/FS-3176)
	core: Fix: Bridging a call to multiple legs and using leg_delay_start, legs that lost the race before the leg_delay_start time is up still get originated for a brief moment (r:c5daf80e/FS-3218)
	core: Have UPNP/PMP active without opening port mappings in the router/firewall (r:008f9889/FS-3208)
	core: add execute_on function so you can have execute_on_answer_1 execute_on_answer_2 execute_on_answer_3 etc (r:27c6d111)
	core: do this slightly safer so we don't have the mutex locked when we exec the app (r:ef175741)
	core: Fix argument parsing for tone_detect app (r:38c3a67a/FS-3229)
	core: add L16 def for 32ms and allow timer matrix to drop to 1ms to support nelly (r:82e3d49f)
	core: fix segfault in zrtp srtcp (r:2330b340)
	core: add switch_clean_name_string util function to strip out caller id name chars that can cause issues (r:244048f8)
	core: switch_core_sqldb - clear pointer on release (r:aaef33cc)
	core: all [] {} and <> can be stacked and override the delim per set <><^^:>{}{^^:}{^^;}[][^^:] (r:4c4bf59e/FS-3246)
	core: fix default tipping point it was too low (r:e4eade33)
	core: enable optimal defaults on linux kernels that can support newer features. (r:0b51aca3)
	core: Lower NAT port mapping disabled log msg from WARNING to INFO (r:973a850d)
	core: Change the structure of the phrases/language system. Previously it was fxml->phrases->macros->language->macro.  Changed it so fxml->languages->language->phrases->macros->macro You can have sub macros <macros name="voicemail"><macro ...> and allow you to call it login@voicemail. Change the sound-path to sound-prefix to make it constistant with the rest of freeswitch. Also allow to set a sound-prefix to a macros, so you can override it for a specific file set. You can set say-modules="en" or whatever in the <language section to define that say module to use. (r:4137b360)
	core: Fix edge case segfault on fifo member answer call (r:bf107c6f/FS-3269)
	core: Fix intercept application (r:f8835a81/FS-3271)
	core: add bridged timestamp and hangup_complete_with_xml=true to add xml_cdr to the body of hangup_complete events (r:bd471fc6)
	core: Modify freeswitch to use a configurable switchname instead of a hostname (r:00b53a91/FS-3277)
	core: add option to disable srtp with --disable-srtp (r:a6b336e4)
	core: record_session: Will auto create recursive destination folder if it doesn't already exist (Doesn't create folder when used with local cache feature) (r:c4b78a49)
	core: add largest_jb_size (r:1772fcb0)
	core: improve curl + openssl threading (r:7064487d/FS-2936)
	core: reset offset_pos on seek to 0 (r:e375d1d2)
	core: fix edge case between fail_on_single_reject and group_confirm (r:fae95433/FS-3303)
	core: add prefix chars to playback_terminators + means include the term in the string and x means include the char and return SWITCH_STATUS_RESTART eg #+* only includes the * if you type it but not the # (r:38b3f43d)
	core: add additional format YYYYMMDDHHMMSS to strepoch (r:38f06a3b)
	core: Fix APR EWOULDBLOCK issue (r:50e54364/FS-3308)
	core: fire SWITCH_EVENT_RECORD_STOP after closing file (r:94e9957e/FS-3311)
	core: add arrays to event headers and chanvars (r:c1c75952)
	core: allow -1 as silence generation divisor to specify only zeroes silence (r:294a57fb)
	core: Don't send silence frames for parked calls until media is ready. (r:dc028b36/FS-3046)
	core: add code to pass recording bugs on to other legs when executing an attended transfer, needs testing and possible follup commits before using (r:e2da3bea)
	core: flip_record_on_hold to make the recording flip to the other leg on hold, record_check_bridge to make recording the same file on the opposite leg of a bridge considered a duplicate attempt and record_toggle_on_repeat to make repeat recording the same file toggle the recording off (r:7bbbb9cc)
	core: lower log-level of failed ivr_originate for mundane conditions like no answer and attended transfer (r:f25085e0)
	core: add scoped channel variables (%[var=val,var2=val2] blocks valid in any app data field and will only last for that one app execution) (r:b2c3199f)
	core: enable recursion for scoped variables so applications that exec more apps will preserve the scope, the most recent app will mask variables just during the duration of that app (r:c6268da5)
	core: only clear scope vars when they were set (r:d4fcba74,r:77688084)
	core: Add the ability to issue a break to switch_ivr_sleep when media is not ready, allowing continuation of processing of the dialplan. (r:dfc30b2e/FS-3373)
	core: parse events and messages in channel_ready (r:94148095)
	core: add last_hold_time and hold_accum vars for cdr data (r:676ef808)
	core: avoid recursion loop in parse_all_events vs channel_ready (r:22d89943)
	core: auto populate global origination_caller_id_name/number from effective_caller_id_name/number in enterprise originate (r:f8c029a1)
	core: add --enable-timerfd-wrapper to wrap timefd syscalls for platforms with the right kernel and wrong libc (r:306b332d)
	core: don't parse events in channel_ready during hold (r:cad68d53)
	core: only parse messages from channel_ready when its a session calling channel ready on itself not when another thread calls it (r:1d12519d)
	core: Fix single quote stripping and add %y to turn ' into \'  (r:3b5a0ae5/FS-3359)
	core: push out signal data into its own queue system (r:f1ee225c)
	core: When in a dialplan hunt and we have a custom caller_profile, ${destination_number} and other variable kept the previous value of the original dialplan parsing.  This correct this so it take the custom created caller_profile for that hunt (r:b0e0dd22)
	core: pause traffic if sql_queue gets to big (r:2939262e)
	core: fix detection of tones in monitor_early_media_fail (r:3cbae3fb/FS-3413)
	core: use rwlock for global vars to reduce contention (r:0521886d)
	core: Fix separate_string_blank_delim to handle strings with '&' (r:f3a42258/FS-3099)
	core: Fix setting display on wrong channel on eavesdrop (r:3dc4b530)
	core: add new detailed_calls view a version of the channels table that shows only one legged calls or bridged calls (r:beecd937)
	core: display update on flip_cid (r:0fc8050c)
	core: make sql stmt more portable (r:6b948cf1)
	core: print ip:port on rtp bind err (r:11d2cd1b)
	core: display fixes and add 2 new cols to channels to store last sent display data (r:d364e9f2)
	core: sanitize outbound caller id number on one-legged calls (r:dee0f540/FS-3483)
	core: clean up originator/ee profile so the right one is prevelant in events (r:3e2c662a)
	core: check for answer flag in bridge to do display update properly (r:0f459d4b)
	core: add event subclasses in switch_event.c (r:3696ced7/FS-3497)
	core: add max_sessions to heartbeat event (r:9c8437a1/FS-3415)
	core: fix event firing for CHANNEL_PROGRESS_MEDIA event (r:e2a4fb11/FS-3396)
	core: add emulation for asterisk DIALSTATUS magic var (r:9d98d49f)
	core: the new code requires accurate timestamps, we were incrementing it by the interval (20) instead of the samples (160) (r:f10566af/FS-3181)
	core: pass cancel_cause into enterprise_originate (r:2e9724d2)
	core: add support for global namespace in chat interface to bind to unhandled messages (r:6dd1264d)
	core: resolve Syntax Error when using MSSQL in core (r:40990c04/FS-3527)
	core: add RTP_BUG_ACCEPT_ANY_PACKETS to disable dropping invalid packets for interop with Oracle CCA (r:aea22cd4)
	core: yield when doing b64 encode to avoid stealing the cpu on single proc crappy hardware (r:7d612da4)
	core: DTMF stands for Devil Took My Fone (r:0c066f06)
	core: add callee_id name/number to xml_cdr (r:bcd1e147)
	core: add new flag to frames to denote pass thru frames that are not audio (r:cb9abe02)
	core: change -hp to -rp, add -lp and -np, no priority flags means auto which will do -rp if you have > 1 cpu (r:c1dd008b)
	core: only reap sigchld when in fork mode for system (r:1e712c7e)
	core: Add application flag zombie_exec so registered applications can apply to be executed on channels that are already hungup, like the inline exec this is only limited to a small family of apps that do not use the channel for audio. (r:637a5ed8)
	core: do not escape strings inside single quotes unless we can find a closing quote too (r:b4b99c41)
	core: convert chat interface to use events instead of a bunch of args (r:9125a96c)
	core: make app to turn on new zombie exec instead of always doing it (r:3a2f8183)
	core: fix inaccurate sample count in file handle, buffered samples were being double tallied (r:5fe3a22d)
	core: prevent sql injection by using sqlite formatter on various code that generates sql stmts with switch_snprintf (r:256a6264)
	core: add ivr_menu_terminator variable you can set to none or the dtmf chars you want to terminate input (r:0a3e5d2f)
	core: delay_echo was double the length in milliseconds from what it should be (r:3317f5d3)
	core: fix issue where clearing a single realm does not completely clear (r:d2710422)
	core: add manual_rtp_bug gen_one_gen_all to prevent rtp passthru to break the *S* word.. (r:9e094835)
	core: Add transfer_history to logs (r:1bf97fa7)
	core: add fsctl sync_clock_when_idle so you can sync the clock but have it not do it till there are 0 calls (r:2094f2d3)
	core: add getGlobalVariable and setGlobalVariable to swig stuff (r:2faaee0e)
	core: Fix erroneous "module busy" messages on module unload (r:bad5964b/FS-3589)
	core: move code from uuid_kill into core (r:3c9551ee)
	core: Directed pickup sends "call completed elsewhere" (r:43ca3ee8/FS-3634)
	core: jitter-buffer tweaks (r:e3ade445/FS-3671 and r:599a4543/FS-3672)
	core: Add option to disable PLC when using the jitter buffer (r:ba14f95d/FS-3678)
	core: Allow fail_on_single_reject to do negated causes, i.e. fail_on_single_reject=!UNALLOCATED_NUMBER (r:522c0d53/FS-3675)
	core: move the thing that passes proto_specific_hangup_cause to the core and prefix it with last_bridge_ and also log last_bridge_hangup_cauuse for good measure (r:e04f9ba9)
	core: Add CHANNEL_EXECUTE to the list of default "verbose events" (r:ff7432d9/FS-3680)
	core: fix queued dtmf on channels with no timer issue (r:bc968ca8)
	core: use the non-signal checking version of switch_channel_up/down in the core (r:9ecf187d)
	core: Link curl into core (r:74ed2cef)
	docs: Major clean up of doxygen generated core API documentation (r:794246e1)
	docs: Add libteletone back to core API documentation (r:c35c138d)
	embedded languages: Provide core level support for conditional Set Global Variable (r:c017c24b/FSCORE-612)
	embedded languages: add insertFile front end to switch_ivr_insert_file and reswig (r:c4e350ab)
	flex client: check in basic flex demo as basis to develop a client application (r:25be760b)
	flex client: the hotkeys js is broken, get rid of it (r:2f6f71d4)
	fs_cli: block control-z from fs cli and print a warning how to exit properly (r:dc436b82)
	fs_cli: skip blocking writes on fs_cli to avoid backing up event socket (r:2ec2a9b0)
	fs_cli: let ctl-c work until you are connected (r:986f258d)
	fs_cli: add -i --interrupt to fs_cli to allow control-c to exit the program (r:e7b3c3b1)
	fs_cli: add timeout option to fs_cli (r:5fad26b4)
	fs_cli: implement CLI prompt redrawing (r:a79f1f42) (many other minor changes by TC on 2011-09-22)
	fs_cli: implement configurable prompt, input, and output coloring (r:c7ec19d6)
	fs_cli: allow ;; seperated commands in fs_cli -x (r:cbc92936)
	fs_cli: only enable new features on supported terminals (r:26cd927c)
	lang: Improve French phrase files (FSCONFIG-23)
	lang: Update langs - Add pt_PT, update es to have es_ES and es_MX, update mod_say_es and add mod_say_pt (FS-2937) (r:c81a9448/FS-2937)
	libapr: Fix issue where after a bridge with a member, uuid of Agent is set to single quote character ' (r:3fee704d/FS-2738)
	libdingaling: fix race on shutdown causing crash (FSMOD-47)
	libdingaling: Fix crash in new GV interface when exceeding 24 calls (r:be00609a/FS-2171)
	libdingaling: fix crash when GV call ends (r:687140b5/FS-3139)
	libdingaling: fix small leak (r:d3ea42d8/FS-3334)
	libdingaling: send keep alive packets to prevent NAT from munging connection (thanks Federico Beffa) (r:4bd305e5/FS-3612)
	libesl: Fix potential race condition (ESL-36)
	libesl: Add /uuid command to fs_cli to filter logs by uuid
	libesl: Increase buffer in fs_cli for Win (r:d1d6be88/FSCORE-611)
	libesl: fix esl buffer overflow (r:9c9cb5b3)
	libesl: add -r to fs_cli to retry every second up to 2 min to connect (r:36bfe432)
	libesl: Fix esl doc, s/ESL_STATUS_SUCESS/ESL_SUCCESS/g (r:84b4b5da)
	libesl: make esl_connect thread-safe by using getaddrinfo instead of gethostbyname (r:52885dfc)
	libesl: Add esl_connect_timeout to specify custom connection timeouts (r:12a0ec74)
	libesl: use recv_timed in esl_connect_timemout in case we get stuck in a blocking recv (r:13137e22)
	libesl: use uint32_t instead of long for timeout as in the rest of the lib, 0 means forever. Also added esl_send_recv_timed() (r:49d6c803)
	libesl: Fix SEGV when using serialize function without any arguments (r:910729b5/ESL-44)
	libesl: fix leak-on-error in esl_connect_timeout() (r:4263d60e)
	libesl: Call close on connection handle if the connection fails (r:413dcc4c/ESL-50)
	libesl: allow fs_cli -x to have args up to 1024 chars (was 256) (r:7039ba47)
	libesl: Make last_event pointer last longer (r:a15f51d5/ESL-37)
	libesl: use a packet buffer for ESL (r:2081bf97)
	libesl: Noevent/Noevents disparity (r:d29d83d7/ESL-53)
	libesl: FS-2957 esl lib on windows fails to build (r:5254df04/FS-2957)
	libesl: Small fix on ESL that cause event_id to be set wrong on headers that had value failure. (r:eb88304a)
	libesl: added python eslmod installation to esl Makefiles (r:b83a30ca)
	libesl: null terminate buffer after reading from the socket to prevent cross-over to old data that confuses the parser and throws off framing (r:e8a10558/ESL-56)
	libesl: add optional job-uuid param to bgapi in oop mod (r:e96acac3)
	libesl: fix linger support in esl client lib (r:0444626b)
	libesl: fix segfault (r:30813ca5/FS-3130)
	libesl: Don't destroy last_event pointer until it's being set to a new pointer - fixes rare segfault (r:e8474d60/ESL-57)
	libesl: Add 'make perlmod-install' to ESL (please test) (r:06c42179)
	libesl: build python esl bindings and ship them in freeswitch-python-package (r:44bfcf1d/FS-3128)
	libesl: use poll instead of select in ESL client lib because select is not your friend.... (r:ae595cd5)
	libesl: Add digit_timeout to ESL::IVR's playAndGetDigits method (r:f564d383)
	libesl: add array manipulation to the wraper code (r:ffa0a071)
	libesl: fix mem leak - good catch, Jlenk! (r:e420e17f/FS-3386)
	libesl: add sendmsg function to esl (r:2ae688a3)
	libfreetdm: implemented freetdm config nodes and ss7 initial configuration
	libfreetdm: fix codec for CAS signaling (r:b76e7f18)
	libfreetdm: freetdm: ss7- added support for incoming group blocks, started adding support for ansi (r:c219a73c)
	libfreetdm: receive side, update libteletone to track duration so it is less likely to double detect and push api changes down to freetdm (r:a65794fb/FS-3570)
	libg7221: A bunch of tweaks to the G.722.1 codec (r:5d548570)
	libgnutls: link to libgcrypt as well, please report any platforms this breaks, but it should be portable (r:c569fb0f/FS-1248)
	libiksemel: making this the new default and patching libdingaling to use it exclusively with openssl, now we actually have single thread for gtalk an no gah noodlez (r:f506e19e/FS-3471)
	libjs: non-portable comment syntax in .s files 
	libldns: select on FD > 1024 get this patch to ldns ppl (r:710fc7a7/FS-3110)
	libopenzap: Add CLI tracing
	libs: Merged OpenZAP and FreeTDM into the FreeSWITCH tree.
	libs: Add support for TLS on Windows using openssl (r:1abe3b93/MODSOFIA-92)
	libs: fix bsd shell incompatibility (r:e2b85e94/FS-287)
	libsndfile: Update libsndfile and edit mod_sndfile.c to support ogg and flac (r:02a604f7/FS-1197)
	libsofiasip: Fix random crashes (r:c15ee980/SFSIP-219)
	libsofiasip: Fix T.38 bug in sofia_glue (r:2843f1ad/MODSOFIA-94)
	libsofiasip: VS2010 sofia posix problem (r:46dd24c2/SFSIP-220)
	libsofiasip: set minimum initital sip t1 timer to 1000ms to work around race condition on retry timer firing before all the things that are supposed to be handled by the timer are set.  The base resolution on this timer is 500ms, so doubling up makes sure we always hit the initial retry timer on the next run, where everything should be set.  The side effect was, 1/2 the time on a request that did not get immediate response, the timer would be fired and cleared, but the action (sending retry) was never done, and a new timer was not set, causing the request to just sit zombied and never retry.  A better solution would be to find and correct the race condition so the timer is never set to early and we never hit this condition. (r:20c2740c)
	libsofiasip: fix bad assert (r:56404641/FS-3133)
	libsofiasip: lower stack and boost priority of sofia schedule thread (r:257bc9ff)
	libsofiasip: Fix for issue reported on the mailing list with a Chinese locale and windows. This commit removes a hidden char that should not have been there anyway. (r:7adaceb8)
	libsofiasip: resolve edge case in the 3rd party sofia sip stack library when dealing with a malformed contact and missing ack. Will push upstream to sofia devs (r:d68605f5/FS-3394)
	libsofiasip: use individual pools instead of sub-pools for nua handles to avoid pool swell (r:f7612413)
	libsofiasip: Fix segfault in sofia's stun code (r:7403db70)
	libsofiasip: add homer capture hooks to libsofia (r:3e029f0d)
	libsofiasip: Fix mem leak when homer capture server not available (r:bc177a4b/FS-3475)
	libspandsp: Fixed a typo in spandsp's msvc/inttypes.h Updated sig_tone processing in spandsp to the latest, to allow moy to proceed with his signaling work.
	libspandsp: removed a saturate16 from spandsp that was causing problems fixed a typo in the MSVC inttypes.h file for spandsp
	libspandsp: Changes to the signaling tone detector to detect concurrent 2400Hz + 2600Hz tones. This passes voice immunity and other key tests, but it bounces a bit when transitions like 2400 -> 2400+2600 -> 2600 occur. Transitions between tone off and tone on are clean. (r:bc13e944)
	libspandsp: Fix Windows build after libspandsp update (r:d70cc852/FSBUILD-293)
	libspandsp: Fix for T.30 processing of operator interrupts, to improve compatibility with some machines, which seem to send them when no operator is around. (r:84ee0ae6)
	libspandsp: spandsp t38 fax receiving error in win XP - regression from f029f7ef (r:761cec8f/FS-2766)
	libspandsp: Added missing error codes when an ECM FAX is abandoned with the T30_ERR message (r:ec57dc7a)
	libspandsp: Fixed a vulnerability in T.4 and T.6 processing which is similar to http://bugzilla.maptools.org/show_bug.cgi?id=2297 in libtiff. A really screwed up 2D T.4 image, or a maliciously constructed T.4 2D or T.6 image should potential run off the end of an image decoder buffer. (r:c6f67322)
	libspandsp: Changed T.38 terminal handling, so errors from the user's packet transmit routine properly filter up the chain, cause termination of the FAX session, and are reported to the caller. (r:c890fbfa)
	libspandsp: Numerous little changes to spandsp that haven't been pushed to Freeswitch for a while. The only big changes are a majorly rewritten V.42 and V.42bis which are now basically functional. (r:d30e82e2)
	libspandsp: Another round of tweaks for spandsp. There should be no functional changes, although quite a few things have changed in the test suite (r:4a7bbf4e)
	libstfu: add param to jb to try to recapture latency (disabled by default) (r:d59d41d7)
	libsqlite: fix issue on mailing list mod_crd_sqlite entry limit and sqlite segfaults on triggers (r:1badec17)
	libsqlite: make strdup NULL return strdup("") in sqlite for mac bug (r:b6bed14f)
	libsqlite: force an update on sqlite build (r:71dd3ca8)
	libunimrcp: Update to latest UniMRCP version.  MRCP requests can no timeout if there is no server response. (r:17099473)
	libunimrcp: unimrcp lib does not notify mod_unimrcp of RTSP TEARDOWN timeouts (r:3484f338)
	libunimrcp: fixed unimrcp to prevent double destroy of connection (r:493085bb)
	mod_avmd: Initial check in - Advanced Voicemail Detect (r:10c6a30a) (by Eric Des Courtis)
	mod_avmd: Add to windows build (r:df4bd935)
	mod_avmd: Fix mem leak (r:cd951384/FS-2839)
	mod_blacklist: Add mod_blacklist from contrib. (r:3a477c42)
	mod_blacklist: Add example configuration file (r:d00f7464)
	mod_blacklist: Resource leak fixes, config checks and add help output for api interface (r:41abb3e6)
	mod_blacklist: add ability to dump a list back to it's text file (r:1d5f5ec7)
	mod_blacklist: fix broken dump/save (r:abc5d7cd/FS-3617)
	mod_callcenter: Initial commit of the mod_callcenter application. This module is in it early state of developpement.  You can see documentation on the wiki at : <a href="http://wiki.freeswitch.org/wiki/Mod_callcenter">http://wiki.freeswitch.org/wiki/Mod_callcenter</a> For support/comments, please use <a href="https://freeswitch.org/jira">https://freeswitch.org/jira</a> and select the MOD CALLCENTER module. (r:ba09b96d)
	mod_callcenter: Add ability to unload/reload/load a queue setting (You still need to reloadxml before). Note that joining a queue will check for it in the config and load it on the fly... I've used the same system as in mod_voicemail.  Not sure if we should allow this, but just comment it out of the config before unload and it wont be available anymore (r:3eafca60)
	mod_callcenter: Try to fix the ring-all, also add cli auto complete done in previous commit (r:1666783c)
	mod_callcenter: Add missing odbc db support (Not tested, please someone test this) (r:42436e27)
	mod_callcenter: More ODBC changes.  It is not a global settings value.  Cannot be changed in runtime. (r:6980305f)
	mod_callcenter: Added value busy_delay_time and reject_delay_time so we can wait if those 2 occur (Un registred phone are considered as busy). Add a ready_time epoch time when we can contact an again again, fix ring-all (good this time I hope). (r:8082aa98)
	mod_callcenter: Add tiers rules before jumping to a different level.  Also added support for dial-in agent. (r:86c9bed7)
	mod_callcenter: Default the level to 0 since the new tier system will wait x second at level 1... just level 0 that will ring agent right away (if set to do so) (r:6558276a)
	mod_callcenter: You can now allow caller that have hangup before agent answer to call back and resume their previous position. (r:ab2529d4)
	mod_callcenter: correct multiple little things following the recent tiers and join back features (r:9b33bd1c)
	mod_callcenter: Add more channel variable and event and fix a mem leak (r:2d3d8c8d)
	mod_callcenter: Make more sence to bridge the caller to the agent.  Before, in the xml_cdr you saw it it like the agent initiated the call to the member (r:0be95658)
	mod_callcenter: Added max-wait-time and max-wait-time-with-no-agent param to a queue. (r:3482f95e)
	mod_callcenter: Make sure we fail to load if config is not present (r:e1fb79a1)
	mod_callcenter: Fix invalid update of agent field (r:426a448f/FS-2738)
	mod_callcenter: Allow to get queue info via api (r:70d592ae)
	mod_callcenter: Fix bad return type so it compile on archlinux, thx bougyman (r:3a475986)
	mod_callcenter: Make callcenter_config agent get return the value of the item requested.  Also added queue param max-wait-time-with-no-agent-time-reached: If the max-wai-time-with-no-agent is already reached for the queue, then new caller can wait for x amount of second before it kicked out of the queue rather than get rejected automatically. (r:81a03869)
	mod_callcenter: Add new event socket agent-offering.  Plus some documentation and better handling of bad agent type -- FS-2869 (r:80174cf3/FS-2869)
	mod_callcenter: Add error response for queue load and queue reload (FS-2988) (r:49a5effc/FS-2988)
	mod_callcenter: IMPORTANT UPDATE, DTMF during moh created an loop to reactivate MOH but got canceled right away because of pending DTMF in the queue never been cleaned.  Could cause masive disk write of debug, and can cause problem to the rest of FS stability.  This patch also include basic fundation for DTMF capture support for member waiting. (r:cd1982ce)
	mod_callcenter: force loopback_bowout=false on originate.  This will need to be reworked, but should fix basic issues call to an agent using loopback (r:2e399b0b)
	mod_callcenter: segfault using busy-delay-time parameter (r:c6f044d5/FS-3067)
	mod_callcenter: Fix a bug when an caller leave the queue from a BREAK Call (Transfer...), it doesn't think an agent answered. (r:51a531aa)
	mod_callcenter: Add new CLI cmd and change some to be more standard.  Patch from Francois Delawarde, thanks. (r:30dd1774)
	mod_callcenter: >WARNING, some event value got removed< Adding new event time value that can then be used to calculate the Wait;Talk;Total duration of a member were on call.  CC-Wait-Time CC-Talk-Time and CC-Total-Time are no longer returned.  Visit the code or check the wiki for the updated variable. (r:5f233785)
	mod_callcenter: Add better support when agent doesn't answer, including creating a new variable for the delay that is different than reject or busy.  Thanks to Francois Delawarde (r:26303c5c)
	mod_callcenter: Add better handle of failed agent, member channel getting a break, and debuging info upon leaving.  Thanks to Fran?ois Delawarde (with some changes) (r:25cee255)
	mod_callcenter: New Agent order Possibility: Agent order by Level and Position by agents.last_offered_call. Change the default and sequentially-by-agent-order strategy to include the longest-idle-agent.  This should offer a default consistant way to go through all the agent within the same tier/position. (Before, it was left to the DB to return the order of the result) (r:dcafff20/FS-3158)
	mod_callcenter: Generate per member uuid different from the member session uuid.  Might fix transfer between queue.  More changes are coming (r:b63a72f8)
	mod_callcenter: Remove the concept of Caller for Members.  Event Socket event have been changed (CC-Caller.* to CC-Member.*)  Also CC-Caller-UUID is renamed to CC-Member-Session-UUID. The reason for this is you could actually put people to be call in the queue.  So they are not caller per say.  But they are a member of a queue. (r:40a134bd)
	mod_callcenter: Reload a queue wont delete all the currently waiting members.  Only a reload of the module will. (r:c5ae5de0/FS-3250)
	mod_callcenter: Add a very prototype (and maybe not functional) strategy called : sequentially-by-next-agent-order. It will try to find the last agent we tried to reach, and start calling more agent after that one based on position. It will use the level for the next agent, but once that level is done, it start back at the lowest level (r:bef6f0f4)
	mod_callcenter: New strategies: round-robin, random, and 'top-down' (r:2b4b23aa,r:bee247ca)
	mod_callcenter: Display an warning when MOH is invalid and resume wait with silence. (r:37b14c9a/FS-2740)
	mod_callcenter: Fix member been switch as abandoned when he was pickup by an agent (r:9ff8f53f/FS-3281)
	mod_callcenter: Adapt mod_callcenter loopback agent fix to work with commit 2eae19e6 (r:d32ba761/FS-3657)
	mod_cdr_mongodb: add MongoDB CDR module (r:a9169199)
	mod_cdr_sqlite: initial commit (r:f625fe3b)
	mod_cdr_sqlite: config file for mod_cdr_sqlite (r:25bc8fe3)
	mod_cdr_sqlite: Drop transaction BEGIN/END around INSERT. We're only executing one command, and autocommit will automatically rollback if the INSERT fails. Sync state_handlers with mod_cdr_csv. Minor libpq fixups. (r:0f95b870)
	mod_celt: Bump celt to 0.10.0 (r:231fbe5e)
	mod_celt: update code in mod_celt to match API of 0.10.0 (r:6e4c30ea)
	mod_celt: Add dependency to fix parallel builds (r:6e37a8b2)
	mod_cepstral: add ability to set encoding of text (r:28738b06/FS-3001)
	mod_cidlookup: null xml is bad (r:095815f8)
	mod_cidlookup: honor skipcitystate when using whitepages (r:a66654de/FSMOD-53)
	mod_cidlookup: fix crash when caching is enabled (r:2e1f0b50/FS-3350)
	mod_commands: make break uuid_break and add cascade flag 
	mod_commands: add uuid_autoanswer command (now uuid_phone_event)
	mod_commands: expand last patch to do hold as well and rename the command to uuid_phone_event
	mod_commands: allow uuid_break to interrupt one or all in a delimited string of files the same as several individual files (r:eba05c3c)
	mod_commands: add show channels count auto-completion for mod_commands (r:5ffc57e5/FSMOD-54)
	mod_commands: Fix a segfault if no arguments is provided to limit_hash_usage (r:8ceb2a9b)
	mod_commands: fsctl max_session should display int, not float (r:f7e2410e/FSCORE-634)
	mod_commands: limit - reset rate and release resource apis  Thanks Moy (r:a7c31e6f/FSCORE-637)
	mod_commands: Fix user_data returning the first value found instead of the last.  Also add support to get variable from the group. (r:402f2391)
	mod_commands: Allow cond API to return empty false value (r:c8a897b9)
	mod_commands: ***BEHAVIOUR CHANGE*** reloadacl, load <module>, reload <module> will now explicitly call reloadxml (r:42c9df72)
	mod_commands: add nat_map usage (r:7577b8aa)
	mod_commands: add escaping empty strings to sql_escape (r:7bd0a5a6/FS-2833)
	mod_commands: add uuid_fileman <uuid> <cmd>:<val> <-- same vals as the callbacks in js and lua to control the currently playing file of a channel from the cli or ESL (for the people who were ignoring me on the conference call so I decided to implement it instead of try to explain it ) (r:c4369fc8)
	mod_commands: FS-2210 Add support for auto completion for uuid_simplify (r:72bcc01b/FS-2210)
	mod_commands: allow epoch in strftime_tz (r:bbf1cd1f)
	mod_commands: Dramatic jitterbuffer changes (r:d5470961)
	mod_commands: add uuid_buglist to fetch the current media-bugs attached to a given session uuid (r:f6eab64c)
	mod_commands: add recovery_refresh app and api and use it in mod_conference to send a message to the channel telling it to sync its recovery snapshot (r:650393fb)
	mod_commands: add moh by default to uuid_broadcast when only broadcasting to A leg use aleg arg to disable this (r:d164a797)
	mod_commands: add API uuid_limit - thanks to Francois Delawarde (r:98a95016/FS-1792)
	mod_commands: omit file_string:// prefix if input begins with ~ (r:f12ab59e)
	mod_commands: fix crash when uuid_break all cannot find bonded uuid channel (r:69e61f76/FS-3468)
	mod_commands: fix uuid_dual_transfer for inline dialplan (r:5d84efc3/FS-3403)
	mod_commands: update show calls to show both 1 legged calls and bridged calls, also show bridged_calls for previous behaviour of show calls (r:c16c74d9)
	mod_commands: Add 'presence_data' field to 'show channels like xxx' list of fields. This makes anthm's trick mentioned on the mailing list even more handy. (r:4872e6ff)
	mod_commands: Update tab-complete for show cmd to include bridged_calls, detailed_calls, detailed_bridged_calls and removed distinct_channels (r:2fa8f110)
	mod_commands: add threaded-system-exec param and fsctl (set it to false to use fork) (r:910f5364)
	mod_conference: Fix reporting of volume up/down (MODAPP-419)
	mod_conference: add last talking time per member to conference xml list
	mod_conference: add terminate-on-silence conference param
	mod_conference: Add User Flags to Member Data Events (MODAPP-423)
	mod_conference: add conference_member_id variable to all sessions with the member id used by their conference participation (For drk__) (r:49c9bfdb)
	mod_conference: fix relate nohear (r:f029ce07/MODAPP-428)
	mod_conference: Fix floor change events not always firing (r:8f1767d3/MODAPP-424)
	mod_conference: refactor conference to use switch_ivr_dmachine for the digit parsing controls are now bound to each member separately based on conference_controls channel var, then the caller-controls param in the profile or a default to "default" (r:ac19f73c)
	mod_conference: Fix crash on dtmf action (r:4d5389bd/FS-2781)
	mod_conference: revert to the last transfered conference on recover (r:d11c83b1)
	mod_conference: Add a chan var conference_enter_sound to override conference enter-sound param on the profile (r:651acc62)
	mod_conference: Add an unique id to the conference obj so that we can track conferences. (r:479f3de2)
	mod_conference: Fix corrupted audio when playing "you are now (un)muted..." (r:10257c7d/FS-2768)
	mod_conference: clear last_transferred conference when you exit the conference app (r:fb017a52)
	mod_conference: Add energy level to conference_add_event_member_data (r:8d6d52e0)
	mod_conference: if more digits than the length of the correct pin the remaining digits are accounted for next retry (r:b88cd345/FS-3000)
	mod_conference: Fix unexpected behavior with endconf and auto-outcalls and pre_answer (r:6f58e6aa/FS-2771)
	mod_conference: Added conference UUID to xml_list (r:10d696eb)
	mod_conference: Added to the Auto OutCall support to specify the conf profile to be used using variable : conference_auto_outcall_profile (r:67edc7c3)
	mod_conference: don't switch to CS_SOFT_EXECUTE before setting the current extension (r:4b5bcba0)
	mod_conference: play files saying vol level in conf in lieu of making a function of say modules to return file_string urls (we need that) (r:94b680fb)
	mod_conference: fire auto gain level events (r:d8ef36ed)
	mod_conference: clear talk flag when you mute (r:b7419add)
	mod_conference: fix pthread mutex lock error and add some tab completion and help messages from cli (r:547d5393/FS-3095)
	mod_conference: Use the channel's sound_prefix if it's not set in the conference's config (r:0911ed74/FS-3124)
	mod_conference: Add conf_uuid chan var for djbinter (Thanks Math) (r:3c9ee25a)
	mod_conference: removes the existing conference transfer function and replaces it using the core transfer it also introduces a new tracking method where the same conference id is reserved for a particular member for the lifetime of the call allowing a user to transfer in and out of conferences and ivr and bridges etc and retain the same member id for the duration of that call (r:246b2195/FS-3095)
	mod_conference: prevent race condition on conference join/exit (r:1552ecf5)
	mod_conference: I finally tracked this down to the actual recordings generated by mod_conference.  This patch delays the recording slightly to allow time for the buffer to fill up, we were riding it so closely that sometimes we would come up short and inject silence into the file to preserve time passing (r:3253bcb3/FS-3147)
	mod_conference: wait for channels to come up in paging mode (r:b8063c3d)
	mod_conference: Conference APIs for enabling/disabling enter/exit sounds for active conferences (r:31cebd4f/FS-3219)
	mod_conference: Fix pool swelling, replaced a pool strdup that could recur with a strdup/free to avoid it (r:bcd6c3a1/FS-3137)
	mod_conference: remove auto gain events (r:7ba849b3)
	mod_conference: add custom exit sound to match enter sound on conf (r:3d475876)
	mod_conference: don't play default when playing a custom one (r:c7b36157)
	mod_conference: Add 'conference xxxx list count' to get exact member count for a given conference (r:f731abe0)
	mod_conference: move muted/unmuted indications to main thread via flags (r:e8962d56)
	mod_conference: wait for thread to start in mod conference to avoid one in a million race on heavy traffic (r:b1cf5bee)
	mod_conference: add conference member flag nomoh (r:f35a6814)
	mod_conference: add hup command to conference (kick without the kick sound) (r:492db906)
	mod_conference: see H.264 iFrames (r:765be8c9/FS-3406)
	mod_conference: add moderator PIN controls (thanks Moy) (r:1936c2b0/FS-3493)
	mod_conference: remove waste flags from both conference and member and explicitly always send audio from conferences to avoid random interop issues and general discomfort these flags are now deprecated (r:5d77e789)
	mod_conference: add conference cdrs to mod_conference (r:127be02d)
	mod_conference: add custom kick sound to conference (r:8fde25cc)
	mod_conference: Fix bug where not entering PIN allows caller into PIN-protected conference (r:9dd45e35/FS-3661)
	mod_conference: fix crash in video-bridge mode (r:4c13e7c0)
	mod_curl: use method=post when post requested (r:c6a4ddd0/FSMOD-69)
	mod_db: fix stack corruption (MODAPP-407)
	mod_dialplan_xml: Add in the INFO log the caller id number when processing a request (Currenly only show the caller name) (r:e1df5e13)
	mod_dialplan_xml: Add <regex> tag for OR logic in XML dialplan (r:c1615dbb/FS-3655)
	mod_dialplan_xml: add xor to regex attr (r:7c7b0068/FS-3655)
	mod_dialplan_xml: add tod_tz_offset variable to set to the integer value of the tz offset or the tz-offset tag on a per-tag basis (r:65a75664)
	mod_dingaling: make mod_dingaling compat with google's new free phonecalls thing (r:ba0a2a32)
	mod_dingaling: make dingaling work with google voice inbound too (r:4ee68141)
	mod_dingaling: Fix crash when testing the new gv-dingaling with around 24 concurrent calls (r:73e1ec5e/FSCORE-667)
	mod_dingaling: Fix NULL pointer (r:e3eff816/FS-1103)
	mod_dingaling: fix leak in chat_send (r:eb109a85)
	mod_dingaling: use the login as message source when not in component mode. (chat_send) (r:58c28aab)
	mod_dingaling: fix mod_dingaling/iksemel/gnutls link error when using newer autotools (r:294b0779/FS-3182)
	mod_dingaling: fix segmentation fault on mod_dingaling when receiving a discovery from the server (r:2e651c8f/FS-3391)
	mod_dingaling: Remove unused but set variables GCC-4.6 -Wunused-but-set-variable (r:0f45b8ba/GCC-4)
	mod_dingaling: Add from_jid (r:f0b52ef7/FS-3611)
	mod_dingaling: autoflush durning bridge in dingaling (r:bd9317f2)
	mod_directory: Add variable directory_search_order to allow to search by first name by default is set to "first_name" (r:163ca31f)
	mod_directory: let mod_directory use non-XML dialplans (r:8895de1b)
	mod_distributor: Add mod_distributor to VS2010 - not built by default (r:bac79ba1)
	mod_dptools: add eavesdrop_enable_dtmf chan var (r:596c0012)
	mod_dptools: Make park app not send 183 session progress (r:76932995/FSCORE-567)
	mod_dptools: add block_dtmf and unblock_dtmf apps (r:d9eb0197)
	mod_dptools: refactor export code and add new bridge_export app which is like export but exports across when one channel bridges another (r:4aa9a838)
	mod_dptools: add bind_digit_action application (r:9537197b)
	mod_dptools: emit event when user presses DTMFs (r:37298f56)
	mod_dptools: Log error when there's no IVR menus configured when you call 'ivr' DP app (r:30034891)
	mod_dptools: reset signal_bond variable back to its original value on failed dial in att_xfer (r:330d7418)
	mod_dptools: Fix storage class for 'cause' in user_outgoing_channel() so that each call has its very own hangup cause (r:cb6f1ed6)
	mod_dptools: transfer_on_fail note I changed the variable name to auto_cause (r:45edec4c/FS-3193)
	mod_dptools: merge file_string into dptools (r:eefdb764)
	mod_dptools: change mod_dptools to use the better method of fetching user xml that does not hang onto the xml root (r:e52e44e3)
	mod_dptools: the intent for having the module and lang separate is for things where the same module can use different sets of sounds like en module and en-male or en-female lang (sound dirs) there was indeed a disconnect in the dialplan version of this app.  Originally say was only available in phrase macros so I change the syntax of the say app so you can specify both the module and the lang absolte from the dp with something like he:he as the module name. (r:44304f49)
	mod_dptools: Set the default lang if not supplied (mod_say_en) (r:5382972a/FS-3215)
	mod_dptools: add capture dp app (r:860d2a6c)
	mod_dptools: Allow redefinition of continue_on_fail and failure_causes during bridge execution. (r:01d0250e/FS-1986)
	mod_dptools: Fix "dial 0" 3-way call on att x-fer (r:d4fe85ed/FS-3275)
	mod_dptools: fix campon to play music even on first run and cancel faster (r:9cf44f3a)
	mod_dptools: fix small leak in strftime (r:bbbd67ba)
	mod_dptools: resolve Heap corruption in strftime_api_function -thanks (r:707bd05b/FS-3417)
	mod_dptools: fix seg on user_recurse_variables reported on the mailing list (r:01b2bd04)
	mod_dptools: add digit_action_set_target app that can set the target (direction of the dtmf flow and subsequent channel who gets the events) to self or peer (bridged channel when possible) (r:cf9859ea)
	mod_dptools: get rid of digit_action_set target and add target,bind_target params to bind_digit_action (r:42b64ccd)
	mod_dptools: add flags to digit bindings (r:d93ed90b)
	mod_dptools: New dialplan app: play_and_detect_speech (r:34338e5a/FS-3692)
	mod_easyroute: Fix possible segfaults and memory leak during unload, and add new setting odbc-retries (r:7fbc47f8/FS-2973)
	mod_enum: switch mod_enum to use new portable in-tree version (r:2bbc37e3)
	mod_enum: fix race condition between ldns configure creating ldns/util.h and mod_enum (r:87884c5c)
	mod_enum: fix ms resolution with new query-timeout-ms, query-timeout still works as expected (r:88f4828c/FS-3282)
	mod_enum: fix ldns_lookup not respecting query-timeout (r:1d490df9/FS-3282)
	mod_erlang_event: Make XML fetch reply ACKs distinguishable, update freeswitch.erl (r:9d44ed04)
	mod_erlang_event: Add 3 new commands; session_event, session_noevents, session_nixevent (r:698fa045)
	mod_erlang_event: generate long node names the same as erlang does (r:9ad509c2)
	mod_erlang_event: Improve some logging to include UUIDs (r:c0d51b83)
	mod_erlang_event: Support for reading erlang cookie from a file (r:094ffe37)
	mod_erlang_event: Rewrite XML fetch conditional wait to be more sane (Reported by James Aimonetti) (r:6941c6eb/FS-2775)
	mod_erlang_event: Don't urlencode events (and destroy an event after use) (r:4eccdfef)
	mod_erlang_event Add proper locking for the list of XML bindings (r:9fe440b2)
	mod_erlang_event: Fixed a memory leak, too short of connect times across data centers, a deadlock condition with the globals.bindings_rwlock not being released, a buffer overrun possibility or 4, and added the ability to send a body when injecting an event (r:994f9a8c)
	mod_event_multicast: make multicast loopback configurable (r:97a7668c/FS-3416)
	mod_event_socket: fix up other users of switch_event_xmlize() to use SWITCH_EVENT_NONE (r:d6eb7562)
	mod_event_socket: Fix small mem leaks (r:e4f90584/MODEVENT-68)
	mod_event_socket: Add "-ERR" to api cmd response when failure occurs (r:58759052/FS-2827)
	mod_event_socket: clear unique headers on event_socket filters (r:436413e0)
	mod_event_socket: Unlock mutex to prevent mortuus obfirmo (r:64bc1938/FS-3156/FS-3157)
	mod_event_socket: (and mod_erlang_event) make empty apply-inbound-acl config line not deny all (r:8ae9ab5d/FS-3034)
	mod_event_socket: allow duplicate headers to be parsed into events received on the wire (r:2b7a830d)
	mod_event_socket: add optional format string after myevent (r:7ed7f539)
	mod_event_socket: Allow ridiculously long commands over event socket (r:6bbde4e2/FS-3621)
	mod_event_zmq: Intitial mod_event_zmq code (r:4d554067)
	mod_event_zmq: Update download file from 2.1.3 to 2.1.4 (2.1.3 tar file is gone from zmq server) (r:0b780702)
	mod_event_zmq: Bump to zeromq-2.1.9 (r:3a352e67)
	mod_fifo: allow multiple dtmf to exit fifo, set fifo_caller_exit_key to specify which (MODAPP-420)
	mod_fifo: cancel outbound call if customer hangs up (r:cadb4d94)
	mod_fifo: add taking_calls param to fifo member add and config file (r:821488bf)
	mod_fifo: add nomedia flag (r:2d30a8c2)
	mod_fifo: Fix inconsistency between the fifo queue and the channels (num callers in queue can become "-1") (r:07487114/FS-1659)
	mod_fifo: fix issue leaving stale records in fifo_bridge table (r:b36d015f)
	mod_fifo: fix fifo race in use count dec (r:402e383b)
	mod_fifo: add outbound_ring_timeout param to mod_fifo (r:3885eea7)
	mod_fifo: add default_lag to fifo (r:dd4fb5be)
	mod_fifo: Fix crash when using fifo_destroy_after_use (r:ee562c82/FS-2879)
	mod_fifo: don't seg in edge case error conditions (r:9ee13b72)
	mod_fifo: set tracking data before enabling hooks (r:34267869)
	mod_fifo: Fix fifo orbit timeout when not using a chime tested with and without chime (r:7fee1fd1)
	mod_fifo: Fix URI (r:26039c57/FS-3548)
	mod_fifo: add _continue_ value for fifo orbit exten that just means exit back to the next dp instruction (r:4d6ee827)
	mod_file_string: Fix segfault when using file string in conference (r:9c40e8e9/FS-3122)
	mod_flite: Update to flite 1.5.1 it should sound better now too (r:ecbd1db8)
	mod_freetdm: Fix for TON and NPI not passed through to channel variables on incoming calls
	mod_freetdm: add pvt data to freetdm channels fix fxs features (r:9d456900)
	mod_freetdm: export and import boost custom data (r:edb2d582)
	mod_freetdm: windows casting (r:74a3f20e)
	mod_freetdm: add call waiting disable/enable feature (r:7256232a)
	mod_freetdm: stop loop on call start (r:dcd02fe9)
	mod_freetdm: use s<spanno>c<channo> notation for logging channel related msgs (r:9b6a9b6c)
	mod_freetdm: Fix Windows build in release mode (patch by Peter Olsson) (r:dace9df1/FSBUILD-277)
	mod_freetdm: disable DTMF app and cmd line option (r:f974cea8)
	mod_freetdm: enable DTMF app (r:e00d2af9)
	mod_freetdm: added scheduling API (r:041b8da5)
	mod_freetdm: run sched in the background if requested (r:22e8a442)
	mod_freetdm: fix makefile and remove binary app (r:63d9768d)
	mod_freetdm: add trace/notrace commands to trace input and output from channels (r:f4da0e5c)
	mod_freetdm: add logging when failing to read a frame in mod_freetdm (r:e596fc2e)
	mod_freetdm: add new logging macro (r:75be3da8)
	mod_freetdm: check for hw dtmf before enabling (r:b1fd88d7)
	mod_freetdm: adding ftmod_sangoma_ss7 support (r:94283355)
	mod_freetdm: added SIGEVENT_COLLISION (r:501f8704)
	mod_freetdm: fix Windows build of mod_freetdm (r:71650ae1/FSBUILD-291)
	mod_freetdm: call rate limit (r:a1fe7c8d)
	mod_freetdm: fix hangup race (r:438c93e8)
	mod_freetdm: added dtmf recording feature for debugging (r:9f9c4541)
	mod_freetdm: add PRI tapping starting code (r:b485f25f)
	mod_freetdm: Fix build of mod_freetdm in Windows (r:04dbc7bc/FSBUILD-295)
	mod_freetdm: fix fxs dialtone - should be stopped on first digit (r:f822180f)
	mod_freetdm: add bearer capability and layer1 pass-thru for boost (r:07b81760)
	mod_freetdm: OPENZAP-107 - Patched by Jeff Lenk (r:aa075136/OPENZAP-107)
	mod_freetdm: allocate channels in their own memory page when debugging (r:fcd8df0a)
	mod_freetdm: lock the channel when placing call (r:705dd237)
	mod_freetdm: created cmake files for freetdm (r:fc55997b)
	mod_freetdm: ss7 - added support to control mtp2, mtp3, and isup timers via freetdm.conf.xml (r:4455d581)
	mod_freetdm: made ftmod_r2 use FTDM_SPAN_USE_SIGNALS_QUEUE and properly send FTDM_SIGEVENT_SIGSTATUS_CHANGED (r:af5f0a4a)
	mod_freetdm: add specific alarm status in dump (r:7c971707)
	mod_freetdm: make ananlog alarm (r:23d86585)
	mod_fsk: add mod_fsk (r:fcc912a9)
	mod_gsmopen: copy from branch
	mod_gsmopen: fix FS-2793, compilation stops (r:355c0dbb/FS-2793)
	mod_gsmopen: retry serial initialization if failed, zeroing audio buffers, slower retry on soundcard busy (EAGAIN) (r:c7aefe93)
	mod_h323: initial t.38 support. remake logical channel opening. add missing param name in example config. (r:8c58074c)
	mod_h323: some t.38 and locking improvements. replace ptrace with switch_log_printf. (r:5efe5c88)
	mod_h323: add missing conf praameter (r:0b353d7a)
	mod_h323: Add mod_h323 to windows (r:015bcaf6/MODENDP-301)
	mod_h323: move PTrace level set to FSH323EndPoint::Initialise. partially apply patch from from Peter Olsson, Remove UnLock() when TryLock() failed and DEBUG_RTP_PACKETS directiv e. (r:7b5803f7)
	mod_h323: set network_addr of caller profile to signaling ip address. (requested by Steven Ayre) (r:072bf5ad)
	mod_h323: fix race condition on destroying signaling thread in h323 library (r:c22aac0e)
	mod_h323: move the switch_rtp_request_port() call from the contructor to FSH323Connection::CreateRealTimeLogicalChannel() - fix rtp port leak. tnx to Peter Olsson. (r:6f4c4ea0)
	mod_h323: fix stale calls then fax send/recv in some circumstance (r:00fce043)
	mod_hash: free all hashtables on shutdown (r:e76d7d92)
	mod_hash: remove unneeded initializer (r:10d468a6)
	mod_hash: begin working on remote support (r:c5ad49da)
	mod_hash: remove runtime function properly (r:a55747aa)
	mod_hash: remote sync in working state (r:f66ac890)
	mod_hash: add hash_remote api function (r:7036c9b8)
	mod_hash: fix syntax messages and add console autocomplete (r:67713fd6)
	mod_hash: change tab spacing for api, remove unnecessary log at every connection attempt in case a box is down (r:6d8c0b19)
	mod_hash: add hash.conf.xml (r:51bc65e2)
	mod_hash: fix null check on API (r:52c278c2)
	mod_hash: formatting and add an error message in the api (r:9e047ef1)
	mod_hash: use 5 seconds connection timeouts for remote connections (r:7431fbe9)
	mod_hash: use esl_recv_timed with a 5000ms timeout when doing api commands (r:27d8378f)
	mod_hash: limit_remote_thread sending invalid handle to esl_connect_timeout causing core (r:6cdd3e2a/MODAPP-446)
	mod_hash: avoid scheduler calling a function on null hash during shutdown (r:8458adeb)
	mod_hash: add realm filter to hash_dump db command so that you can quickly dump all entries that belong only to a specific realm without getting the whole db (r:81347126)
	mod_http_cache: Initial commit of new module (thanks crienzo) (r:c51acfcc/FS-3597)
	mod_java: fix eventConsumer issue and add flush() method (r:7fd3aff6)
	mod_java: Allow user defined java methods to be called at startup and shutdown of JVM (r:1339e218/MODLANG-117)
	mod_json_cdr: Fix segfault in mod_json_cdr.c (r:f347698a/MODEVENT-66)
	mod_khomp: Added mod_khomp Endpoint. (r:5fea197b)
	mod_khomp: Removed alternative contexts / extensions - New struct for matchs - On calls originated from an FXS branch, the Endpoint searches for a valid extension (digits sent) after the DTMF '#' or after the timeout (option fxs-digit-timeout). That search is done in the context defined in section <fxs-options>, or if no context configured, the search is done in context defined in context-fxs. - Added "dialplan" configuration: Name of the dialplan module in use (default XML) - Group context enabled. If set, the search for a valid extension is done only in that context. - Updated documentation (r:1ef3fc9a)
	mod_ladspa: Add mod_ladspa (Audio plugin framework for linux) (r:2d3d8f8d)
	mod_ladspa: add string params to ladspa so you can connect files to audio ports (string params don't count towards number params) (r:b7891511)
	mod_ladspa: putenv() breaks the process environment variables, use setenv() instead. (r:f6dadb58)
	mod_lcr: Expand variables (MODAPP-418)
	mod_lcr: add enable_sip_redir parameter (r:70bf7a0a/MODAPP-427)
	mod_lcr: don't validate profiles with ${} vars since they are dynamic   and we can't guess what the proper value should be (r:af33afaa)
	mod_lcr: fix dialplan issues with default profile and logging when no caller_profile set (r:00170558)
	mod_lcr: assign default profile even if testing is skipped (r:6420099c)
	mod_lcr: fix compiler warning on newer gcc (r:bfa414cb)
	mod_lcr: don't count twice (r:eaeabc7b/FS-1810)
	mod_lcr: properly destroy lcr object when done (r:084819a3/FS-3199)
	mod_lcr: don't add routes that have no rate of the desired type (r:82e3ccf8)
	mod_lcr: fix "as xml" for larger number of arguments (r:3dca2ebb/FS-3283)
	mod_lcr: fix malformed XML when has embedded %s (r:5fa9619f/FS-3284)
	mod_lcr: initial addition of very basic LRN (r:6d1d4a9c)
	mod_logfile: Replace hard-coded file permissions w/ OS default or umask (r:513a9e1a/FS-3710)
	mod_loopback: add loopback_bowout_on_execute var to make 1 legged loopback calls bow out of the picture
	mod_loopback: only execute app once in app mode (r:64f58f2d)
	mod_loopback: fix bug in mod_loopback where bowout=false (r:e9ab5368)
	mod_loopback: pass indication when in app mode on mod_loopback (r:c423e209)
	mod_loopback: fix voicemail failure (r:1a1881e8/FS-2795)
	mod_loopback: pass ring_ready like we do with pre_answer (r:9d087d45)
	mod_loopback: refactor mod_loopback timeout handling (r:43442e4f)
	mod_loopback: Fix loopback_bowout_on_execute failure when doing txfax calls (r:895b505f/FS-3494)
	mod_lua: Add switch_core_sqldb functionality from inside Lua script (r:26f2e095/FS-1384)
	mod_lua: Made 2nd arg to freeswitch.Dbh:query (cb func) optional (r:87db11af)
	mod_lua: Added SAF_ROUTING_EXEC flag to lua app, so it can be run inline (r:7d5ca1c0)
	mod_lua: spelling error in -ERR return code encounterd -> encountered (r:86e7cdc5/FS-2949)
	mod_lua: Make dbh:connected accessible from Lua - thanks Grmt (r:09e6fd3f)
	mod_lua: Added optional core: prefix to first arg passed to freeswitch.Dbh for giving direct access to sqlite db (r:a0181479)
	mod_lua: expose switch_simple_email as "email" method (r:89c5f3bf/FS-3023)
	mod_lua: Fix setInputCallback crash (r:c49c1fde/FS-3161)
	mod_managed: Added wrapper for switch_event_bind for .net (r:a5f07a80/MODLANG-165)
	mod_managed: add additional support (r:5be58aac)
	mod_managed: add mono 2.8 patch file see FS-2774 (r:6a948bd9/FS-2774)
	mod_managed: resolve Memory leak in mod_managed by EventBinding and swig delete_switch_event (r:c6048134/FS-3381)
	mod_managed: upgrade mono to support 2.8 - MichaelGG (r:1dcac642/FS-2774)
	mod_mongo: New mod, initial commit; module for MongoDB (http://www.mongodb.org/) (r:dc6ca6f8/FS-3278)
	mod_mongo: add mapreduce API (r:7c5b5797/FS-3357)
	mod_mp4v: MP4V-ES passthru for washibechi on IRC
	mod_mp4: New module. Supports playback of MP4 files. Depends on libmp4v2 <http://code.google.com/p/mp4v2/> (originally compiled against v1.6.1)
	mod_nibblebill: free allocated mem at shutdown; free properly if using custom_sql
	mod_nibblebill: Add SAF_SUPPORT_NOMEDIA to nibblebill
	mod_nibblebill: fix compile issues in latest HEAD (r:b073e82b/FSMOD-51)
	mod_nibblebill: remove on_reporting hook (r:897e6573/FS-2890)
	mod_openzap: custom data (r:5d4db94d)
	mod_openzap: more ss7 custom data (r:c93e392d)
	mod_openzap: handle loop requests (r:23766e36)
	mod_openzap: disable loop on call start (r:76e62fea)
	mod_openzap: callwaiting disable (r:e1b60b4c)
	mod_openzap: disable dtmf app and cmd line option (r:fb4b7f7a)
	mod_openzap: add enable dtmf app (r:3c95106e)
	mod_opus: add mod_opus (r:8f565277)
	mod_opus: Use libtool archives for linking, add dependencies to fix parallel builds (r:74bbd4be)
	mod_osp: initial check (Open Settlement Protocol)
	mod_osp:Changed OSP TCP port from 1080 to 5045. (r:03abefdf)
	mod_perl: add chat app to perl (r:18897f9e)
	mod_pocketsphinx: Update PocketSphinx to the latest builds... only had to make two changes (r:1a39d7fb)
	mod_pocketsphinx: They no longer ship the wsj model in pocketsphinx... and seems the dictionary has moved a bit. (r:********)
	mod_portaudio: Fix inbound state (CS_ROUTING not CS_INIT) (MODENDP-302)
	mod_portaudio: mod_portaudio improvements and bug fixes (r:33b74ca8/FS-3006)
	mod_portaudio: Add pa devlist to portaudio webapi (r:e8f10ea3)
	mod_portaudio: fix crash on bad init (r:6f49e6ba/FS-3361)
	mod_portaudio: move load_config a bit lower since it needs to use the hashtables (r:1529c0ec)
	mod_portaudio: Fix Windows crash (r:94c9cbf6/FS-3498)
	mod_portaudio_stream: update to specify the channel index (r:d1169d6e)
	mod_portaudio: mod_portaudio: fix incorrect use of pa input buffer that could lead to garbage audio (r:f2cf68bf)
	mod_protovm: Removed by author; replaced by mod_voicemail_ivr
	mod_python: add chat app to python (r:********)
	mod_radius_cdr: Add 'Freeswitch-Direction' av pair (r:a5170df0)
	mod_radius_cdr: Add 'Freeswitch-Other-Leg-Id' av pair (r:18d29b46)
	mod_radius_cdr: log errors with the call's uuid (r:fee49b16)
	mod_rtmp: RTMP as easy as A.B.C. Avant-Garde Solutions Inc. -- Barracuda Networks Inc. -- (r:0933a343)
	mod_rtmp: Remove duplicate output from rtmp status profile xxx API command (r:2e016541)
	mod_rtmp: Make all sockets non-blocking (r:affcdb0a)
	mod_rtmp: mod_rtmp for windows (r:f8cda539/FS-3355)
	mod_rtmp: flush buffer to avoid lag and enable plc (r:4bb76831)
	mod_rtmp: add conf (r:4eaabd28)
	mod_rtmp: set variables based on input hash (r:3815d188)
	mod_rtmp: Remove duplicate output from rtmp status profile xxx API command (r:2e016541)
	mod_rtmp: Make all sockets non-blocking (r:affcdb0a)
	mod_rtmp: Lower default buffer size to 50ms (r:d52a254d)
	mod_rtmp: CNG frames need to have codec set too (r:36f812d9)
	mod_rtmp: remove superfluous hangup (r:50817655)
	mod_rtmp: fix crash when call made from user not in domain (r:a5452174/FS-3353)
	mod_rtmp: add params so xml curl can know who's calling the directory request (r:91b73d1f)
	mod_rtmp: New feature: param disallow-multiple-registration on user directory logs out all other users on other sessions with the same user:domain pair. (r:624a2921)
	mod_sangoma_codec: Add sample config file
	mod_sangoma_codec: added load/noload options for the supported codecs
	mod_sangoma_codec: rename load/noload to register/noregister
	mod_sangoma_codec: silence suppression (r:73d9d56f)
	mod_sangoma_codec: do not return 0 len frames and return silence instead when there is no transcoding output update stats only when we really receive a frame (r:dc4d19e9)
	mod_sangoma_codec: flush sockets on first use (r:bbba1148)
	mod_sangoma_codec: use native L16 for the source codec (r:d2e25519)
	mod_sangoma_codec: default to G729 only and remove L16 since it did not make any sense (r:1d5aa062)
	mod_sangoma_codec: add iLBC 13.3k and 15.2k (r:68abb39e)
	mod_sangoma_codec: add G723.1 (r:3e33540a)
	mod_sangoma_codec: register AMR (r:31cc2502)
	mod_sangoma_codec: add G722 (r:ca8c2336)
	mod_sangoma_codec: add siren7 32kbps (r:fcaf2677)
	mod_sangoma_codec: add SIREN7 24kbps (r:3acc5fdb)
	mod_say: Fix crash for Say Number Pronounced with numbers of more than 9 digits (r:06bccf28/FS-3202)
	mod_say_de: method PRONOUNCED - grammatical fixes, thank you Christian Benke (r:5d46ddbc/FS-3195)
	mod_say_en: introduce new say_string method of doing say and use it in mod_say_en as an example.  try: eval ${say_string en.gsm en current_date_time pronounced ${strepoch()}} from the cli with this patch.  We can do more to centralize the say things and go back and apply it to other langs, using this method you can set the desired file ext as well which I think is a bounty.... (r:d5ef86d7)
	mod_say_en: If you only tell SAY CURRENCY to say 100 it should only say 100 dollars without the "0 cents" (r:426a4e76/FS-2922)
	mod_say_en: add "PRONOUNCED_YEAR" so 1985 is "nineteen eighty-five" (r:c0af0a85)
 	mod_say_es: fix grammar when saying dates and time (r:6bed19b2/MODAPP-429)
    mod_say_he: Add Hebrew say module (r:ebd9c83e/FS-3211)
	mod_say_ja: initial commit, still needs sound files (r:b2423158/FS-2755)
	mod_say_ru: Fix saying time with +1 hour of current time (r:68d74c31/MODAPP-444)
	mod_say_ru: now support say_string like mod_say_en. Now support channel variables gender,cases can be set in english and russian for example: <action application="set" data="cases=nominativus/>  <action application="set" data="gender=male_h"/> <action application="say" data="ru NUMBER PRONOUNCED  1001"/> (r:8b5ecd2f)
	mod_say_zh: Number reading should now be OK for the whole range of integers for Cantonese and Mandarin
	mod_shell_stream: Fix defunct processes being left behind (r:89666f44/FS-3316)
	mod_shout: bump mod_shout to use mpg123-1.13.2 to hopefully address unwanted calls to exit() and inherit other upstream fixes (r:079f3f73)
	mod_shout: add append flag to mod_shout, can append MP3's (r:0419c4e0)
	mod_shout: add ability to set bitrate, samplerate, and encoder quality in config file (r:8ea3cbd5/FS-1231)
	mod_shout: Initialize Lame sooner for file open (r:44b5cf82/FS-3646)
	mod_silk: Fix mod_silk compliance and performance issues (r:2ddbc457/MODCODEC-20)
	mod_skinny: Add the missing api files
	mod_skinny: add example dialplan and directory config (r:1bfcc17e)
	mod_skinny: rewrite of the skinny state machine (r:8cc89ab0)
	mod_skinny: More channel variables (r:5bdf8bcc)
	mod_skinny: Avoid message too short for off_hook (r:2cb595c7/SKINNY-1)
	mod_skinny: add mod_skinny vcproj file for windows build (r:f89cbdd6/MODSKINNY-3) 
	mod_skinny: size_t has different sizes on different platforms (r:a2ceff14/MODSKINNY-5)
	mod_skinny: correct define_time_date (r:48c7bb32/SKINNY-4,SKINNY-7)
	mod_skinny: hangup and hold calls in PROCEED state (r:5e07bd33)
	mod_skinny: avoid crash with autocompletion when skinny is unloaded (r:db1289df)
	mod_skinny: corrected early media (r:b40804a5/MODSKINNY-8)
	mod_skinny: ib_calls stats (r:165140e0)
	mod_skinny: blind transfer MODSKINNY-10 (r:53f75e9c/MODSKINNY-10)
	mod_skinny: ring tone on dialing side (r:0a04ecb8)
	mod_skinny: stop media on early media hangup (ring-out) (r:ce352bcc)
	mod_skinny: add windows x64 build support for  mod_skinny (r:3e205683)
	mod_skinny: avoid "-ERR no reply" when using API commands from CLI (r:690ae1b3)
	mod_skinny: allow configuration of softkeys via xml (r:f5a6831f)
	mod_skinny: allow skinny-default-soft-key-set-set per device (r:07c3c94d)
	mod_skinny: Rename skinny-default-soft-key-set-set to skinny-soft-key-set-set (r:ba3a6ad6)
	mod_skinny: centralized registration (r:e7a8189b)
	mod_skypopen: making XEvents to works when EARLYMEDIA, and correctly manage threads death
	mod_skypopen: now answer a call only when directed to do it (before was trying to answer any incoming call). Lot of changes to a messy part, so maybe some problem will come out... (r:45c6c4d3)
	mod_skypopen: ignore early media sent by channels to be bridged before our channel is answered (r:ef14b78a)
	mod_skypopen: OSS driver, refinement (r:b0a23f8e)
	mod_skypopen: deleted osscuse subdir (r:4842a620)
	mod_skypopen: adding installer and Skype client configuration directories (to be announced :) ) (r:25ebf715)
	mod_skypopen: refining INTERACTIVE INSTALLER for Linux (to be announced :) ) (r:aa7f47ac)
	mod_skypopen: refining oss driver, removing audio sync during call (was each 20 secs), audio sync at the tcp interfacing with the skype client (reading more than 20ms worth) (r:891015e6)
	mod_skypopen: fixed a demented bug (incrementing a variable zeroed in the same loop) maybe responsible for moh sputtering under load on virtual machines (r:43eeeb82)
	mod_skypopen: avoid accumulating delay on VMs, better debug logging (r:1b4c78bf)
	mod_sms: add new chatplan concept and mod_sms.  Apps for chat messages: copy new base freeswitch.xml and chatplan dir if you are upgrading on existing config base (r:7333d46d)
	mod_sms: fix "format literal and no arguments" warning (r:a0e91001)
	mod_sms: allow chatplan apps to have null args (r:4feb26d3)
	mod_sms: only ignore events if you matched something on a cp (r:a14b20af)
	mod_snapshot: fix bad codepaths in mod_snapshot (r:844ac220)
	mod_sndfile: Add support for .alaw and .ulaw to mod_sndfile (r:facf09b8/MODFORM-41)
	mod_sndfile: return break in mod_sndfile when seek returns failure (r:564dc7e4)
	mod_snmp: initial checkin of mod_snmp (r:6e2b1bd3)
	mod_snmp: fix segfault when getting channel list (r:b6b4e6b5/FS-3114)
	mod_snmp: fix segv when snmpwalking ringing channels, fix segv in snmp getBulkRequest (r:9c4c1e81/FS-3120)
	mod_sofia: Send SIP MESSAGE to unregistered users by prefixing sip: to user@domain
	mod_sofia: fix callee being updated with callee information
	mod_sofia: set appearance-index in update statement for SLA
	mod_sofia: add killgw _all_ to delete all gws
	mod_sofia: Fix Callee is updated with Callee display information (FSCORE-586)
	mod_sofia: add RTCP separate on audio and video and add passthru 
	mod_sofia: add outbound_redirect_fatal channel variable
	mod_sofia: fix RTCP port update on reINVITE (FSCORE-594/MODSOFIA-74)
	mod_sofia: don't put ':' in to user agent string (MODSOFIA-71)
	mod_sofia: add failed call statistics to gateways (MODENDP-238)
	mod_sofia: add 'sofia_count_reg' api function
	mod_sofia: add EXPSECS time till reg expire and total to status return
	mod_sofia: fix core in sofia_presence (MODENDP-304)
	mod_sofia: replace don't bridge when parsing replaces header on invite (also hangup)
	mod_sofia: add disable_hold var or disable-hold profile param
	mod_sofia: allow sendonly mode in IVR where caller listens only (no input) (MODENDP-303)
	mod_sofia: sip_acl_authed_by and sip_acl_token vars to tell when you are authed by an acl
	mod_sofia: fire an event for gateway ping
	mod_sofia: initial handling of udptl and t.38 re-invite
	mod_sofia: Implement "redirect server" functionality with 300 Multiple Choices (r:e15abcf9/BOUNTY-18)
	mod_sofia: allow video negotiation on re-invite (r:be92e5d/SFSIP-211)
	mod_sofia: use rfc recommended default session timeout of 30 min according to RFC 4028 4.2 (r:52cd8cdd/MODSOFIA-76)
	mod_sofia: add sip_force_audio_fmtp (r:6360264f)
	mod_sofia: add sip_copy_multipart to work like sip_copy_custom_headers (r:a291af57)
	mod_sofia: Rename sofia_glue_get_user_host to switch_split_user_domain and move to switch_utils. To allow use by other modules. (r:3f7cafd7)
	mod_sofia: allow the profile gateway config to set sip_cid_type for each gateway (r:0152706f/BOUNTY-19)
	mod_sofia: Adding subject to SEND_MESSAGE (r:2e347c93)
	mod_sofia: add multiple rtp-ip support to sofia profiles add extra rtp-ip params to a profile to add more ip which will be used round-robin as new calls progress. (r:22569d4a)
	mod_sofia: Fix segfault (r:72be253d/MODSOFIA-83)
	mod_sofia: Add openssl build support to windows - no external build support needed (step 1 - not hooked up yet) vs2008 pro+ only (r:b0de3585/MODSOFIA-92)
	mod_sofia: REFER: to-tag and from-tag should be set other way around when other (bridged) channel is incoming. (r:92d324d3/MODSOFIA-91)
	mod_sofia: fix 302 to hangup in the two cases where switch_ivr_transfer is used and not in the case when it should carry on and follow the redirect (r:00b51403)
	mod_sofia: Remove OPENSSL_USE_APPLINK - not needed (r:437c7805/MODSOFIA-92)
	mod_sofia: Send Instant Messages To All Endpoints Registered to Targeted Extension (r:96b790fa/BOUNTY-20)
	mod_sofia: increase sps during recovery (r:f1aead31)
	mod_sofia: Forward unsolicited MWI nofity (r:5481d9a9/MODSOFIA-86)	
	mod_sofia: Add a quick fix for basic Polycom presence support.  A more sane solution need to be implemented (r:a55b9d07)
	mod_sofia: Unify gateway printing between 'sofia xmlstatus gateway' and 'sofia xmlstatus gateway <gatewayname>' (r:37c22467)
	mod_sofia: Fix memleak and mwi event not generated on first register (r:04b9b3e2)
	mod_sofia: when getting presence with no payload consider it an extension to the expires time in the dialog (r:70331e88)
	mod_sofia: don't put blank 'version' attr in dialog-info packets (r:749dc864)
	mod_sofia: speed up db action in sofia recover (r:8114b3f1)
	mod_sofia: Support display updates for Cisco SIP endpoints (tested on SPA series) (r:ac205288/FS-884)
	mod_sofia: dont put an rpid in 183 or 200 if pass-callee-id is false (r:86de47ff)
	mod_sofia: improve sofia recover in some nat cases (r:4526ba30)
	mod_sofia: edge cases for sofia recover (r:646a5609)
	mod_sofia: Correct the order what param and variables are overriding them self in user/group/domain (r:5a6f0f5c)
	mod_sofia: include accumulated stats from rtcp into vars (r:d5ff3e04)
	mod_sofia: make sure hold-related code is skipped 100% with disable-hold set (r:403bf6af)
	mod_sofia: make force-subscription-expires only work on nonzero expire deltas, 0 means unscubscribe (r:b7751868)
	mod_sofia: presence tweaks and addition of all-reg-options-ping which is like nat-options-ping only for every registered host (r:04b52156)
	mod_sofia: If sip_invite_domain is used lets use it for rpid_domain no matter what because I know best if I set it (r:8726104a)
	mod_sofia: add inline lists for tab complete db using ::[a:b syntax (r:445731ee)
	mod_sofia: add sofia profile <profile> gwlist up|down to list up or downed profiles for feeding into mod distributor to exclude dead gateways (r:0477cb67)
	mod_sofia: add 'sofia global siptrace on' so we don't have to always teach people to enable sip trace on each profile (r:09fa6678)
	mod_sofia: fix seg on subscribe with no contact host (r:c236541e)
	mod_sofia: fix typo and printf specifier resulting in incorrect output of call counts on profiles and gateways (r:29ea6e29)
	mod_sofia: fix t38 passthru when port changes on re-invite (r:72baaf6d)
	mod_sofia: let ~ signify that multipart content will contain headers (r:3548168d)
	mod_sofia: Fix rash with rxfax when no remote host (r:a9446ac1/FS-677)
	mod_sofia: Handle incorrectly formatted T.38 booleans (r:8f731f42/FS-957)
	mod_sofia: fix crash in sofia_reg_find_gateway_by_realm__ (r:721c8019/FS-488)
	mod_sofia: Handle 301 moved permanently. (r:ba59c51d/FS-2739)
	mod_sofia: don't passthru when its proxy media, bypass media or there is no rtp session, fixes seg (r:45e2b99d)
	mod_sofia: improve video support for new polycom phones (r:84a383fe)
	mod_sofia: Forward unsolicited MWI nofity (r:e946da9a/FS-861)
	mod_sofia: Support display updates for Cisco SIP endpoints (tested on SPA series) (r:6937ca39/FS-884)
	mod_sofia: BLF compliance with RFC-4235: dialog-info 'version=' field is reset to 0 on every new call instead of being incremented (r:589502d3/FS-2747)
	mod_sofia: fix parsing of sofia tracelevel param, moved param from profile params to global_settings as its global, and it only worked on reparse before anyways.  Please correct any documentation on this issue on the wiki (r:82c4c4cc/FS-523)
	mod_sofia: fix nat acl count check to check against the number of nat acls (r:e11550e7/FS-502)
	mod_sofia: add sofia_glue_find_parameter_value function to get a specific value from a url params string (r:c701d41c)
	mod_sofia: Sofia SIP Stack Lockup (r:8f13eb89/FS-2762)
	mod_sofia: ix memory leak caused by regression from FS-2747 (r:6c4cb07b/FS-2747)
	mod_sofia: Set SDP proper connection type of &quot;c=&quot; field to IP6 or IP4 when IPv6 called IPv4 (r:14361c09/FS-620)
	mod_sofia: FS wrongly assumes NAT on Cisco 7941G (r:42f534fa/FS-2773)
	mod_sofia: sofia_reg.c doesn't pass profile name into query, also, typo with all-reg-options-ping (r:6772c795/FS-2787)
	mod_sofia: adjust sql stmts in presence to allow even non-registered entities to be tracked (r:4e0399d0)
	mod_sofia: dont update display to ring when call is hungup in pidf presence (r:36851a90)
	mod_sofia: 1) Add force-publish-expires to set custom presence update expires delta (-1 means endless) 2) Check how many users are registered when receiving a PUBLISH AND Multiple Registrations is enabled: if there is more than just 1 AND you are sending a offline message: skip publishing it to everyone to prevent clients from thinking themselves has gone offline. (r:fd1736b3)
	mod_sofia: profile param ignore-183nosdp, chanvar sip_ignore_183nosdp FS-1978 (r:59d3b84d/FS-1978)
	mod_sofia: fix race in codec failure condition, then fix bug in sdp parsing (likely a regression from recent codec changes) to never have the problem in the first place so you are double-protected (r:19325c43)
	mod_sofia: fix mem leak (r:1970ec1d/FS-2810)
	mod_sofia: parse static route in sip uri in notify by event (r:35676e7e)
	mod_sofia: add support for NDLB-force-rport=safe param that does force-rport behavior only on endpoints we know are safe to do so on.  This is a dirty hack to try to work with certain endpoints behind sonicwall which does not use the same port when it does nat, when the devices do not support rport, while not breaking devices that acutally use different ports that force-rport will break (r:fc4d290c)
	mod_sofia: add separate reg timeout from retry sec (r:e5b891ee)
	mod_sofia: fix display of timeout (r:2043d5a)
	mod_sofia: fix missing name and potential segfault in gateway status (r:40ac860a)
	mod_sofia: Add missing RTP info for early SDP in bypass media (r:10119e9e/FS-2824)
	mod_sofia: add manual_rtp_bugs to profile and chan var and 3 new RTP bugs SEND_LINEAR_TIMESTAMPS|START_SEQ_AT_ZERO|NEVER_SEND_MARKER (r:b278dd23)
	mod_sofia: apparently some sip device vendors did not read the RFC (who knew?) adding verbose_sdp=true var to add needless a= lines for standard iana codecs that explicitly do not require them (r:6c4f49a8)
	mod_sofia: Fix registering a gateway, sofia always places a Via header with ext-sip-ip, even if this gateway is local (r:cf398e1a/FS-535)
	mod_sofia: add presence-probe-on-register sofia param to send a probe on register instead of presence to deal with some broken phones and add some general improvements to allow multi homed presence (r:14394994)
	mod_sofia: Fix issue when fs_path is used so we pick the correct media IP in our outbound invite this was soemthing that wouldn't work correctly over ATT on the iphone. (r:a669f76f)
	mod_sofia: Default T38 Options (r:92f43440/FS-2892)
	mod_sofia: Fix wrong IP in VIA and contact HEADER for MESSAGE method while fs run in private network (r:59ea4a1b/FS-2886)
	mod_sofia: SIP-header History-Info might exist multiple times, but only last header is exposed as a channel variable (r:8cf15012/FS-2881)
	mod_sofia: Add support to reboot Yealink phone remotely (r:fdc31908/FS-2897)
	mod_sofia: Add reuse-connections sofia profile param to allow users to turn off TPTAG_REUSE, thus not re-using TCP connections (r:98ed05cc)
	mod_sofia: Make sofia recover also work on custom uuid (r:3a645dee/FS-2913)
	mod_sofia: remove check for va_list completely in sofia since i don't even think it happens ever (r:dfecc914)
	mod_sofia: have mod_sofia always elect to be the session refresher so we know it will work, also make the session-expires set to 0 imply 100% disabled session timers (r:321013ef)
	mod_sofia: Do not set nat mode when the device's network_ip is within the acl also so if your FS is behind nat and your phone is too then it will still make the right decisions (r:6c6eab8c)
	mod_sofia: prevent race on codec change mid-call (r:668763f4)
	mod_sofia: improve fail2ban logging (r:f4d52d4c/FS-2943)
	mod_sofia: refactor sofia_contact to try the profile_name first then the domain to resolve the profile then fall back to querying every profile to reduce confusion with multi-homers (d'oh) also special profile name * will force a search-all situation (r:81608da0)
	mod_sofia: support allowing pidf-ful presence clients to share the same account and 'appear offline' without influencing each other =/ also refactor the contact generation string based on nat into a helper function (r:97a68c50)
	mod_sofia: gateway not identified when extension-in-contact is set (r:7b289941/FS-502)
	mod_sofia: Fix erroneous error log on SQL statement (r:2c595a6c/FS-2961)
	mod_sofia: Fix routing behavior of inbound calls from gateways that only match gateway based on the gw request uri param (r:0132cd3f)
	mod_sofia: don't say we are not for polycom phones (safe rport) when its not really nat (r:9462f53c)
	mod_sofia: Set route header as a var on channel like Diversion header (r:d41e6498)
	mod_sofia: fix seg related to ptime mismatch + CNG + PLC (if you ever get purple ptime mismatch warnings you want this patch) (r:54de293b)
	mod_sofia: be more iOS friendly when using TCP or TLS because the phone never sleeps thus drains the battery (r:159ae989)
	mod_sofia: add send-presence-on-register (true|false|first-only) param to sofia and api command sofia global debug [presence|sla|none]
	mod_sofia: disable media timeout when encountering a recvonly stream (r:029d68ce)
	mod_sofia: fix sofia flush_inbound_reg to work when @domain is given (r:68bf642c)
	mod_sofia: fix session timer failure when freeswitch is generating the sdp and there are enough dynamic codecs enabled to conflict with the 2833 pt (4 by default) (r:018a3800)
	mod_sofia: Places ;fs_path= within the contact string <...> when using NDLB-connectile-dysfunction-2.0, instead of just appending to the end of the contact string. (r:afc02747/FS-2989)
	mod_sofia: Fix handling SUBSCRIBE to &quot;park+&quot; fifo, the NOTIFY data was not being generated from mod_fifo data. (r:3dd9d5c0/FS-3007)
	mod_sofia: fsctl pause improvements (r:008e527c/FS-3012)
	mod_sofia: only pass publish on when you have a subscription (r:85913b70)
	mod_sofia: sip_codec_negotiation to override inbound-codec-negotiation setting (r:74a0cfd1/FS-3027)
	mod_sofia: fix uuid_jitterbuffer edge case debugging a non-existant jb causing a seg (r:88d410d3)
	mod_sofia: tell rtp stack about what remote payload type to expect when the receiving end follows the stupid SHOULD as WONT and sends a different dynamic payload number than the one in the offer (r:c565501f)
	mod_sofia: rip off the fs_ args on message like we do in SEND_MESSAGE (r:b7fd81de)
	mod_sofia: use the correct URI on endpoints behind nat (r:5f2857b8)
	mod_sofia: put transport in the request uri on outbound registers if register_transport is set and proxy does not already contain a transport param (r:4b62ff79)
	mod_sofia: pass custom headers backwards over sofia (r:13dc6058)
	mod_sofia: fix profile SIP INFO dtmf not working (r:4c4ca08d)
	mod_sofia: Fix SIP INFO DTMF (r:39ff78bf/FS-3078)
	mod_sofia: contact-params should not be set if the string is empty (r:06988e1a/FS-3084)
	mod_sofia: segfault with sofia_contact when invalid parameters are given (r:4e60f14a/FS-3072)
	mod_sofia: Fix minupnpd nat_map updated IP not getting set in SIP profiles (r:e7acd4d1/FS-3054)
	mod_sofia: add sip_execute_on_image variable similar to execute_on_answer etc so you can run t38_gateway or rxfax etc when you get a T.38 re-invite but no CNG tone or you want to ignore the tone and only react when getting a T.38 re-invite (r:53fc3f7f)
	mod_sofia: add sip_jitter_buffer_during_bridge which you can set to true to keep a jitter buffer on both ends of the call when you are NormT (r:01073a79)
	mod_sofia: fix race condition in sofia recover for atom processors (r:3eeb4995/FS-3117)
	mod_sofia: improve codec ordering in ep_codec_string (r:8fe24a29/FS-3121)
	mod_sofia: Send BYE to endpoints that lose race even if they answered  (r:8c3651fa/FS-640)
	mod_sofia: do not renegotiate codecs on hold re-invites (r:bfd0ba97)
	mod_sofia: add rtp-notimer-during-bridge (alternative to rtp-autoflush-during-bridge (r:2a35dfb5)
	mod_sofia: send another presence event on calls that were cancelled from LOSE_RACE to fix winnable race in Broadsoft SCA (r:59f6654e)
	mod_sofia: pass header in X-FS headers on attended transfer CID update to indicate specific situation to flip callee/caller id when targeting a 1 legged call (r:24a97292)
	mod_sofia: change text of error message to be more descriptive (r:4c435ec5)
	mod_sofia: Correct a problem where restarting profile would cause some profile hash entry to remain. (r:81bfe435)
	mod_sofia: New Sofia API to look up the username of a given user (r:7556ec57/FS-3187)
	mod_sofia: sip_authentication was not cleared after nonce expired -caused sofia_reg_internal.db grow bigger and bigger with time (r:c735e28a/FS-3190)
	mod_sofia: pass failure across in T.38 passthru mode (r:31273b42)
	mod_sofia: auto-aleg-full and auto-aleg-domain for from_domain field in gateway (r:fda2283b)
	mod_sofia:  After further review I can concede the point that we should always say partial considering how we do things.  With this commit we should at least be sending separate partial updates for each existing dialog to everyone with a subscription.  If we need to introduce more data, consolidate them etc.  We need to do it in small chunks and keep things sane. (r:7eae7f37/FS-2877)
	mod_sofia: Fix:Attended transfer with bypass media fails in various ways (r:4b706dac/FS-3227)
	mod_sofia: SO, If the RFC told you to jump off a cliff......? (r:07b9186d/FS-3226)
	mod_sofia: Don't assume incoming "gw" contact param is valid (prevent possible DoS) (r:2b6f7070/FS-3244)
	mod_sofia: offer both avp and savp when using srtp (r:5857495e)
	mod_sofia: fix race in sla (r:a4ed829d)
	mod_sofia: Fix segfault when no channel is present and need to print err msg (r:ce5c8462/FS-3236)
	mod_sofia: don't nat map on loopback addrs (r:e70af1f8)
	mod_sofia: Add reporting chan vars: sip_bye_content_type and sip_bye_payload (r:2d856f8f/FS-3276)
	mod_sofia: Add "tel:" patch - thanks jaybinks (r:154731a7)
	mod_sofia: fix rare t38 gateway issue (r:123eaa52)
	mod_sofia: fix sofia presence with dial does not maintain version number correctly (r:3ebd173c/FS-3307)
	mod_sofia: fix One way audio problem from B-leg of the call on Session Refresh and HOLD if B-leg is using a dynamic payload type (r:66d16d17/FS-3270)
	mod_sofia: chat API issue: dup_dest was being overwritten by switch_split_user_domain (r:765908f3/FS-3152)
	mod_sofia: Reformat sofia usage string and make it a static const char[]. (r:812fd727)
	mod_sofia: Add channel variable deny_refer_requests to make it possible to deny REFER requests (r:9e12983f/FS-3100)
	mod_sofia: Fix TLS crash when NAT configured w/o actual external IP addr (r:64f8ad3f/FS-3324)
	mod_sofia: only accept info dtmf when its configured to (r:51c21580)
	mod_sofia: add support for 3pcc-proxy when in bypass media. (r:68c389df/FS-3326)
	mod_sofia: release rwlock on error (r:0675b59b/FS-3321)
	mod_sofia: Mask remote party identity in SIP presence if channel var presence_privacy=true (r:8d8e5a23)
	mod_sofia: add check_sync to sofia cli (like flush_inbound_reg without the unreg) (r:079f4845)
	mod_sofia: pop ::<profile_name> off the domain name in mwi events to hint at the profile (r:e2ed8c08)
	mod_sofia: dig into the database to figure out what profile to send mwi on when they are not willing to alias the domain to the profile =/ (r:b14340a5)
	mod_sofia: add mutex around gateway access on per-profile basis and token based access to global profiles to prevent hanging on to the hash mutex while doing sql stmts which may cause issues/slowdowns (r:9df8169d)
	mod_sofia: add parallelism to sofia by offsetting sip messages to the concerned sessions and using multiple queue threads for message handling (r:fb68746e)
	mod_sofia: removed the vid refresh thing (r:49e52b4c/FS-3362)
	mod_sofia: add sip_liberal_dtmf chanvar and liberal-dtmf profile param to use the maximum methods of DTMF avoiding sticking to the spec which leads to incompatability (r:bc7cb400)
	mod_sofia: support final response in response header passing (r:acd0898e)
	mod_sofia: Fix failure to fall back to g.711 when t.38 attempt fails (r:07a79752/FS-3214)
	mod_sofia: pop ::<profile_name> off the domain name in mwi events to hint at the profile (r:e2ed8c08)
	mod_sofia: dig into the database to figure out what profile to send mwi on when they are not willing to alais the domain to the profile =/ (r:b14340a5)
	mod_sofia: Fix 3pcc codec negotiation issue with bypass_media (r:c5a2275f/FS-3340)
	mod_sofia: re-add not-so-superfluous-after-all NUTAG_AUTOANSWER(0) (r:927fde18/FS-3349)
	mod_sofia: add mutex around gateway access on per-profile basis and token based access to global profiles to prevent hanging on to the hash mutex while doing sql stmts which may cause issues/slowdowns (r:9df8169d)
	mod_sofia: add parallelism to sofia by offsetting sip messages to the concerned sessions and using multiple queue threads for message handling (r:fb68746e)
	mod_sofia: Fix sofia hang on shutdown (r:3be64cbf/FS-3354)
	mod_sofia: remove vid refresh from SDP on declined video connection (r:49e52b4c/FS-3362)
	mod_sofia: fix small mem leak in sofia (r:6f62f391/FS-3386)
	mod_sofia: add proxy tag to UPDATE packets if it was set by INVITE (r:e6605139)
	mod_sofia: resolve attended transfers, it fails to parse the Replaces when encoded (r:d9bbf129/FS-3304)
	mod_sofia: if user has set presence_id, don't override it (r:7cdc8342)
	mod_sofia: only list real profiles not aliases in presence code (r:f9969f38)
	mod_sofia: Fix 200 OK not passed for Callee-Initiated ReInvite for T.38 (r:b2299035/FS-3421)
	mod_sofia: destroy nh if SIP transaction terminated by a 488 (r:a0cec8ab/FS-3444)
	mod_sofia: use register contact to determine proper contact in 200 ok response to register (r:f9612fec)
	mod_sofia: add NDLB-allow-nondup-sdp to indicate you want to parse a differnt sdp in 200 ok from 1xx (previous default) this is a RFC violation so I decided not to support it by default anymore.  Enable this if you want that broken behaviour (r:3f489a2a)
	mod_sofia: add homer capture hooks to mod_sofia (r:98473085)
	mod_sofia: sdp_m_per_ptime is now implied to be true, if you don't like this set it to false but its going to be undefined behaviour.  This basically means if you call in with ptime 30 then you have a bunch of ptime 20 codecs in your outbound list that there will be one m= line with 30 and the original inbound codec and more m= lines for each discinct ptime in your list.  This is, of course, will depend on disable_trancoding or absolute_codec_string as well (r:56d67ead)
	mod_sofia: filter re-transmission of extra SIP headers (r:9e399c19/FS-3439)
	mod_sofia: Fix RTP handling bug to allow goofy & undefined behavior (r:77413ba9/FS-3451)
	mod_sofia: don't allow auto answer on an non-outbound call (r:61ee7fdc)
	mod_sofia: use the call_id of the original register in the unsolicited notify for MWI (r:53b0ecce)
	mod_sofia: Fix caller ID name on bridged appearance (r:7efa4fb2/FS-3532)
	mod_sofia: speed up restart speed of profiles (r:fb5f29c2)
	mod_sofia: this is actually compliant when mixing ptimes in the same sdp but since iLBC uses its own fmtp for ptime I will add this patch to make it beleive its 20 for the sake of arguement.  If you have any other problems with this, set the channel or global variable sdp_m_per_ptime=false to completely disable the default correct behaviour (r:247537a9/FS-3545)
	mod_sofia: I missed a few more spots to hack in the exception for iLBC, (thanks for marring my code iLBC ppl) it should work as expected now even with the m_per_ptime on (r:83a78fbf/FS-3545)
	mod_sofia: don't turn X-FS- headers into variables, they are reserved for FS specific communication and should not be passed on (r:7d399cce)
	mod_sofia: This patch will probably make it work but the bug is actually in the phone, the patch is simply tolerating the bad behaviour.  You are correct about the a=sendonly missing, this was fixed in a later revision of the polycom firmware.  I suggest that even if this patch works, that you update your phones to a newer firmware, preferably the most recent. (r:7acddfac/FS-3549)
	mod_sofia: add auth username to unreg event (r:1b9b3456)
	mod_sofia: fix wrong media ip in recover data issue (r:5154b881)
	mod_sofia: fix missing ACK that causes Polycom failure (r:408adb8d/FS-3558)
	mod_sofia: use bridge_to instead of signal_bond for recovering bridge (r:f4794620)
	mod_sofia: save telephony event data for recovery (r:4d1a76ee)
	mod_sofia: tighten up the parsing of these boolean vals issue in t38 sdp (r:626b5cb2/FS-3571,FS-3442,FS-957)
	mod_sofia: resolve race condition caused by unresponsive host when unregistering the gateway. (r:607c112f/FS-3583)
	mod_sofia: only require user and pass in gateway when register is true (r:9e4ca2c2)
	mod_sofia: Remove arbitrary 100 ACL limit (r:dd5188f3/FS-3605)
	mod_sofia: add t38_pass_broken_boolean to pass the broken boolean behaviour over to the other side of the call when detected (r:afd0e1fc)
	mod_sofia: Update handling of sub/notify (fixes broken Snom BLF) (r:51c22811/FS-2877)
	mod_sofia: Don't add Contact header to MESSAGE requests, per RFC3428 (libsofia still does it a bit) (r:1f6670e7/FS-3628)
	mod_sofia: add presence_map (r:4ee1722f)
	mod_sofia: add presence_map config (r:48416707)
	mod_sofia: add lookup params to presence_map (r:ea7f6a18)
	mod_sofia: add missing mod_sofia indexes (r:40df8d65)
	mod_sofia: When doing direct pickup send CANCEL with reason="call completed elsewhere" (r:6cd6a719/FS-3634)
	mod_sofia: Handle "isup-oli" in SIP From: field (similar to "gw" handling) (r:0ed54079/FS-3663)
	mod_sofia: Fix NOTIFY expiry time to conform to RFC 3265 (r:42c69c04/FS-3685)
	mod_sofia: Preserve VIA headers in sofia recovery (r:5a6231b6/FS-3686)
	mod_sofia: Add respond 503 "Maximum Calls In Progress" param for incoming SIP OPTIONS when busy (r:a891514c/FS-3697)
	mod_sofia: don't list all registrations when doing "sofia status profile foo" (use 'reg' option instead) (r:00e381f1/FS-3699)
	mod_sofia: Fix typo and possible memory corruption in sofia presence (r:ff910276/FS-3706)
	mod_sofia: comment out optional Require header from re-invites for the sake of interop with testy t.38 terminals (r:58c3c3a0)
	mod_sofia: Set reg and invite auto failures to loglevel debug10 to satisfy Fail2ban (r:35112f22/FS-3586)
	mod_sofia: Fix segfault on malformed NOTIFY (r:82807998/FS-3718)
	mod_soundtouch: updated soundtouch to library 1.5.0 to fix gcc > 4.3 incompatibilities (r:dfb5c629)
	mod_soundtouch: Update soundtouch to 1.6.0 to fix FS-3634 (r:d8ae59fd/FS-3634)
	mod_spandsp: initial checkin of mod_fax/mod_voipcodecs merge into mod_spandsp (r:fa9a59a8)
	mod_spandsp: rework of new mod_spandsp to have functions broken up into different c files (r:65400642)
	mod_spandsp: improve duplicate digit detection and add 'min_dup_digit_spacing_ms' channel variable for use with the dtmf detector (r:eab4f246/FSMOD-45)
	mod_spandsp: add start_tone_detect/stop_tone_detect app and api commands for tone and cadence detection (r:a6e65147/MODAPP-378)
	mod_spandsp: Fix mod_spandsp receive t38 fax error in windows7 (r:fca93f29/MODAPP-443)
	mod_spandsp: Moved spandsp to a more recent version. A huge number of little changes occur here, as recently spandsp lost all the $Id$ entries the source files had for the dark old days of CVS (r:f029f7ef)
	mod_spandsp: move app flag into 'T38' namespace for the sake of housekeeping (r:0d0b4b43)
	mod_spandsp: make t38 terminal mode more reliable (r:83da7bd3)
	mod_spandsp: deadlock in mod_spandsp (mod_spandsp_fax.c) (r:b02c69bb/FS-1690)
	mod_spandsp: T.38 reINVITE glare condition causes FAX processing to stop. (r:04aa7ef9/FS-1682)
	mod_spandsp: improve nat handling when using stun or host as ext-rtp-ip (r:03e74c51/FS-526)
	mod_spandsp: Fire event when fax is finished; indicates result of fax attempt (r:314a2a1e/FS-3004)
	mod_spandsp: new option to set sip_execute_on_image to 't38_gateway self nocng' this should skip the tone detection adn go right into the gateway mode so you should be able to do only this and have it work based on remote re-invite (r:9227b538/FS-3252)
	mod_spandsp: additional fix to this bug and add better fax detect code to mod_spandsp (r:7fe313cf/FS-3252)
	mod_spandsp: Fire event when fax finishes indicating result (r:a57336ba/FS-3004)
	mod_spandsp: Prevent hung chans on fax errors (r:789a9ce8/FS-3213)
	mod_spandsp: add more fax event information (r:0555b702/FS-3345)
	mod_spandsp: fix memory issue in spandsp_tone_detect (r:8793c2ed)
	mod_spandsp: add proper tone detect stop (r:8beb10d2/FS-3367)
	mod_spandsp: add more fax event information (r:0555b702/FS-3345)
	mod_spandsp: fix memory issue in spandsp_tone_detect (r:8793c2ed)
	mod_spandsp: Tweak link order of some libs to fix build on gcc platforms that have ld flag "--as-needed" as default. (r:0db88b59/FS-2873/FS-2874)
	mod_spandsp: Fix spandsp_start_fax_detect timeout issue (r:90a6e78d/FS-3619)
	mod_spandsp: Add tone_type param to start_fax_detect to handle outbound fax detection (r:d6d18748/FS-3624)
	mod_spidermonkey: allow vars to be set containing vars from languages (r:5cd072a3)
	mod_spidermonkey: fix seg in js hangup (r:7d554c11)
	mod_spidermonkey: Fix mod_spidermonkey build on FreeBSD, (Undefined symbol PR_LocalTimeParameters). (r:3edb8419)
	mod_spidermonkey: Add session.ringReady() to check for CF_RING_READY (r:7386b9f8)
	mod_spy: add support for loopback endpoint (MODAPP-416)
	mod_spy: fix crash when session can't be located (r:c4154633/FS-2929)
	mod_timer_fd: external timerfd module by Timo Ter?s (r:48b11935)
	mod_timer_fd: add timerfd support to the core for now you must enable it in switch.conf.xml with the param enable-softtimer-timerfd=true later if it proves to work well we can make it on by default, please test if you have a new kernel that supports this option kernel >= 2.6.25 and libc >= 2.8 (r:10174ea6)
	mod_tts_commandline: fix core dump, temp file problem. flush can be called several times (FSMOD-35)
	mod_unimrcp: fix fortify findings for mod_unimrcp (r:336f0b4e/FSMOD-67)
	mod_unimrcp: fix truncated TTS (r:e37dd41e/FS-3201)
	mod_unimrcp: Destroy schannel only *after* cleanup of its contents is done (r:0f17bcc5)
	mod_unimrcp: add locking to mrcp dtmf generator (r:f5704114/FS-3163)
	mod_unimrcp: check for NULL recog_hdr (r:478d5186/FS-3247)
	mod_unimrcp: Wait for unimrcp lib to timeout requests (r:ee176092)
	mod_valet_parking: add event data to valet parking hold event
	mod_valet_parking: add event for Valet Parking action exit
	mod_valet_parking: pass hold class on transfer (r:76a065ec)
	mod_valet_parking: add valet_announce_slot variable (r:293d7254)
	mod_valet_parking: make valet parking reserve a space for 10 seconds to allow time for an attended transfer switcharoo (r:308f44af)
	mod_valet_parking: refactor timeout tracker for valet (r:3514c780)
	mod_valet_parking: valet_info now returns uuid properly (r:34ddeb75/FS-3613)
	mod_valet_parking: add presence to mod_valet_parking subscribe to the lot name for general stats or individual slots to monitor with park+ prefix *note* this used to be used in mod_fifo which now will use queue+ going forward (r:9daa42c1)
	mod_valet_parking: lower token freq to 5, put it in a define and have more obvious warning about full lots (r:2f786a07)
	mod_valet_parking: make auto unpark choose the one waiting the longest instead of the earliest ext val (r:00cf5aed)
	mod_voicemail: Fix vm_prefs profile lock (MODAPP-417)
	mod_voicemail: add 'vm-enabled' param (default true)
	mod_voicemail: fix vm msg being deleted when pressing key to forward to email (MODAPP-403)
	mod_voicemail: make voicemails use the uuid of the channel who recorded it when applicable (r:98a5a30a)
	mod_voicemail: user unable to play or delete voicemail via web API (r:b5205c0b/MODAPP-447)
	mod_voicemail: Allow to forward a message or send it via email key during the playback of the recording, not just when the menu is playing. (r:83aeda79)
	mod_voicemail: fix vm_inject to a group and change syntax for sending to a whole domain to domain= for clarity sake (r:f30a1cc6)
	mod_voicemail: add quotes to vm_cc command generated internally to escape spaces in the caller id name (r:5f012813)
	mod_voicemail: Play caller id of callee prior to playing a vmail (r:e7b97907/FS-2719)
	mod_voicemail: FS-1776 Add support for per user operator exten override param vm-operator-extension (r:df5b3498/FS-1776)
	mod_voicemail: Set email address from within directory (r:83ce26b2/FS-2972)
	mod_voicemail: add events for record/change greeting and record name (r:54421f59)
	mod_voicemail: let vmain-key and operator-key be set empty (r:de49305a)
	mod_voicemail: add ability to jump to a specific message (r:0f8fb4b1)
	mod_voicemail: vm-skip-instructions param in xml directory to disable instructions how to record a file (r:ed7e1f39)
	mod_voicemail: Implement 10 new standard api function call that allow you to control fs voicemail storage system.  The goal is to have a standard API set for any additional storage system we wish the voicemail to run off.  Current list of added api name are : vm_fsdb_msg_count, vm_fsdb_msg_list, vm_fsdb_msg_get, vm_fsdb_msg_delete, vm_fsdb_msg_undelete, vm_fsdb_msg_purge, vm_fsdb_msg_save, vm_fsdb_pref_greeting_set, vm_fsdb_pref_recname_set, vm_fsdb_pref_password_set. (r:1f4cb488)
	mod_voicemail: Adding a new voicemail fsdb api vm_fsdb_auth_login that does basic login authentication for a user (r:bfdfac5e)
	mod_voicemail: Fix vm to email dial 8 option (r:8592b6d9/FS-3382)
	mod_voicemail: Add 2 new profile settings, db-password-override and allow-empty-password-auth.  By default, they have value of their previous behavior.  If db-password-override=true, the db password will only be used if present, if not present fallback to the xml config file vm-password.  If allow-empty-password-auth=false, it will disable login via a authentication method if there is no password set in the user account (This wont affect voicemail_authorize=true login). (r:a9db642a)
	mod_voicemail: remove pointless update_mwi() in vm_list api command (r:b952b2b2)
	mod_voicemail: add message_len to output of vm_list api command (r:77c5000d)
	mod_voicemail: use vm_email as notification address if vm_notify_email isn't set (that behavior was in voicemail_leave_main but not in deliver_vm) (r:8974f9d6)
	mod_voicemail: better fix for voicemail email key match (r:aff4bcbe/FS-3080)
	mod_voicemail: Prevent rewind key from breaking out of message playback (r:923a104b/FS-3637)
	mod_voicemail: add variable vm_auth_only or option auth_only to voicemail app so you can use it as a pin checker (r:3051480d)
	mod_voicemail: fix wrong message count with saved urgent messages (r:54b4b088)
	mod_voicemail: add voicemail_formatted_caller_id_number param for templates in vm (r:94b9cc0f)
	mod_xml_cdr: add force_process_cdr var to process b leg cdr on a case by case basis when b leg cdr is disabled (XML-17)
	mod_xml_cdr: add leg param to query string (XML-24)
	mod_xml_cdr: fix locked sessions (XML-26)
	mod_xml_cdr: fix minor memory leaks and config bug (r:19253d83/MODEVENT-62)
	mod_xml_cdr: Fix prefix-a-leg not respected for url submission (r:ea9021a2/FS-2998)
	mod_xml_cdr: Fix delay to 5 sec from 5000 sec (r:34a38009/FS-2815)
	mod_xml_cdr: Accept all 2xx messages, not just "200" (r:d39b7c6b/FS-3593)
	mod_xml_rpc: Fix crash if unauthorized XML RPC is attempted (r:9835395c/FS-184)
	scripts: added honeypot.pl and blacklist.pl which add extra SIP security options (r:b6a81ba7)
	scripts: do simple verification to make sure we are getting IP addresses from VoIP abuse blacklist (r:b0049160)
	scripts: add_user - cmd line utility that lets admin create new users very easily. (r:ec8f2c2b)
	sofia-sip: fix null derefernce segfault in soa (r:f356c5e6)
	sofia-sip: extend timeout for session expires on short timeouts to be 90% of timeout instead of 1/3 to handle devices that do not refresh in time such as polycom (r:a7f48928/SFSIP-212)
	support: update fscore_pb to work with git (r:8f67e93a)
	tools: Add fs_encode tool (r:89b17601)
	tools: Add randomize-passwords.pl script to main tree (r:5e6123ef)
	tools: Change logger.pl host flag to -H from -h (conflicted w/ -h for help); add -H/--host to usage (r:73ca862c)
	tools: Add sound_test.lua utility script (lets you listen to all the sound files in a particular rate/type combo (r:1a71dbf2)
	
freeswitch (1.0.6)

	all: migrate to git
	core: add ... and shutdown as a fail-safe when no modules are loaded
	core: fix high mem usage during shutdown
	
freeswitch (1.0.5)

	all: run indent on the whole tree and update copyright dates in prep for 1.0.5 (r:16579)
	applications: add inline dp to multiple apps (MODAPP-337/r:14922)
	build: Fix build to account for FreeBSD sed issues (FSBUILD-184/r:14479)
	build: build unimrcp after sofia (r:14507)
	build: Fix mod_ldap not building on Linux (FSBUILD-189/r:14725)
	build: add --with-rundir configure param and -run freeswitch runtime param to adjust the location of the pid file (FSBUILD-188/r:14768)
	build: fix compile on Ubuntu 8.10 (FSCORE-443/r:14928)
	build: add mod_cidlookup (r:14930)
	build: fix libtiff build (r:15034)
	build: CMAKE_MINIMUM_REQUIRED is called too late. (FSBUILD-192/r:15050)
	build: remove broken modules from debian build (FSBUILD-193/r:15068)
	build: fix Build-Depends on debian build for skypiax (FSBUILD-197/r:15113)
	build: Debian build misses some packages (FSBUILD-201/r:15216)
	build: Win32 Dependencies do not download if there is a space in the path to sources (FSBUILD-203/r:15348)
	build: Handle BIGENDIAN Windows Bug (MODENDP-245/r:15410)
	build: fix spandsp installing (FSBUILD-198/r:15414)
	build: use same version of libtool and libtoolize (FSBUILD-205/r:15416)
	build: Update Debian build init script (FSBUILD-204/r:15417)
	build: Fix Debian script broken in r15417 (FSSCRIPS-21/r:15443)
	build: Add windows projects for Broadvoice only 2008 (r:15470)
	build: debian: remove incorrect option (FSBUILD-208/r:15496)
	build: fix automated libsrtp build (r:15523)
	build: fix --srcdir for modules.conf (r:15526)
	build: fix --srcdir for sounds_version.txt and swigall
	build: fix uclibc cross compile issues (stolen from http://astlinux.svn.sourceforge.net/viewvc/astlinux/trunk/package/freeswitch/freeswitch-sqlite-make.patch?revision=2455) (r:15531)
	build: add new broadvoice to windows solution and cleanup template (r:15562)
	build: Add initial build support to windows (MODUNIMRCP-6/r:15610)
	build: srcdir fixes for modmake.rules (FSBUILD-211/r:15644)
	build: srcdir fixes for Makefile.am (FSBUILD-211/r:15645)
	build: srcdir fixes for configure.in (FSBUILD-211/r:15646)
	build: fix srcdir for libs/apr/configure.gnu (FSBUILD-211/r:15648)
	build: fix calling of sub configure.gnu files during --srcdir build (FSBUILD-211/r:15661)
	build: fix --srcdir make error (FSBUILD-211/r:15673)
	build: Initial check in for Windows Inno Setup script (r:15681)
	build: add --disbale-cpp configure argument to disable the c++ build for the core (r:15717)
	build: pass configure args/vars to sub-configure commands run from module makefiles (r:15718)
	build: try to not insert -std=c99 into cflags passed to libs that are not ansi compliant (r:15739)
	build: add -Wdeclaration-after-statement for supported compilers (r:15762)
	build: add changes for sounds download to allow independent version specific selection (r:15777)
	build: move sounds out to their own packages as they do not change revisions with freeswitch (r:15809)
	build: fix setting of BASE passed to module makefiles when doing --srcdir builds (FSBUILD-211/r:15814)
	build: Numerous --srcdir fixes (FSBUILD-211/r:15849)
	build: compile by default on osx 10.6 and add --enable-64 support (with previous patch) for osx 10.6 (r:15862)
	build: add --enable-64 support for os x 10.4 and 10.5 (r:15874)
	build: add missing modules to modules.conf (FSBUILD-214/r:15879)
	build: fix mod_managed build on gcc 4.4 (r:15897)
	build: Disable rpath checking in freeswitch.spec (FSBUILD-217/r:16037)
	build: Remove libuuid from tree (r:16072)
	build: Avoid building static version of modules (e.g. mod_enum.a) by adding the "-shared" libtool option. (r:16225)
	build: Fix pidfile path in debian init script (FSBUILD-264/r:17040)
	build: Numerous debian improvements (r:17119)
	config: improvements to French language handling (MODASRTTS-20/r:14911)
	config: Add valet_parking to default config (r:15124)
	config: Add valet macros (r:15156)
	config: Fix dialplan 6000 - call park retrieval example (r:15292)
	config: clarify the info as the word IP in the option isn't clearly saying you can put an IP in the options value (r:15302)
	config: Convert lang/fr/vm/sounds.xml to utf8 and minor changes (MODASRTTS-22/r:15319)
	config: Fix default group calling so that the order is followed (FSCONFIG-12/r:15492)
	config: add commented uuid param to mod_logfile's default config (r:15537)
	config: add descriptions for broadvoice codecs (FSCORE-497/r:15626)
	config: Add core-db-dsn (commented) to switch.conf.xml (r:15756)
	config: Block intercepts after call has been answered (FSCONFIG-16/r:15828)
	config: Move erlang module's default config file to be with all the others (r:15881)
	config: Add confirm-key & -macro, tts-engine & -voice, confirm-attempts attributes, extra comments to ivr.conf.xml (r:15929)
	config: Allow specifying auth-scheme in config (r:16350/MDXMLINT-56)
	config: Improvements to french lang (FSCONFIG-18/r:16585)
	config: Add new English sounds, sound prompts (r:16911)
	config: Improvements to French sound prompts (FSCONFIG-23/r:17118)
	core: Add per-call logging (r:14509)
	core: Fix IVR menu timeout when caller presses no digits (DP-4/r:14548)
	core: re-factor node allocation and make a copy of userdata in case the session gets killed before the logger module gets the node (r:14555)
	core: don't expose node allocation functions outside of switch_log.c (r:14556)
	core: Fix leak in xml ivr menus (FSCORE-421/r:14579)
	core: add record_start and record_stop events (r:14591)
	core: re-factor mod_event_socket so it uses switch_log functions to duplicate and free log nodes while it uses them internally (fix bad ptr for userdata on event socket listeners) (r:14598)
	core: add answersec answermsec and answerusec to time how long it took for a call to be answered (r:14620)
	core: add recursive flags and workaround for nested broadcast in controlled situations (r:14644)
	core: add origination_cancel_key variable for a dtmf key that can abort an originating call (r:14645)
	core: add state change hooks for destroy (r:14647)
	core: fix reaction in att_xfer when call is cancelled or times out (r:14650)
	core: fix timeout while bridge is waiting for CF_BRIDGED flag (FSCORE-424/r:14702)
	core: set the hostname core variable in switch_core_init (r:14726)
	core: add more stuff to event in hangup hook api (r:14740)
	core: add graceful zrtp failure (r:14745)
	core: reduce poll timeout when dtmf is present (r:14749)
	core: fix start_dtmf muting input from portaudio (FSCORE-426/r:14755)
	core: fix missing reset causing the same timestamp forever on perfect storm of conditions involving transcoding and ptime combo (gotta love it) (r:14771)
	core: CoreSession::originate: set the uuid on success (r:14773)
	core: add optional prefix arg to set_user (FSCORE-429/r:14789)
	core: try to improve autoflush and other silly audio glitches from edge cases and help (FSCORE-416/r:14800)
	core: fix Windows mem leak (FSCORE-440/r:14882)
	core: tolerate offset dtmf payload (zoiper) (r:14883)
	core: Add the ability to seperate the spool and the storage directories for voicemail (BOUNTY-9/r:14889)
	core: Add min_dtmf_duration setting to FS core and modified default_dtmf_duration in rfc2833 queuing function to not be used as min (FSCORE-442/r:14893)
	core: Remove crash protection (r:14919)
	core: Move switch_util_quote_shell_arg to core functions (FSCORE-446:/r:14958)
	core: add preprocess framework for agc ec etc, ALPHA, needs work (r:14960)
	core: add i flag to bind_meta_app to do inline execute of the app (not wise to use unless app returns instantly) (r:14969)
	core: fix using native_file as ringback on codecs that have passthru only (r:14980)
	core: pita bypass media transfer fiasco fix (r:14983)
	core: don't enforce existing file check on streams (r:15014)
	core: re-INVITEs sent only after ~2 seconds (FSCORE-453/r:15021)
	core: changing default behavior ignore_early_media=true must be set together with ringback to generate local ringback on early media (r:15025)
	core: improve behavior of leg_delay_start, leg_timeout, leg_progress_timeout (FSCORE-439/r:15027)
	core: Fix spinning threads that receive 183 on bridge/originate with bypass_media set (FSCORE-452/r:15028)
	core: Check handling of SIGINT (FSCORE-456/r:15051)
	core: add bridge_answer_timeout variable, a timeout in seconds how long to tolerate a bridge that is in early media without being answered (can be set on either leg) (r:15057)
	core: Enable auto update displays in more places (r:15110)
	core: fix file handle mem leak (thanks pressuerman) (r:15114)
	core: add CALL_UPDATE event (r:15119)
	core: move accidental new behavior behind new param originate_continue_on_timeout=true (r:15121)
	core: fix att xfer to an app (r:15122)
	core: Fix CODEC event has duplicate codec name and rate in lowercase (MODEVENT-59/r:15123)
	core: add missing comma that causes buffer overflow in event socket (r:15128)
	core: expand range of acceptable chars (MODENDP-249/r:15129)
	core: Add 48k 30ms and 40ms codecs (r:15135)
	core: Fix playback cannot play stereo .wav files (FSCORE-463/r:15139)
	core: re-factor some of the message parsing code to use fifo to reduce threading contention (r:15142)
	core: send update after both sides are answered (r:15149)
	core: fix playback_ms and record_ms to work right and add playback_seconds and record_seconds (r:15155)
	core: delay update till after media has been confirmed to prevent SOA race in sip (r:15159)
	core: fix arg parsing on restart to not accumulate args (r:15164)
	core: add new macro to make sure we don't send bypass command too soon (r:15165)
	core: Fix shutdown not working on Windows with fs_cli (FSCORE-435/r:15167)
	core: Fix inbound calls with incompatible codecs can result in heap corruption and crash (FSCORE-467/r:15178)
	core: prevent unsolicited ring_ready (r:15179)
	core: make switch_core_sqldb_stop non-blocking (FSCORE-470/r:15183)
	core: add SWITCH_MESSAGE_INDICATE_UUID_CHANGE to warn a session when the uuid has changed (r:15193)
	core: Handle bypass_media_after_bridge with loopback (FSCORE-418/r:15195)
	core: hangup_after_bridge=false only works if bypass_media=false (FSCORE-417/r:15196)
	core: add api_on_answer var (r:15207)
	core: Fix thread runaway in switch_console_loop for Windows (FSCORE-472/r:15209)
	core: change switch_strlen_zero to zstr (r:15211)
	core: add ignore_display_updates variable to block display updates on that leg (r:15218)
	core: re-factor how record_answer_req=true works, add media_bug_answer_req=true variable and backport record_answer_req=true to use it (IRQ-00/r:15235)
	core: don't consider cng packets for rtp auto-adjust (r:15240)
	core: add RECORD_READ_ONLY and RECORD_WRITE_ONLY chan vars to influence session_record (r:15257)
	core: answer channel on record to ensure media (r:15265)
	core: re-factor ringback so it does not start early_media until it has to (r:15266)
	core: add record_min_sec chan var (r:15271)
	core: flush audio on reset of auto-adjust (r:15282)
	core: filter out unwanted media on rtp (r:15284)
	core: make switch_cmp_addr ipv6 friendly and cleanup/refactor autoadjust code in rtp (r:15289)
	core: add slide_write to switch_buffer (r:15297)
	core: add non opaque return for switch_thread_cond_timedwait (r:15322)
	core: fix some bypass xfer scenarios (r:15326)
	core: Originate/bride doesn't pass 183s, then times out on media while in bypass-media mode (FSCORE-479/r:15329)
	core: add flags to state_handler table (r:15356)
	core: send callee id info as caller id info when an outbound call becomes the a leg of another outbound call (r:15390)
	core: fix issue with global bypass_media_after_bridge messing up callflow (r:15404)
	core: add DUMP_EVENT macro (r:15439)
	core: Add Record-File-Path to SWITCH_EVENT_RECORD_* fired by switch_ivr_record_file() (FSCORE-486/r:15446)
	core: expand per-thread db caching to odbc (r:15453)
	core: add switch_ivr_enterprise_originate optional new dimension to originate strings every element in :_: separated list in originate strings will fire in a dedicated thread and can contain their own {} and , and | lists (r:15455)
	core: add read_frame_callback to gentones (r:15459)
	core: add ringback to enterprise_originate (r:15461)
	core: tolerate MySQL ODBC limitation (FSCORE-487/r:15465)
	core: add <> var container to enterprise_originate syntax to set variables to pass to every thread (r:15469)
	core: add cache_db handle api (odbc/sqlite abstraction) (r:15473)
	core: caller/ee id cleanup (r:15474)
	core: add support for running core-db over odbc (r:15487)
	core: Properly count CNG packets (FSCORE-489/r:15493)
	core: Fix segfault on exit (FSCORE-491/r:15499)
	core: add append and truncate to audio file API (r:15503)
	core: re-factor enterprise_originate (r:15565)
	core: fix thread saftey issue in expiring media bugs (r:15587)
	core: add debug-level to switch.conf.xml and tweak a few debugs (r:15595)
	core: re-factor/abstract the core sql stuff more (r:15599)
	core: don't hangup call on asr errors (FSCORE-493/r:15623)
	core: fix db connection hijacking issue (r:15624)
	core: send bridge/unbridge indication during eavesdrop (r:15638)
	core: record the correct remote_media_ip and remote_media_port even when we autoadjust (FSCORE-500/r:15659)
	core: improve chunked sql transaction submission (r:15672)
	core: add switch_caller_extension_add_application_printf() (r:15688)
	core: Fix switch_poll timeout that causes bad audio in Windows (FSCORE-501/r:15722)
	core: add switch_core_vsprintf (r:15737)
	core: allow recursive broadcasting (r:15757)
	core: Add support for coma-delimited lists in a user's auth-acl param (MODENDP-224/r:15760)
	core: allow listeners to pre-bind to subclasses before the main subscriber is loaded and fix off by one issue in unbinding event handler functions (r:15790)
	core: don't mix ptimes in the codec string lists, just discard conflicting ones (r:15797)
	core: Fix 'uuid_bridge' kills both channels if one of them is executing java app (FSCORE-508/r:15799)
	core: add io mutex to cache db for optional mutex protection (r:15800)
	core: add chan name to log lines (r:15835)
	core: fix caller_profile race in hangup that may cause some missing cdr data in certain cases (r:15839)
	core: add priority queue for events so important broadcasts like hold music can take precedence over event-lock (r:15865)
	core: Adding switch_mprintf (broken out from sqlite)
		Adding new %w to mprintf to auto escape both single quote and backslashes
		Improve the tab completion a tad and fix some sqlite/odbc compat with new mprintf opts
		Change the default stream writer to use switch_vmprintf so %q/%w can be used there too (r:15875)
	core: Fix choppy inband DTMF generation from FS (FSCORE-510/r:15896)
	core: fix record size causing mysql table create failure (FSCORE-512/r:15938)
	core: fix x-fer for SIP phones (FSCORE-513/r:15944)
	core: cleanup tab completion more and introduce new callback based expansion functions to do real time tab completion in some areas (r:15952)
	core: fix build without SWITCH_HAVE_LIBEDIT on Windows and perhaps others (r:15953)
	core: switch_ivr_parse_event(): allow execute-app-args to be specified in event body (FSCORE-514/r:15970)
	core: return true/false from sql tester incase you want to do more stuff when the table had to be created (r:15981)
	core: add fsctl flush_db_handles to expire any unused db handles (r:15982)
	core: allow mod_say to use more than nine digits (MODAPP-380/r:15984)
	core: reset audio level on mute/unmute (r:15991)
	core: Add tab complete to windows (r:16000)
	core: Fix playback during a record session causing invalid wav file (FSCORE-517/r:16003)
	core: don't restart runtime functions when shutting down (r:16008)
	core: Add tab complete to windows (ESL-24/r:16010)
	core: switch_play_and_get_digits now returns SWITCH_FALSE if caller doesn't dial anything (DP-10/r:16011)
	core: adding 12kHz and 24kHz (r:16026)
	core: Fix  dialplain looping over and over again when group_confirm_file is missing (DP-11/r:16063)
	core: trade some max call count for more accurate timing in full media situations, hint: use 30ms ptime for drastic reduction in resources (r:16081)
	core: add core param for controling timer (r:16093)
	core: Incorrect handling when rtp-timer-name=none causes medialess RTP sessions to never time out (FSRTP-10/r:16100)
	core: add SOCKET_EVENT event (r:16135)
	core: count runlevel to prevent dup calls to the core init routines (r:16140)
	core: fix power of 10 in cps counter (r:16144)
	core: add ;; delim to console (r:16166)
	core: fix mitm for audio for passing sas with zrtp (r:16204)
	core: setting jitter buffer keeps dtmf events from firing (FSCORE-523/r:16207)
	core: don't segfault when -nonat is used and nat_map reinit is called (r:16253)
	core: switch to conditional broadcast mode on slow kernels (r:16308)
	core: add bridge_early_media=true variable to hear early media from first channel with early media as ringback, this is 1 way audio (r:16313)
	core: fix progress_timeout sets incorrect originate_status (FSCORE-527/r:16314)
	core: switch_ivr_originate: fix windows compiler warning (r:16320)
	core: few new command line opts -vm for conditonal timer, -nocal to skip timer calibration and -nort to turn off clock_realtime fam of functions (r:16324)
	core: fix failed_xml_cdr prefix is not respected (FSCORE-529/r:16401)
	core: allow alias expansion from fs_cli (r:16416)
	core: allow double escape in parser for \ (r:16440)
	core: Add a -version flag to freeswitch binary (FSCORE-534/r:16482)
	core: Delay windows service startup so other command line options are processed (r:16558)
	core: ZRTP Video works with ZFone (but you have to set your endpoint to use 99 for the payload for video or 125 in mod_h26x.c for H264 becuase the payloads MUST match or it can't figure it out (r:16563)
	core: add new api funcs to xml (r:16605)
	core: disable cpu timer affinity by default but make it still possible via config and fix stray constant in tipping_point (r:16679)
	core: rename switch_socket_create_pollfd to switch_socket_create_pollset, add switch_socket_create_pollfd that really creates a pollfd out of a socket, expose switch_pollset_poll and switch_pollset_remove (r:16683)
	core: Fix play_and_get_digits not honoring max_tries param (FSCORE-554/r:16706)
	core: Fix bridge_answer_timeout dropping calls even after being answered (FSCORE-556/r:16737)
	core: add force_local_ip_v4 and force_local_ip_v6 global vars to override the core funcs to always discover the same ip (r:16801)
	core: compromising on timing code, remove -vm and make it default, make new -heavy-timing for previous default, change tipping-point to work of count of active timers rather than sessions, this should statisfy the droves of 'I wish it worked like 1.0.4 people' (r:16853)
	core: add events for bug start and stop (r:16858)
	core: "I want my two dollars, er, CPU percentage points" (r:16869)
	core: skip dialplan args with bad char sequences (r:16882)
	core: fix mem leak in phrases (r:16884)
	core: add an event to phrases to be used as possible variables in parsing (r:16885)
	core: Fix infinite loop that consumes RAM on bridge group/ and enterprise originate (r:16913)
	core: Fix crash in UniMRCP stream_write callback (r:16917)
	core: Fix dore dump in mod_stun.c (MODENDP-297/r:16953)
	core: add fsctl min_idle_cpu and min-idle-cpu feature to refuse calls after the system falls below a certain percentage of idle cpu (r:16962)
	core: add record_fill_cng var (r:16964)
	core: Remove proxy media flag on video calls when not in proxy media (FSRTP-12/r:16969)
	core: improve group_confirm:
		group_confirm_read_timeout=<ms>
		group_confirm_file=<file>
		group_confirm_error_file=<file>
		group_confirm_key=<now can be one or more keys>
	core: add uuid_bridge_continue_on_cancel var to move on in dialplan if uuid_bridged channel hits a bad b-leg (r:17044)
	core: switch_core_service_thread() causes crash in Windows (FSCORE-576/r:17065)
	core: Break the loop in switch_ivr_parse_all_events() if CF_BREAK flag has been set (FSCORE-577/r:17074)
	core: Fix memory leak caused by the function switch_core_codec_copy (FSCORE-579/r:17105)
	core: add sanity check to project size of decoded codec data (r:17108)
	core: handle some errors on missing db handle conditions (r:17136)
	core: Fix warning message (FSCORE-578)
	core: add switch_channel_export_variable
	docs: Add large Doxygen update (thanks Muhammed Shahzad) (r:14973)
	docs: update es phrase file (MODAPP-317/r:15575)
	embedded_languages: Prevent unloading of embedded languages modules (also fixes MODLANG-121/r:14491)
	embedded_languages: add session.hangupCause() (r:14912)
	embedded_languages: add session.getState() (r:14924)
	embedded_languages: Fix caller_profile old code forcing empty caller ID (FSCORE-450/r:15006)
	embedded_languages: add bridged method to scripting langs (r:16854)
	libapr: add new apr functions/macros needed by unimrcp update (r:15542)
	libbroadvoice: update to http://www.soft-switch.org/downloads/snapshots/voipcodecs/broadvoice-20091122.tar.gz (r:15602)
	libdingaling: patch libdingaling to use next_tag instead of child (r:14624)
	libesl: automatically load ESL.so in PHP (r:14707)
	libesl: fix php swig generation (ESL-20/r:14750)
	libesl: disable duplicate headers on request param events (r:14909)
	libesl: c# wrapper for esl lib (r:14955)
	libesl: add logger.pl a FS debug tool using ESL.pm (r:15377)
	libesl: Add send_info example  (r:15613)
	libesl: use event_clone type internally to not confuse people who think they are getting events in response to commands (r:15660)
	libesl: hook new complete api up to FSAPI and export tab completion down to fs_cli (r:15956)
	libesl: change execute and executeAsync to return the last event instead of status since it will almost always be 0 (r:15973)
	libesl: add help banner on connection failure (ESL-25/r:16040)
	libesl: add support for user level auth to esl and fs_cli (r:16161)
	libesl: tcl example thanks Mark Mann (r:16307)
	libesl: rename 'event' by 'Event', so it's C# keyword (r:16519)
	libesl: Improve C# support. Rename properties like C#-style (r:16520)
	libesl: fix filters (and please use JIRA instead of mailing list to report bugs) (r:16527)
	libesl: Add new examples about use ESL managed. Inbound and Outbound modes were documented. (r:16541)
	libesl: Fix possible ESL deadlock when waiting for non-existent data on socket (r:16721)
	libesl: fix esl sendevent issue and change the sendEvent method to return the reply text like sendRecv etc (r:16921)
	libesl: Add ESL Dispatch perl example (Thanks Jay Binks) (r:16922)
	libiksemel: fix iksemel build against gnutls 2.x (FSBUILD-219/r:16019)
	libjs: pass ldflags to dso builds for libjs and nspr (r:16549)
	libminiupnpc: make socket connection nonblocking to timeout to dead ip addrs (r:16611)
	libportaudio: Dither code in mod_portaudio doesn't properly compile on 64 bit systems (r:15422)
	libsofiasip: fix sofia build with new openssl (r:15426)
	libsofiasip: make sofia not auto-respond to SUBSCRIBE refresh requests (r:16273)
	libsofiasip: Assertion fails in nua_bye_server_report (SFSIP-197/r:16534)
	libsofiasip: Fix segfault in stun_encode_message (FSCORE-551/r:16635)
	libsofiasip: fix typo in sofia that causes endless loop when you have a challenge with 2 auth headers in it (r:17114)
	libspandsp: update to spandsp snapshot 20091005 (r:15084)
	libspandsp: update to spandsp-20091006 snapshot (r:15093)
	libspandsp: fix spandsp build on 32 bit 10.6 OS X builds (LBCODEC-4,MODAPP-351/r:15631)
	libstfu: add resize function (r:14871)
	libunimrcp: Update to unimrcp 1297, remove API change (r:15615)
	libunimrcp: unimrcp windows x64 build support and fix release builds (r:15621)
	libuuid: inital addition of libuuid to tree (r:16021)
	mod_alsa: update to new module api (r:14833)
	mod_bv: remove non-working bv codecs pending working lib from steve and mod_bv unified with both codecs in one module. (r:15559)
	mod_bv16: add support for BroadVoice16 codec (r:15464)
	mod_bv16: add plc (r:15466)
	mod_bv16: move bv16 so it doesn't use the same number as another codec in mod_voipcodecs (r:15468)
	mod_bv32: add support for BroadVoice32 codec (r:15464)
	mod_bv32: add plc (r:15466)
	mod_cdr_csv: try to rotate on failed write to cdr file (r:14948)
	mod_cdr_pg_csv: postgresql cdr module, requires some adjustments to make it more platform agnostic, good for solaris and centos (Cypromis) (r:17100)
	mod_celt: bring CELT up to the latest build we need more testers  (r:15278)
	mod_celt: add CELT for Windows (r:16414)
	mod_cidlookup: Fix segfault on no DSN (MODAPP-346/r:15019)
	mod_cidlookup: add whitepages.com support (r:15023)
	mod_cidlookup: add channel var / debug log for cidlookup src (r:15268)
	mod_cidlookup: fix minor bugs, make whitepages work if regular url not set (oops), channel var set for area,	ignore "UNKNOWN" and "UNAVAILABLE" from API (r:15331)
	mod_cidlookup: more cleanup, always try to get a location, add verbose option to api call, cache area and src so the cache is full rep of cid data (r:15333)
	mod_cidlookup: proper fix for working in npanxx only mode (thanks for the heads up bkw) (r:15341)
	mod_cidlookup: use new cached odbc api (r:15680)
	mod_cidlookup: add shorter timeout + warn on slow responses (r:15683)
	mod_commands: add host_lookup api call (r:14667)
	mod_commands: add uuid_exists api call (r:14668)
	mod_commands: add non-destructive regex substitution (MODAPP-319/r:14727)
	mod_commands: add bg_system (like system but in the bg) (r:14746)
	mod_commands: add optional trailing & to sched_api to denote running the task in a sep thread (r:14765)
	mod_commands: fix segfault (r:14961)
	mod_commands: Updating tab completion for show cli command (FSCORE-468/r:15180) 
	mod_commands: add api hostname (r:15225)
	mod_commands: add uuid_debug_audio <uuid> <read|write|both> <on|off> (r:15274)
	mod_commands: add more autocomplete items (FSCORE-468/r:15506)
	mod_commands: Fix group_call returns nothing if one endpoint on the group is not registered (MODAPP-368/r:15530)
	mod_commands: fix parsing in time_test to prevent a few edge cases (r:15549)
	mod_commands: add db_cache API - shows db cache status (FSCORE-503/r:15697)
	mod_commands: add escape API to escape a string (r:15764)
	mod_commands: add uuid_recv_dtmf (r:15840)
	mod_commands: change name of 'key' field due to reserved name in sql (r:15842)
	mod_commands: add console_complete_xml api (r:15967)
	mod_commands: fix db_cache's auto-complete (r:15969)
	mod_commands: add uuid_audio cli cmd (r:15989)
	mod_commands: check for zstr(cmd) in escape_function (r:16029)
	mod_commands: Fix group_call_function not loading group-dial-string and dial-string from user type pointer (MODAPP-335/r:16034)
	mod_commands: add timer_test cli app (r:16079)
	mod_commands: fsctl add shutdown now for debugging (r:16220)
	mod_commands: add uuid_simplify and sip_auto_simplify (r:16420)
	mod_commands: switch_channel_expand_variables value escape enhancement (MODAPP-354/r:16465)
	mod_commands: Add missing show modules to mod_commands (FSCORE-468/r:17015)
	mod_conference: Fix conference floor ownership being ceded too easily (MODAPP-323/r:14703)
	mod_conference: Display callers' rates (r:14992)
	mod_conference: Don't start conf auto record until a second party arrives (MODAPP-348/r:15029)
	mod_conference: wait-mod doesn't loop moh-sound audio file (MODAPP-349/r:15171)
	mod_conference: Return additional flags in xml_list (MODAPP-360/r:15275)
	mod_conference: take pin from x header (MODAPP-333/r:15415)
	mod_conference: add more mutexes to video thread (r:15551)
	mod_conference: fix video thread (r:15590)
	mod_conference: add mute on/mute off actions in addition to toggle mute action (MODAPP-370/r:15845)
	mod_conference: add conference member data into the play-file-member-done event (r:15909)
	mod_conference: add waste-bandwidth flag to conference (r:16110)
	mod_conference: mix member play file data in with conference so you can hear the bg while hearing the member file (r:16408)
	mod_conference: mod_conference add conference_max_members channel variable that can be set on the first channel calling a conference to override the profiles max-members param (r:16597)
	mod_conference: Change conf recording to start properly when 2nd person joins (MODAPP-398/r:16681)
	mod_conference: Fix race condition that occasionally prevents conf "alone" sound from playing (MODAPP-400/r:16723)
	mod_conference: Add 'you are now muted' prompt to deaf-mute conference option. (MODAPP-402/r:16908)
	mod_console: Improved tab completions, and more description usage informations (LOGGER-2/r:15103) 
	mod_curl: don't include response code in response data as it has its own var (MODAPP-369/r:15591)
	mod_dialplan_xml: Fix anti-action not being supported for time-based conditions (DP-6/r:14901)
	mod_dialplan_xml: add inline=true to actions to exec certain apps right away (also mod_dptools) (r:14906)
	mod_dialplan_xml: Fix condition "week of year" calculation (DP-7/r:14968)
	mod_dialplan_xml: Add holiday, week of year matching (Thanks Vagabond) (r:15721)
	mod_dialplan_xml: Add optional loops attr to xml dp extension (thanks MOC) (r:16886)
	mod_dingaling: fix segfault on unload (MODENDP-243/r:14588)
	mod_dingaling: Fix mod_dingaling does not reads profile information from configuration file at runtime till whole module is reloaded (LBDING-15/r:14917)
	mod_dingaling: force marked answer on dingaling outbound calls when accept is received (r:17043)
	mod_dingaling: Fix STUN-related core dump (MODENDP-299/r:17049)
	mod_directory: initial commit of real code (MODAPP-325/r:14981)
	mod_directory: fix segfault on failure (r:15076)
	mod_distributor: Initial commit (r:15421)
	mod_dptools: add optional level argument to info app to choose the log level it logs on (r:14643)
	mod_dptools: jump to a specific offset with the playback by appending @@<samples> to the path (r:14752)
	mod_dptools: make continue_on_fail trigger explicit matching and only do default behavior when it's not set (r:14972)
	mod_dptools: att_xfer sometimes doesn't detect B leg hanging up on PSTN - this is a patch to hangup B and initiate the transfer of A to C by pressing * (DP-8/r:15013)
	mod_dptools: Add Campon feature. (r:15201)
	mod_dptools: Add campon_stop_key and campon_announce_sound to tell the user how to force it to the end of the waiting period (r:15220)
	mod_dptools: Remove attended transfer from reasons for continue_on_fail (FSCORE-367/r:15367) 
	mod_dptools: add dp apps for setting audio level and muting (r:15993)
	mod_dptools: make cn optional for mute 1 means absolute silence > 1 means generate cn level (r:15996)
	mod_dptools: add leading m:<delim>: to change delim on inline dp eg m:^:set:foo=bar^set=blah=true to put an end to Vagabond's suffering (r:16526)
	mod_dptools: make bridge_terminate_key work in bypass media when using info dtmf (r:16530)
	mod_dptools: Fix bypass_media ignored in dial string (DP-14/r:16588)
	mod_dptools: add param setting function to asr (r:16600)
	mod_dptools: add action='user_call' to xml_curl lookups for the user endpoint (r:17115)
	mod_enum: fix mod_enum build when udns is already on the base system
	mod_erlang_event: Add support for simply sending an arbitrary message to an arbitrary registered process at a node (r:14875)
	mod_erlang_event: Deprecate new_pid message in favor of get_pid which has an extra element, the call's UUID (r:14877)
	mod_erlang_event: optionally allow compatibility with nodes running older OTP releases (R7 through current) (r:15189)
	mod_erlagn_event: Don't exit the entire listener for a single session failure (reported by Timur Irmatov) (r:16516)
	mod_esf: Add ability to adjust multicast packet TTL with the config (MODAPP-338/r:14927)
	mod_esf: fix compile error on windows (r:14934)
	mod_event_multicast: clean up mod_event_multicast to use blocking recvfrom and better error checking (r:15791)
	mod_event_socket: add unique-id check to sendevent to send and event to a session instead of the system (r:14874)
	mod_event_socket: fix mem leak (MODEVENT-54/r:14891)
	mod_event_socket: fix CHANNEL_HANGUP_COMPLETE missing under myevents (MODEVENT-56/r:15011)
	mod_event_socket: fix event_sink race (r:15389)
	mod_event_socket: fix expires seg in event_sink (r:15594)
	mod_event_socket: only kill event socket on 100 consecutive errors (r:15671)
	mod_event_socket: add userauth <user>@<domain>:<pass> to event_socket to auth against user directory uses esl-password esl-allowed-api esl-allowed-events and esl-allowed-log to control resource access (r:16160)
	mod_fax: add spandsp_start_dtmf app to mod_fax (r:16727)
	mod_fax: put tx page count if its TX (r:16940)
	mod_fifo: fix mod_fifo not honoring member_timeout (MODAPP-322/r:14552)
	mod_fifo: add fifo_position var (r:14806)
	mod_fifo: add API: fifo_add_outbound to add outbound members to a FIFO (r:14809)
	mod_fifo: allow to call outbound member on on-the-fly fifo, also add a settings params to delete or keep all dynamic fifo entry (MODAPP-332/r:14989) 
	mod_fifo: skip setting callerid when it's already set (r:15071)
	mod_fifo: convert mod_fifo to cache_db (r:15971)
	mod_fifo: Small bug in odbc-dsn settings parsing (MODAPP-383/r:16018)
	mod_fifo: Fix core dump on assert fail in switch_core_hash (FSCORE-574/r:17023)
	mod_fsv: wait for video before recording in mod_fsv (r:17103)
	mod_h323: initial commit (r:15223)
	mod_h323: fix faststart in progress handling (r:15352)
	mod_h323: remove SWITCH_RTP_FLAG_AUTOADJ because its not work correctly and not do anything reliable in our cases (r:15374)
	mod_h323: fix rtp timer initialisation.
		implement alerting-indication option and channel variable.
		implement progress-indication option and channel variable. (r:15905)
	mod_h323: move faststart to callproceeding due to some broken hardware not understand correctly faststart in progress. (r:15906)
	mod_h323: fix slow start handling (r:15935)
	mod_h323: add PI handling in alerting (r:15936)
	mod_h323: add PI handling in Call Proceeding (r:15397)
	mod_fifo: gracefully fail on missing config file (r:15106)
	mod_iax: avoiding buffer overflow in silly iax lib (r:14910)
	mod_iax: move to unsupported (r:16460)
	mod_lang: Change mod_lua so that it registers as a dialplan (MODLANG-140/r:15400)
	mod_lcr: fix possible null string passed to switch_log_printf (r:14514)
	mod_lcr: Add support for user_rates which are the (optional) end-user rates (MODAPP-340/r:15022)
	mod_lcr: fix potential buffer overflow (r:17051)
	mod_limit: set more chan vars in limit_function (r:14733)
	mod_limit: fix counter-only mode in limit_hash_execute (r:14829)
	mod_limit: add limit_ignore_transfer var (MODAPP-334/r:14830)
	mod_limit: add limit_execute (like limit_hash_execute); allow -#s to act as counter only; add INFO level logs to limit similar to limit_hash (r:14870)
	mod_limit: make limit work with neg count like limit_hash  (r:14920)
	mod_limit: move mod_limit to cache_db and clean up vm legacy code (r:15677)
	mod_limit: remove memcache version, it never worked right and is unworkable w/out a lot	of effort.  will revisit when pluggable limit implemented (r:15908)
	mod_limit: mod_limit: use = instead of like (r:16020)
	mod_limit: Fix stack corruption when calling an empty call-group (MODAPP-407/r:16957)
	mod_limit: Improve rate limit support add switch_core_hash_delete_multi (MODAPP-412/r:17085)
	mod_local_stream: add chime-list to local_stream (r:14966)
	mod_local_stream: Fix memory leak at mod_local_stream (MODFORM-36/r:15431)
	mod_logfile: add uuid parameter to prefix log files by the call's uuid (r:15502)
	mod_lua: add conjure-session code and make session available in API commands (r:14739)
	mod_lua: make luarun use a new pool every call (r:14763)
	mod_lua: fix seek function (MODLANG-124/r:14812)
	mod_lua: add argv[0] (MODLANG-122/r:15433)
	mod_lua: Fix unsetInputCallback() removing HangupHook (MODLANG-158/r:16843)
	mod_managed: Add utility function to create SWIGTYPE_p objects (r:14923)
	mod_managed: Add ProgressMediaTime to ChannelVariables (r:15001)
	mod_managed: Set stick flag on state handler so signal_bridge won't nuke it (r:15357)
	mod_managed: hangup handler should run in cs_reporting (r:15375)
	mod_managed: Add XmlSearchBinding (r:15406)
	mod_memcache: update to libmemcached 0.32 (r:14935)
	mod_memcache: add --with-memcached=no to libmemcached configure (r:14938)
	mod_nibblebill: Support custom sql with var expansion (MODAPP-409/r:17081)
	mod_nibblebill: Fix url (FSCORE-580/r:17129)
	mod_opal: Added setting of outgoing number and display name from extension number so H.323/Q.931 fields are set correctly in ALERTING and CONNECT messages sent for incoming calls.(r:14900)
	mod_opal: Fixed compile issues with latest Opal (r:15545)
	mod_portaudio: fix compilation failure in mod_portaudio_stream (MODFORM-35/r:14531)
	mod_portaudio: add context param (r:14737)
	mod_portaudio: make the ringfile configurable via new API command 'pa ringfile' (r:15633)
	mod_portaudio: fix mod_portaudio linux build with alsa (MODAPP-377/r:15899)
	mod_portaudio: Set a new channel variable with the call_id (MODAPP-277/r:16115)
	mod_portaudio: fix pa play bug (r:16188)
	mod_portaudio: default to dialplan 'XML' instead of 'default' (r:16345)
	mod_portaudio: Add event for audio device error and properly register/unregister custom event subclasses (MODENDP-282/r:16354)
	mod_python: Fix segfault on multiple calls to Python (MODLANG-133/r:14847)
	mod_python: Fix infinite recursion in python script causing crash (MODLANG-134/r:14898)
	mod_python: fix memory leak (MODLANG-136/r:15432)
	mod_python: Fix input callback with additional argument causes segfault (MODLANG-153/r:16536)
	mod_rad_auth: Add RADIUS authentication module (r:17012)
	mod_radius_cdr: Fix Framed-IP-Address in start and stop having wrong IP (MODEVENT-52/r:15197)
	mod_radius_cdr: Fix memory leak from large CPS test (MODEVENT-55/r:15198)
	mod_sangoma_codec: added to trunk - thanks Moy (r:17110)
	mod_say: first cut and say interface rework to include gender and put all args into a struct for easier future extension (r:16929)
	mod_say_es: Improve Spanish lang handling (MODAPP-317/r:15424)
	mod_say_fr: mod_say_fr: Update for better french support. Will probably have more stuff from FSCORE-528  to complement this patch (r:16933)
	mod_say_hr: add Croatian say module (r:17011)
	mod_say_hu: add mod_say_hu to trunk from csecket branch (r:15622)
	mod_say_it: Fix italian pronounce in mod_say_it (MODAPP-362/r:15407)
	mod_say_it: fix hundred pronunciation (MODAPP-367/r:15500)
	mod_say_th: add mod_say_th (MODLANG-144/r:15592)
	mod_silk: add new module FTW! (r:16945)
	mod_skinny: add new module, add conf.xml file and add to modules.in (r:16863)
	mod_skel_codec: Add (thanks Moy) (MODCODEC-14/r:14959)
	mod_skypiax: ubermegapatch, more stability, robustness, scalability. Memory leaks not yet investigated, we'll do next (r:14517)
	mod_skypiax: more stability, no dangling channels, MODSKYPIAX-42, added 'sk list' statistics (thanks Seven Du) MODENDP-236 (r:14533)
	mod_skypiax: now it accepts a max of 1 call from the same skypename to the same skypename (multiple instances of user A calling multiple instances of user B) each 1.5 seconds (r:14834)
	mod_skypiax: fires a custom event when an incoming CHATMESSAGE arrives. (r:14845)
	mod_skypiax: added skypiax_chat command.(r:14857)
	mod_skypiax: now outbound chatmessages follow the standard path, you can send them with: chat,<proto>|<from>|<to>|<message> or with: skypiax_chat,<interfacename> <to> <text>. (r:14884)
	mod_skypiax: when sending a chatmessage, do not report it as an incoming message (eg: do not report the messages sent by myself) (r:15327)
	mod_skypiax: added one more 'PROTOCOL 7' command, because windows was not getting it. Now CHATMESSAGES will probably work on windows too (r:15342)
	mod_skypiax: added 'report_incoming_chatmessages' configuration file per_interface setting. To activate it, put its value to '1' or 'true', any other value will be 'false'. Defualts to 'true' (r:15343)
	mod_skypiax: commented out 'XCloseDisplay' lines, seems to cause crashes... (r:15344)
	mod_skypiax: no more pipes for audio samples exchange between threads.(r:15541)
	mod_skypiax: do not add delay when a sleep() or play() are executed by a script after answer() and before sending audio. (MODSKYPIAX-29, MODSKYPIAX-58/r:15585) 
	mod_skypiax: changed audio i/o tcp socket size and timing constant on linux, for better compatibility with virtual machines (r:16330)
	mod_skypiax: added FS timers, allow for better Virtual Machine (xen, ec2) conference behavior, to be polished (r:16521)
	mod_snipe_hunt: add mod_snipe_hunt to tree (r:14937)
	mod_sndfile: Fix core dump when trying to record ADCPM in stereo (MODFORM-39/r:16999)
	mod_sofia: allow custom SIP X-headers in 180/183/200 messages (MODSOFIA-20/r:14482)
	mod_sofia: use switch_mprintf to securely parse the user parameters for the SQL query for reboot in mod_sofia (MODENDP-233/r:14484)
	mod_sofia: display the profile name on sofia status gateway xxx (r:14520)
	mod_sofia: display the profile name on sofia xmlstatus gateway xxx (r:14527)
	mod_sofia: fix stun: from appearing in sdp round two (r:14532)
	mod_sofia: we need to handle redirects before checking for proxy mode if we want them to work in bypass or proxy media (IRC-00/r:14537)
	mod_sofia: refactor sofia_glue_tech_choose_[video_]port (r:14541)
	mod_sofia: Fix NAT Reboot notify by merging the MWI notify and Reboot notify to a unified function (MODENDP-242/r:14581)
	mod_sofia: FIX parsing of argument of sofia status AND add a new search option (MODENDP-232/r:14585)
	mod_sofia: add 10 second media timeout waiting for codec establishment (MODSOFIA-22/r:14586)
	mod_sofia: Enable creation of sip compact headers from sofia profile configuration (MODENDP-246/r:14698)
	mod_sofia: Fix race with hold/unhold in proxy media (FSCORE-432/r:14797)
	mod_sofia: ATTENTION BEHAVIOR CHANGE... you now have to explicitly set sip_invite_to_params to add params to the to field we will NO longer fall back to sip_invite_params in this case. (FSCORE-433/r:14849)
	mod_sofia: Fix missing NOTIFY MWI when registering via proxy (MODSOFIA-26/r:14851)
	mod_sofia: add set funcs for impls (r:14881)
	mod_sofia: Segfault when receiving 30x response without contact header (MODENDP-247/r:14953)
	mod_sofia: Added sip_rh_ response header addition capability (MODSOFIA-29/r:14967)
	mod_sofia: new param force-subscription-domain (r:14971)
	mod_sofia: improve bad timestamp detection (r:14979)
	mod_sofia: Override the MWI User@Domain used per user/group/domain (MODENDP-241/r:14987) 
	mod_sofia: set extension=auto_to_user in gateway to make destination equal to user (r:15010)
	mod_sofia: cover up sofia bug parsing contact strings that do not have <> in them (r:15026)
	mod_sofia: no more 202 Accepted after REFER since changeset 14701 (MODSOFIA-31/r:15056)
	mod_sofia: append host when its not supplied on carrot dialing (r:15099)
	mod_sofia: Fix MESSAGE_QUERY causes event without minimum event information (MODEVENT-58/r:15117)
	mod_sofia: Add a log message when the invite has no SDP and 3pcc isn't enabled to aid troubleshooting (r:15140)
	mod_sofia: add variable for one legged 302's via originate so you can tell it happened. (r:15163)
	mod_sofia: clear up some more display issues and fix resume-media-on-hold sofia option (r:15177)
	mod_sofia: Fix bridge call seems to hangup as if originator_cancelled when it has not (FSCORE-471/r:15186)
	mod_sofia: fix issue with resume-media-on-hold and t38 re-invites (r:15187)
	mod_sofia: Fix RTP destination port is not updated (SFSIP-180/r:15190)
	mod_sofia: add sip-force-extension (r:15191)
	mod_sofia: add firing of event after processing re-INVITE (MODENDP-253/r:15205)
	mod_sofia: record path and template are two values with backwards compatibility tried  (r:15208)
	mod_sofia: make sure dest is not blank for rare edge case (r:15212)
	mod_sofia: Fix seg where INVITE w/o SDP + FS 3pcc-proxy + B leg has early media (MODSOFIA-33/r:15214)
	mod_sofia: sql-in-transactions profile param (only vaid on first load) whether or not to bundle sql stmts in transactions (r:15217)
	mod_sofia: add pass-callee-id (defaults to true) disable by setting it to false if you encounter something too stupid to ignore X headers (r:15230)
	mod_sofia: overcome ack via in nat (r:15277)
	mod_sofia: windows compiler fixes and switch_mprintf overflow(32bit only) (r:15286)
	mod_sofia: doing caret dialing to a location without a gateway will result in the host being null in some cases this will not allow that if you specify the full sip:dest@host it works fine but if you only specify the number then host will be null (r:15332)
	mod_sofia: add distinct-to=true|false to be used with from-domain when you don't want to change the to (r:15334)
	mod_sofia: make subscriptions time out, discard code that made them last forever (r:15364)
	mod_sofia: reset handle on bad reg (r:15365)
	mod_sofia: don't send blank update (r:15366)
	mod_sofia: fix sql stmt for blf subs (r:15396)
	mod_sofia: fix bug tolerating the other guy's bug (r:15401)
	mod_sofia: experimental support for send-message-query-on-register=first-only (r:15405)
	mod_sofia: add pai to responses (r:15408)
	mod_sofia: add columns to sip_dialogs (r:15420)
	mod_sofia: avoiding initial segfault (MODENDP-265/r:15427)
	mod_sofia: solve problem from MODENDP-258 with an alternate approach, and make send-message-query-on-register=first-only and sql-in-transactions=true the defaults (MODENDP-258/r:15441)
	mod_sofia: lookup gateway by realm when not found by name (r:15475)
	mod_sofia: if the phone is behind nat with us... don't consider nat processing fixes cases where things are EVIL like cisco 79xx (r:15486)
	mod_sofia: Fix sofia statistics (MODSOFIA-38/r:15491)
	mod_sofia: add content-disposition to SEND_INFO (r:15495)
	mod_sofia: video improvements (r:15524)
	mod_sofia: always check for callee update not just because of pai (r:15589)
	mod_sofia: add ani and aniii to caller profile (MODSOFIA-34/r:15596)
	mod_sofia: reset timestamp counter when we get new sdp etc because sonus likes to say ptime 20 and send 30ms timestamps in the 183 then once they say 200 ok with the same sdp they decide to actually send 20 for real this time (FSRTP-8/r:15597)
	mod_sofia: convert sofia to use new core version of cache db handles (r:15600)
	mod_sofia: Fix crash where client register request does not contain "nc" parameter (MODSOFIA-39/r:15609)
	mod_sofia: Add ping hysteresis for SIP peer qualification (MODSOFIA-40/r:15628)
	mod_sofia: add sip_profile_name go go with sip_gateway_name on outgoing calls (r:15629)
	mod_sofia: fail2ban support in mod_sofia thanks jay binks. (MODSOFIA-41/r:15654)
	mod_sofia: Revert part of MODSOFIA-41 r15654 that deals with phone reboot - breaks Aastra/Polycom (r:15656)
	mod_sofia: Fix call not hanging up after hold (FSCORE-481/r:15657)
	mod_sofia: add separate inbound/outound codec prefs params to sofia profile original codec-prefs sets both to the same value for back-compat (r:15658)
	mod_sofia: reset the remote_media ip/port vars more often (r:15676)
	mod_sofia: retry reg faster on dns timeouts (r:15763)
	mod_sofia: huge sonus infection (FSRTP-8/r:15781)
	mod_sofia: fix runaway ptime mismatch with xlite and bv (r:15788)
	mod_sofia: set sip_copy_custom_headers=false to avoid custom sip headers to be copied on the b-leg (r:15789)
	mod_sofia: send bye instead of cancel after invalid 200 OK (MODENDP-270/r:15808)
	mod_sofia: don't set auto-nat on ipv6 profiles (r:15829)
	mod_sofia: Fix att_xfer origination_cancel not working and b has no chance to talk with c in an A-B-C scenario (MODAPP-376/r:15838)
	mod_sofia: add disable_q850_reason chan var to disable sending Reason header in sip BYE messages (MODSOFIA-44/r:15850)
	mod_sofia: Fix UPDATE being sent to session creator without crypto (MODENDP-273/r:15851)
	mod_sofia: add shutdown-on-fail param to sofia profiles (MODSOFIA-45/r:15854)
	mod_sofia: fix send-message-query-on-register set to 'false' (MODSOFIA-42/r:15857)
	mod_sofia: move NUTAG_MIN_SE to sofia_glue_do_invite so it only appears on requests. (MODSOFIA-47/r:15943)
	mod_sofia: Fix redirect contacts not set correctly on 300 Mutliple Choices (SFSIP-190/r:15994)
	mod_sofia: Add fix for silly endpoints who use G729a improperly (MODSOFIA-48/r:16004)
	mod_sofia: Add ping hysteresis for SIP peer qualification (MODSOFIA-40/r:16039)
	mod_sofia: Major presence update (r:16053)
	mod_sofia: Fix sofia loglevel 9 (MODSOFIA-50/r:16058)
	mod_sofia: don't segfault on notify message with no contact (r:16130)
	mod_sofia: honor disabled soa flag in re-invite (r:16193)
	mod_sofia: share and share alike, only nothing is alike in sip =/ (r:16194)
	mod_sofia: Get sofia "auth-calls" parameter and directory "auth-acl" parameter to work through a proxy. (BOUNTY-12/r:16200)
	mod_sofia: add command-line completion for sofia xmlstatus (MODSOFIA-52/r:16260)
	mod_sofia: held-private now works (r:16295)
	mod_sofia: add event header to gateway registration events (MODENDP-281/r:16319)
	mod_sofia: fix PUBLISH/200 response sent without ETag (FSCORE-262/r:16388)
	mod_sofia: set chanvars on both directions in sip and introduce sip_to_tag and sip_from_tag vars (r:16395)
	mod_sofia: fix rpid and from correctly when auto-nat is enabled so polycom won't display the flipping URI and freak some poor souls out :P  (r:16473)
	mod_sofia: sip is stupid MODSOFIA-51, the 202 has to have the correct contact or the phone comes back and subscribes to the contact and not the actual extension (MODSOFIA-51/r:16500)
	mod_sofia: fix attended transfer bug where port specified in Refer-To header is ignored (MODENDP-291/r:16524)
	mod_sofia: exec sql stmt sooner to prevent race (r:16525)
	mod_sofia: add sip_append_audio_sdp var (r:16537)
	mod_sofia: add user-agent-filter and max-registrations-per-extension (BOUNTY-15/r:16557)
	mod_sofia: Fix FS-SIPX attendant transfer problem (SFSIP-86/r:16562)
	mod_sofia: fire presence_out when reg is flushed manually (r:16564)
	mod_sofia: exclude rows with the current call_id - could be an endpoint updating it's
	registration while we're at max_registrations (r:16567)
	mod_sofia: Multicast presence/mwi patch and a small registrations improvements with some NULL-proofing (MODSOFIA-46/r:16575)
	mod_sofia: document tracelevel, add completion for tracelevel (r:16599)
	mod_sofia: add extended info parsing feature to send api commands or queue messages to sessions over info packets (r:16688)
	mod_sofia: add ability for multiple profiles to share the same gateway names via profile::gwname syntax for normT (r:16698)
	mod_sofia: allow domains with no dot in them as long as you specify a profile name (r:16739)
	mod_sofia: add sofia_dig [xml] api func (r:16745)
	mod_sofia: Allow rewrite of the o= line either by detault or as an option when proxy_media=true (MODSOFIA-50/r:16806)
	mod_sofia: change default dtmf duration to 2000ms from 100ms (MODENDP-296/r:16888)
	mod_sofia: move exception for iLBC hack to sdp up to sofia (see Moc if anything breaks) (r:16898)
	mod_sofia: Rewrite of the s= line when proxy_media=true (Following MODSOFIA-50) (MODSOFIA-64/r:16919)
	mod_sofia: Add expires to PRESENCE_PROBE event (MODENDP-298/r:16968)
	mod_sofia: add display feature to SCA (r:17054)
	mod_sofia: fix it so the Cisco phones will update their display properly in these cases and not show Private for all calls (r:17066)
	mod_sofia: only allow barge-in on SCA mode when calls are from their own line (r:17067)
	mod_sofia: add sip_local_network_addr var to see the ip of the sip profile a call came in or went out on for cdr (FSCORE-562/r:17073)
	mod_sofia: mod_sofia: Add url encode to a var in the xml output to be valid xml. Also changed switch_url_encode to return the pointer of the string rather than the length, same as switch_amp_encode() (r:17087)
	mod_sofia: prevent race in killgw followed by an immediate rescan with the same gateway name (r:17096)
	mod_sofia: fix telephone-event negotiation with devices that don't do what the rfc says they SHOULD do (r:17097)
	mod_sofia: Double @ in To header (MODENDP-300/r:17098)
	mod_sofia: add killgw _all_ to delete all gws
	mod_spidermonkey: allow inline javascript, use a ~ as first script character (r:15598)
	mod_spidermonkey: fix mod_spidermonkey on OSX 10.6 (lets see if this breaks any other platforms) (r:15650)
	mod_spidermonkey: fix teletone issues in javascript (MODLANG-159/MODLANG-162/r:16909)
	mod_spidermonkey_core_db: Allow to bind value to parameters in prepared statements (MODLANG-139/r:15632)
	mod_syslog: Enable mod_syslog to log to a specific facility (LOGGER-3/r:15162)
	mod_syslog: mod_syslog does not respect facility setting, always logs to user.* (LOGGER-4/r:15447)
	mod_syslog: fixed mod_syslog.c on solaris since there is no LOF_FTP and LOG_AUTHPRIV in solaris syslog (r:15606)
	mod_syslog: add uuid logging support (r:16187)
	mod_tts_commandline: Add new module (r:14827)
	mod_tts_commandline: introduce the rate parameter, decrease useless verbosity (r:14885)
	mod_tts_commandline: cut samples in half (suggested by anthm), adjust and clean log levels (r:14886)
	mod_tts_commandline: add config (r:14887)
	mod_tts_commandline: quote shell args, create an unique tempfile (r:14931)
	mod_tts_commandline: improve, update config file (MODASRTTS-21/r:14932)
	mod_unimrcp: add support for file://, <speak, and <grammar (r:15236)
	mod_unimrcp: fixed pizza demo bug, other misc ungoodness (r:15330)
	mod_unimrcp: Add enable-profile-events param to mod_unimrcp (r:15528)
	mod_unimrcp: Add enable-profile-events param to mod_unimrcp config (r:15529)
	mod_unimrcp: Support UniMRCP 0.8.0 and new RTCP configuration params (r:15554)
	mod_unimrcp: Update Nuance MRCP configuration to send RTCP BYE (r:15555)
	mod_unimrcp: unimrcp - add windows 2008 project support - doesn't do anything yet (r:15569)
	mod_unimrcp: support resampling for TTS (UniMRCP 0.8.0 only) (r:15570)
	mod_unimrcp: update to new api in unimrcp r1297 (r:15616)
	mod_unimrcp: fix for loquendo MRCPv2 - destroy session instead of removing channel (r:15736)
	mod_unimrcp: Added synthparams and recogparams to config so that default MRCP params can be defined for SPEAK and RECOGNIZE requests (r:16717)
	mod_unimrcp: Added ssml-mime-type config param to deal with server that doesn't like application/ssml+xml (r:16720)
	mod_unimrcp: Do not allow speech_channel_destroy() to return unless MRCP session has been terminated. Do not explicitly destroy mutexes, buffers, and condvars that are allocated off of pool. (r:16938)
	mod_valet_parking: add (r:15072)
	mod_valet_parking: sense att xfer in valet (r:15126)
	mod_valet_parking: add events and valet_info fsapi to valet parking (r:15136)
	mod_valet_parking: add auto ext type and support for being called from bind_meta_app to valet_parking (needs some more) (r:15145)
	mod_valet_parking: add sleep to inline dp to make xfer to valet wait a second (r:15168)
	mod_valet_parking: enforce arg checking (r:15176)
	mod_valet_parking: Add mod_valet_parking to the windows build (r:15185)
	mod_vmd: fix glaring rwdeadlock in mod_vmd (r:14994)
	mod_vmd: make MIN_TIME configurable via channel variables(MODAPP-372/r:15825)
	mod_voicemail: fix operator-ext/operator-extension duplicate config declaration (r:14498)
	mod_voicemail: allow profile to be specified sep <NAME_EMAIL>@myprofile (r:14756)
	mod_voicemail: announce number of saved messages when there are no new messages (r:14964)
	mod_voicemail: Add new api call to get user mailbox settings (MODAPP-325/r:14982) 
	mod_voicemail: Add ability to skip greeting and instructions when leaving voicemail (MODAPP-331/r:14990)
	mod_voicemail: Fix VM disk quota (MODAPP-353/r:15161)
	mod_voicemail: add missing switch_event_destroy in profile config function (r:15262)
	mod_voicemail: default samplerate to 0 instead of 8000 so that we record using the channel's native rate, thx bkw (r:15394)
	mod_voicemail: prevent obscure divide by zero code path (r:15413)
	mod_voicemail: create max timeouts feature like the one in ivr_menu (MODAPP-365/r:15471)
	mod_voicemail: decrease sql queries for message counts (MODAPP-359/r:15636)
	mod_voicemail: Fix r15636 so New are New, and Old are Old messages (r:15639)
	mod_voicemail: mod_voicemail: Change so total_new_messages and total_saved_messages include the count of new/saved urgent messages (Issue caused by MODAPP-359) (r:15670)
	mod_voicemail: Fix segfault (FSCORE-511/r:15876)
	mod_voicemail: passing originator's account name to cc'd accounts or groups to let them see who forwarded it (BOUNTY-13/r:16270)
	mod_voicemail: Allow param for voicemail to allow message file type to be changed (MODAPP-394/r:16576)
	mod_voicemail: copy user xml to a dynamic xml obj so you do not hold exclusive lock on global xml registry the whole time you are leaving a vm (r:16577)
	mod_voicemail: fix vm to inherit params from domain/group (r:16644)
	mod_voicemail: that shouldve been wrlock, and its missing an unlock (r:16647)
	mod_voicemail: add vm_list vm_delete and alias voicemail_inject to vm_inject (r:16673)
	mod_voicemail: update vm_list to include timestamps (r:17014)
	mod_voicemail: add vm_auto_play=false var if you do not wish the vm to auto-play new messages (r:17055)
	mod_voipcodecs: move mod_voipcodecs to use spandsp instead of libvoipcodecs (windows build to follow) (r:15036)	
	mod_voipcodecs: move mod_voipcodecs to use spandsp instead of libvoipcodecs - windows (r:15088)
	mod_voipcodecs: rearrange codecs so we don't have crazy overlap and remove G723-32 and move it up to dynamic cuz people have nothing better to do then write stupid RFC's (r:16251)
	mod_xml_cdr: Allow specifying auth-scheme in config (MDXMLINT-56/r:16247)
	mod_xml_cdr: Add text/xml encoding and uuid to querystring for xml_cdr HTTP posting (XML-21/r:16570)
	mod_xml_cdr: Add param log-http-and-disk (bool) to let user log only to HTTP or to both (XML-16/r:16643)
	mod_xml_cdr: Fix regression (MODAPP-397/r:16656)
	mod_xml_curl: Fix crash when using use-dynamic-url (XML-10/r:14850)
	mod_xml_curl: Allow choice between HTTP Basic and Digest authentication (r:15107)
	mod_xml_curl: Don't use signals when a timeout is specified (XMLINT-13/r:15997)
	mod_xml_curl: change default to disable100continue, we don't acutally handle this case at all anyways, it just causes the request to fail (r:17102)
	mod_xml_rpc: Fix auth issue (XMLINT-54/r:14580)
	mod_xml_rpc: Fix crash in xml_rpc web interface (XML-23/r:16941)
	phrases: Add Recursive phrases support (FSCORE-458/r:15101) 
	scripts: Fix Freeswitch gentls_cert script so the remove option works (FSBUILD-216/r:15965)
	support: add fscore_pb script for dumping core info directly to pastebin (r:14904)
	switch_utils: add a host lookup func (r:14653)
	mod_zeroconf: Move to unsupported (r:15429)
	

freeswitch (1.0.4)

	add: TextMate bundle for FreeSWITCH - thanks mrene (r:12559-12560)
	add: ZRTP Support please download your SDK from http://www.zfone.com and use build/buildzrtp.sh to build the lib. (r:13406)
	add: gdb helpers to .gdbinit
	all: numerous code cleanups, removal of unused variables
	all: update numerous Doxygen comments
	build: fix mod_flite (FSBUILD-126/r:12153)
	build: fix getlib.sh curl/wget detection (12175)
	build: add msvc 2005 project files (r:12197)
	build: rename spandsp project files windows (r:12195)
	build: fix spandsp windows build, msvc 2005 (r:12199-12202)
	build: merge x64 build for msvc9 (r:12207)
	build: add libtiff auto-download to msvc build (r:12212)
	build: update to match spandsp api (FSBUILD-129/r:12214)
	build: inital work on x64 windows build (FSBUILD-125/r:12215)
	build: fix spandsp msvc build issues (r:12218-12220,12223)
	build: fix msvc configuration manager for x64 targets in both 2008 sln files (r:12227)
	build: use compiler intrinsics for windows x64 build (FSBUILD-131/r:12245)
	build: fix version generator on windows (FSBUILD-115/FSBUILD-69/r:12247)
	build: fix various msvc build issues (r:12272-12276)
	build: silence nuisance sun cc warnings (r:12462)
	build: updated windows projects for flite (FSBUILD-128/FSBUILD-133/r:12477)
	build: download flite from right tarball location (FSBUILD-135/r:12482)
	build: rework Windows build for pocketsphinx 5.1 (MODASRTTS-13/r:12483,12541)
	build: fix automake 1.7 build (r:12487)
	build: build path cleanups (FSBUILD-130/r:12490)
	build: fix rebuild every time on msvc 2008 non team editions (FSBUILD-132/r:12492)
	build: add skypiax to sln file and fix some warnings (r:12502)
	build: fixed openzap.conf issue in deb package (r:12506)
	build: fix rlim_t build on Mac (FSCORE-325/r:12533)
	build: Solaris doesn't define RLIMIT_NPROC (FSCORE-326/r:12534)
	build: removal of file reference for flite (FSBUILD-139/r:12540)
	build: integrate mod_erlang_event into the buildsystem (FSBUILD-142/r:12587)
	build: Fix FreeBSD build (r:12678)
	build: fix libsoundtouch build dependencies after a configure failure (MODAPP-243/r:12783)
	build: fix odbc detection to not try to use odbc when no headers are installed (r:12788)
	build: add build of fs_ivrd (r:12843)
	build: sphinx downloads for windows (FSBUILD-146/r:12853)
	build: Build fails at bootstrap with libtool 2.2.6a (FSBUILD-82/r:12899)
	build: add python build dependency to debian package build (FSBUILD-145/r:12920)
	build: add libtool major version detection to configure in prep for supporting both libtool 2.x and 1.5.x at the same time (r:12922,12926-27)
	build: fix older gcc build (FSBUILD-150/r:12924)
	build: Add a fallback check in case libtool is not yet available in the builddir (get the version from build/config/ltmain.sh instead). print an error message and exit configure if that fails too (r:12933)
	build: Add mod_memcache (commented) to modules.conf.in (r:12955)
	build: make libtool version detection more robust (r:12979)
	build: we need DYNAMIC_LIB_EXTEN for mod_perl and others (r:13023)
	build: add mod_say_ru to modules.conf.in (r:13037)
	build: bump sounds version (r:13040)
	build: use different version file for moh version (r:13093)
	build: de-couple version numbers and builds of sound files and moh files (FSBUILD-153/r:13096)
	build: use sound_version.txt and moh_version.txt to determine sound file version on windows (FSBUILD-152/r:13097)
	build: use in tree libtiff for msvc build and fix some header generation checks (r:13097)
	build: clean esl on make current (r:13204,13205)
	build: fix warning-as-error that stops MSVC from building solution (FSCORE-367/r:13301)
	build: fix MSVC build issue from r13294 (FSBUILD-159/r:13302)
	build: remove broken code analysis test (r:13364)
	build: cleanup msvc code analysis warnings (r:13366)
	build: Add mod_file_string to build + misc cleaning and correction (FSBUILD-158/r:13367)
	build: add new files to debian package build (FSBUILD-148/r:13368)
	build: fix mod_file_string dependencies on windows (r:13370)
	build: fix mod_pocketsphinx build error (JANITOR-1/r:13372)
	build: fix PTHREAD_MUTEX_RECURSIVE detection on SUSE 10 (r:13386,13390)
	build: remove regression caused by r13362 (r:13401)
	build: add mod_nibblebill (r:13462)
	build: fix msvc build (r:13467/FSBUILD-165)
	build: add siren to the default build and install (r:13470)
	build: add upnp to windows build (r:13535)
	build: fix msvc 64 bit build regression from upnp build (r:13538/FSBUILD-167)
	build: Check for ifaddrs.h before including it (r:13600/FSBUILD-168)
	build: fix jobserver unavailable bug in make -j (r:13623/FSBUILD-6)
	build: clean/uninstall disabled modules as well (r:13716/FSBUILD-85)
	build: fix msvc pcre build for pcre 7.9 (r:13729)
	build: trivial build fix (FSBUILD-174/r:13877)
	build: fix automake warnings about underquoted definitions in apr macros (FSBUILD-175/r:13882)
	build: make libtool version detection more portable (r:13926)
	build: change ODBC handling in code to be runtime instead of build time (r:13936)
	build: update of spec file for current trunk (FSBUILD-176/r:13988)
	build: replace mod_radius_cdr's Makefile.am with Makefile that pulls freeradius-client source when needed (r:14053)
	build: add experimental modules section to modules.conf.in (r:14062)
	build: allow comments (using ##) in modules.conf (r:14093)
	build: update to 0.6.0 celt codec (r:14206)
	build: fix 64 bit build of radius modules (FSBUILD-179/r:14201)
	build: use internal libtiff instead of system libtiff (r:14321)
	build: partial libsndfile build fix (FSBUILD-180/r14330)
	build: fix compile on Mac OS X Snow Leopard 10.6 (FSBUILD-178/r:14333)
	build:  nat pmp causes segfault on solaris, so we won't support upnp or nat pmp on solaris (FSCORE-410/r:14449)
	config: default config, calling own extension no longer goes to voicemail (r:12596)
	config: default config, 1000-1019 uses $${default_password} instead of hard-coded 1234 (FSCONFIG-5/r:12838)
	config: tweak the configs to bind siptrace on and off to F10 and F11 (r:12938)
	config: Update sample IVR to include ClueCon reg option :) (r:13043)
	config: Add ext 9991 as ClueCon ext; use transfer instead of bridge on demo ivr opt #4 (r:13046)
	config: remove invalid options from profiles (r:13153)
	config: add documention for dist-dtmf member flag in conference.conf.xml
	config: remove mailbox= attribute (r:13375)
	config: add default local stream (r:13376)
	config: small regex adjustment so we can dial PSTN numbers that happen to have 4000 in them (r:13409)
	config: you can call 9999 from a zrtp endpoint and enroll this should be replaced with an IVR to explain it a bit but most of the work happens client side at this point (r:13445)
	config: add mod_nibblebill (r:13458)
	config: clear up docs (r:13468)
	config: clarify zrtp docs in vars.xml and give link on where to get more info  (r:13469)
	config: tweaks to codecs in default config (r:13485)
	config: Add example of sending SAS from b-leg to display on a-leg's phone (r:13500)
	config: commit config options for new auto nat (r:13595)
	config: Remove use_profile from default dialplan, move to vars.xml, default value is 'internal'
	config: clean up configs and put wan.auto in by default commented out (r:13907)
	config: turn on rollover by default (r:13940)
	config: remove unused param comfort-noise-level from default conference config file (MODAPP-299/r:14350)
	core: fix suncc visibility support in libteletone (r:12154-12157)
	core: fix crash on FS shutdown with "..." (FSCORE-297/r:12173,12361,12402)
	core: add separate mutex for state related methods (r:12191)
	core: move media bug removal to hangup state (r:12225)
	core: create indexes for ODBC (r:12235)
	core: add direction col to sql db, add direction param to session_create, log DTMF digits to digits_dialed variable for CDR (r:12244)
	core: avoid operations on closed file handles in embedded languages (r:12255)
	core: fix bridging issue from r:12244 (FSCORE-308/r:12257)
	core: fix segfault after 2000 calls, 5 days uptime (FSCORE-309/r:12267-12270)
	core: fix crash in switch_ivr_digit_stream_parser_feed (MODAPP-221/r:12262)
	core: fix issue in monitor_early_media_fail (r:12271)
	core: fix leak in switch_event.c (r:12291-12292)
	core: clean up ivr_menu, prevent recursion (r:12295)
	core: improve memory usage (r:12317)
	core: fix leak in switch_ivr_async.c (r:12330)
	core: fix leak in switch_channel.c (r:12332)
	core: uuid_displace stopped working when limit arg used (MODAPP-225/r:12368)
	core: fix att_xfer media bug problem (FSCORE-317/r:12390)
	core: improve stability (FSCORE-297/FSCORE-305/FSCORE-315/r:12392,12403)
	core: add new state for CDR and leg_delay_start originate var (r:12403-12404)
	core: Make MESSAGE work with SIP PATH Exension (FSCORE-310/r:12426)
	core: fix uninitialized mutex in switch_core_media_bug (FSCORE-320/r:12428)
	core: fix segfault with eavesdrop (FSCORE-319/r:12429)
	core: add generic config parser - thanks mrene (r:12432)
	core: add sessions since start up to heartbeat event (r:12472)
	core: increase buffer size for regex api (MODAPP-228/r:12480)
	core: fix FreeSWITCH start failure - "Error: stacksize 240 is too large" (FSCORE-323/r:12509)
	core: unloading endpoint module now removes from endpoint list (MODENDP-196/r:12535)
	core: fix deadlock in fsctl hupall (r:12544)
	core: add origination_pricavy var to originate api (r:12546)
	core: increase stack limit for switch_system (FSCORE-328/r:12569)
	core: fix uuid_originate param not assigning uuid properly (FSCORE-322/r:12591)
	core: add check for a and b leg no answer on bridge (r:12595)
	core: fix origination_privacy var (r:12603)
	core: fix group_confirm regression from svn r12403 (r:12616)
	core: fix caller ID values not being set in CHANNEL_OUTGOING (FSCORE-336/r:12643)
	core: make -vg imply -waste so valgrind runs won't re-exec (r:12670)
	core: add apr_pool_mutex_set() to our apr to fix thread-saftey issue (r:12672)
	core: make port allocator more random (r:12673,12675)
	core: make switch_channel_get_variable strdup so the pointer returned is safe and clean up the state locking (r:12674)
	core: Empty audio files should not be created when RECORD_ANSWER_REQ is set to true and a call is not bridged (r:12682)
	core: add transfer_after_bridge var (r:12691)
	core: other_leg_unique_id incorrectly set when briding with using ',' (FSCORE-331/r:12704)
	core: make gaussian noise less noisy (FSCORE-340/r:12720)
	core: add import vars to FIFO (r:12722)
	core: fix switch_core_file_write method not writing the entire buffer to the file (FSCORE-341/r:12728)
	core: fix hanguphook order of operations vs destroy method issue in c++ code (r:12730)
	core: rearrange hangup callflow to do more work in the session's own thread (r:12784)
	core: Allow variables containing variables in set and export (MODAPP-241/r:12804)
	core: add read_terminator_used var (r:12840)
	core: change blocking rtp to psuedo-blocking to avoid endlessly blocking reads and refactor jitter buffer (r:12846)
	core: add rtp-autoflush profile param and rtp_autoflush var (r:12854)
	core: fix various small leaks (FSCORE-347/r:12873)
	core: add a couple, two tree stats (r:12913)
	core: fix play_and_get_digits to unset the var if the regex didn't match (r:12921)
	core: drop divide by 2 from normalize func (r12935)
	core: add missing begin/end, allow threads to read and play_and_get_digits methods (r:12958)
	core: fix windows calling conventions for modules with sub-modules broken in svn r12919 (r:12960)
	core: add flag to denote if a codec is init or not (FSCORE-349/r:12961)
	core: add more specific checks to new macro just to be safe (r:12973)
	core: change CS_DONE to CS_DESTROY and add state handler for destroy and tear down critical parts of the channel from this method which is not called until it's impossible for anything to be referencing the channel (after final write lock and before destroying the pool) (r:12986)
	core: fix regression from earlier commit (FSCORE-352/r:12987)
	core: expand channel variables for sound files in IVRs (MODAPP-257/r:13005)
	core: clone frames in loopback so we can smooth it out better, now with more memory usage (tm) maybe this will curb pepople from using it like candy (r:13011)
	core: run expand_vars if input contains a special escaped character not just when it contains a variable to eat up back slashes (r:13015)
	core: change names to be more explicit (r:13028)
	core: xml_config: Fix issue where default NULL strings were ignored on reload (r:13052)
	core: autoflush on bridge and add bridge_hangup_cause variable to indicate the hangup cause of the last bridged channel (r:13065)
	core: add record_ms, record_samples, playback_ms and playback_samples chanvars (r:13105)
	core: make state_handler macros not let you install the same one more than once (r:13111)
	core: Do not use struct fd_set uninitialized (always FD_ZERO() them, before using FD_SET() on a new one, or reusing them after select()). Fixes a segfault on solaris (r:13125)
	core: Fix missing UNPROTECT_INTERFACE in case pre_answer fails (r:13130)
	core: add record_waste_resources channel variable to send blank audio during recording (r:13144)
	core: add auto-sync idle timers in switch_time.c (r:13161)
	core: Add Q850 hangup cause variable (FSCORE-356/r:13163)
	core: keep presence up to date (r:13166)
	core: fix failed_xml_cdr_prefix is processed before all variables for the b-leg have been generated (FSCORE-357/r:13167,13176,13264,13274,13285)
	core: failed_xml_cdr_prefix doesn't work for list of destinations separated by pipe (FSCORE-359/r:13209)
	core: add support for gcc 4.4.0 (FSCORE-355/r:13210)
	core: add resume command to event socket and socket_resume variable (r:13212)
	core: prevent buffer destroy race (r:13219)
	core: move originate retry related vars to only be in {} not in channel variable list (r:13225)
	core: Make { } vars take precedence over channel variables (r:13228)
	core: fix memory issue in core file resampling code (r:13233)
	core: fix play and get digits when using phrase macros (r:13244)
	core: Don't set the caller name to origination_uuid's value (r:13248)
	core: Add switch_xml_parse_str_dynamic and switch_xml_parse_str_dup (r:13255)
	core: In IVR menu use menu-timeout instead of menu-inter-digit-timeout when no digits have been entered (r:13279)
	core: fix segault on OpenSolaris 2009.06 snv_111a (FSCORE-365/r:13286)
	core: allow socket app to operate without pre_answer (r:13294)
	core: ensure vasprintf is available on linux (FSBUILD-160/r:13358,13361)
	core: setgroup/initgroup availability on linux (FSBUILD-160/r:13359)
	core: use uint16_t instead of u_short (FSBUILD-160/r:13360)
	core: add configure checks for tm->tm_gmtoff and tm->tm_zone (FSBUILD-160/r:13362)
	core: Fix IVR key response broken when multiple files are played (MODAPP-280/r:13382)
	core: fixx off-by-one in xml preprocessor (r:13385)
	core: fix windows pch (r:13392)
	core: fix segfault on out of memory situation.  (r:13398/FSCORE-366)
	core: treat app::arg syntax in execute_on_answer as a broadcast request (r:13400)
	core: use our own handler so it won't get overriden by anyone if zrtp is on (r:13411)
	core: fix backslash getting removed from regex when using mod_xml_curl (r:13414/FSCORE-370)
	core: fix handle leak in switch_thread_self on windows (r:13421/FSCORE-371)
	core: allow switching from secure to clear and back (r:13422)
	core: add more options to zrtp (r:13424)
	core: add buffer flush (r:13425)
	core: add some stuff for zrtp (r:13426)
	core: can't flush because you have a chance of dumping zrtp control frames (r:13427)
	core: add zrtp_sas1_string and zrtp_sas2_string variables (r:13429)
	core: zrtp - this should make sure the secure mitm has a chance over latent connections (r:13436)
	core: properly detect unterminated (r:13438)
	core: zrtp - fix mitm to be more reliable (r:13443)
	core: zrtp - mark verified (r:13444)
	core: zrtp_secure_media=true will have to be set to true in order for your zrtp to work moving forward similar to how srtp_secure_media works.(r:13461)
	core: prevent zrtp and srtp at the same time (r:13486)
	core: add CALL_SECURE event (r:13487)
	core: add show secure channel status in "show channels" output (r:13502/FSRTP-2)
	core: add CHANNEL_HANGUP_COMPLETE event to take the place of CHANNEL_HANGUP, CHANNEL_HANGUP now fires as soon as the channel is hungup but you must wait for CHANNEL_HANGUP_COMPLETE for the CDR data (r:13505)
	core: fix core dump when calling session.execute (r:13508/FSCORE-373)
	core: add netmask detection for nat discovery work (r:13549)
	core: fix order of ops to enable logging sooner (r:13556)
	core: add hunt_caller_profile (r:13625,13626)
	core: add usec delta to log (r:13647)
	core: add padding to cycles on session_record (r:13648)
	core: Export swtich_inet_ntop so modules can use it (r:13738)
	core: record_sample_rate variable influences the rate record app will use (r:13767,13768,13769)
	core: init buffer in lead out on speak (r:13787)
	core: add local_var_clobber variable for {} to set if [] vars should clobber {} vars (default false) (r:13842)
	core: Fix switch_odbc_handle_callback_exec_detailed to handle floating point database columns properly (FSCORE-384/r:13866)
	core: set sound path in say app (r:13904)
	core: add wan.auto acl (r:13905)
	core: Fix ACL nat.auto (FSCORE-385/r:13925)
	core: add hostname variable (FSCORE-374/r:13982)
	core: decrement RWLOCK->rwlock->__data->__nr_readers after Redirect/Transfer (FSCORE-391/r:14030)
	core: add features to nat-pmp (Thanks Rupa) (FSCORE-381/r:14055)
	core: fix error message that gives wrong stack size (FSCORE-395/r:14058)
	core: Fix IVR menu timeout issue (MODAPP-301/r:14129)
	core: add time of day routing attrs to condition tags in xml dialplan (r:14137)
	core: change timing for media bug recording *yet again* (r:14143)
	core: Fix voicemail rewind key, improve file seek (MODAPP-305/FSCORE-401/r:14199)
	core: Fix uPNP for WRT-54TG-TM (FSCORE-402/r:14241)
	core: group_confirm_cancel_timeout=true to stop timers once a group_confirm is triggered (r:14247)
	core: Fix record_sample_rate when using stereo (FSCORE-403/r:14283)
	core: hit the xml registry to look up path information for the say app (r:14310)
	core: timeout calls stuck in CS_NEW after 1 minute (r:14313)
	core: use FREESWITCH_OPTS env var to hold a string of desired command line switches (r:14352)
	core: Handle UPNP from pfsense sending malformed header (FSCORE-405/r:14373)
	core: add switch_sockaddr_equal (LBAPR-3/r:14397)
	core: fix missing UNPROTECT_INTERFACE (r:14398)
	core: fire_asr_events should be true/false (FSCORE-406/r:14447)
	docs: Fix filename references in phrase_en.xml (r:12976)
	docs: Update phrase_en.xml to include v1.0.8 sound prompts (r:13041)
	docs: Updates to phrase_es.xml (r:13067)
	docs: Latest revision of Spanish prompts file (r:13115)
	docs: switch_core.h doxygen update (r:13181)
	docs: Add ZRTP phrases to phrase_en.xml (r:13488,13489,13490)
	docs: Add Stephane Alnet's French translations to phrase_fr.xml (r:13624)
	docs: Add AGL3.0 for zrtp (r:13848)
	formats: add mod_portaudio_stream to get audio from any dev (MODFORM-25/r:12178)
	fs_cli: fix solaris build (r:12160)
	ivr: make tts-engine and tts-voice valid attributes on the menu xml (r:12278)
	libapr: fix solaris build (r:13671/FSBUILD-169)
	libapr-util: add xml/expat stuff back to apr-utils (requires re-bootstrap) (r:13289,13290)
	libdingaling: hijack gcrypt init call to fix threadsafe race in dingaling and fix buffer overrun in iksemel (r:12575)
	libdingaling: add ldl_handle_running (r:13085)
	libdingaling: add a function to check for connected and authorized (r:13100)
	libdingaling: fix auto-restart on disconnect (r:13134)
	libesl: add api, bgapi methods (r:12179)
	libesl: add ruby example (r:12180)
	libesl: add python example (r:12184)
	libesl: fix missing Event-Name param (MODEVENT-39/r:12311)
	libesl: example of using esl to get events for ASR (r:12344)
	libesl: improved filter handling (r:12488-12489)
	libesl: fix ESL segfault when bgapi is called twice and BACKGROUD_JOB events are subscribed (ESL-5/r:12525)
	libesl: fix illegal free when invalid bgapi originate args (ESL-8/r:12623)
	libesl: add disconnect method; add ivrd (r:12724)
	libesl: add command line args to single_command.py example (ESL-9/r:12729)
	libesl: check in ESL::IVR perl mod (r:12760)
	libesl: fix ruby linking on debian (ESL-7/r:12805)
	libesl: fix esl coredumps in esl_event_serialize when using sendEvent from PHP (ESL-11/r:12884)
	libesl: add event name when sending (r:12889)
	libesl: add ESL::IVR::setVar from intralanman (r:12917)
	libesl: fix ESL/IVR to include the regex (r:12921)
	libesl: fix ctrl-D in fs_cli not to exit unless cmd line is empty (ESL-13/r:12952)
	libesl: fix undef check (r:13012)
	libesl: kill zombies (r:13013)
	libesl: don't double encode events when sending them  (r:13078)
	libesl: adding sendevent example for esl perl (r:13079)
	libesl: send content len with body in esl sendevent (r:13168)
	libesl: add arbitrary notify (r:13188)
	libesl: fix Phantom events when doing several bgapi calls without interleaving them with recvEvent (ESL-12/r:13217)
	libesl: add Ruby example for ESL (ESL-15/r:13235)
	libesl: add INFO example for Perl (r:13293)
	libesl: add socketDescriptor() method to ESL::Connection obj (r:13770)
	libesl: add prelims for Java ESL (r:13949,13950)
	libesl: add fs_cli command line editing (FSCORE-389/r:13964)
	libiksemel: let return 0 be a failure on read in iks to avoid cpu race (r:13123,13124,13133)
	libnatpmp: add (r:13510)
	libnspr: fix bsd port packaging (r:13715/FSBUILD-170)
	libpcre: update to pcre 7.9 (r:13706)
	libsndfile: add executable permissions to libs/libsndfile/src/create_symbols_file.py (FSBUILD-134/r:12535)
	libsndfile: update to 1.0.19 (r:13415/LBSNDF-7) 
	libsofiasip: su.h - define su_family via struct sockaddr (r:12260)
	libsofiasip: sip_parser.c - fixed sip_transport_d() (r:12261)
	libsofiasip: Fix compile time out-of-bounds error in su_uniqueid.c (SFSIP-136/r:12914)
	libsofiasip: make info work out of dialog (r:13087)
	libsofiasip: silence solaris warning from missing braces (SFSIP-132/r:13369)
	libsofiasip: subscription-state subexp-params missing (r:13439/SFSIP-148)
	libsofiasip: fix re-register after 423 w/ Min-Expires (r:13619/SFSIP-143)
	libsofiasip: use addres, id, and version from user sdp if they exist (r:13621)
	libsofiasip: handle mid-line comments in resolv.conf patch from Lee Verberne (SFSIP-152/r:13749)
	libsofiasip: fix CNAME record resolve failure (SFSIP-129/r:13915)
	libspandsp: update to snapshot 20090308 (r:12514)
	libspandsp: update to snapshot spandsp-20090421 (r:13086)
	libspandsp: update to snapshot spandsp-20090427 (r:13177)
	libspandsp: update to spandsp-0.0.6pre12 (r:13311)
	libspandsp: use __cdecl calling convention for span_rx_handler_t and span_tx_handler_t type functions (r:13509)
	libspandsp: update to spandsp snapshot ******** (r:14316)
	libspandsp: add --enable-builtin-tiff configure arg to tell spandsp to use pre-built libtiff in parallel directory (r:14318)
	libspeex: Add visibility support for suncc on solaris (r:13117,13118)
	libteletone: update api visibility macros for windows
	libtiff: add libtiff, v3.8.2
	libxmlrpc: add select to read socket in abyss so we can timeout (r:13107)
	libxmlrpc: fix segfault (MODAPP-293/r:13906)
	mod_cdr_csv: add option to disable creation of multiple files if account code is set (MODEVENT-44/r:13968)
	mod_cidlookup: add new module, mod_cidlookup (r:12990)
	mod_cidlookup: switch to using CURL instead of mod_http (r:12992)
	mod_cidlookup: add config file (r:12994)
	mod_cidlookup: set initial value for status (r:13029)
	mod_clue_choo: add new module, mod_clue_choo (r:13068,13069,13070)
	mod_commands: Add show api [name] and show application [name] (r:12296)
	mod_commands: fix header_string "key" from being set twice from SWITCH_STANDARD_API(user_data_function) (MODAPP-242/r:12786)
	mod_commands: add show modules functionality (MODAPP-227/r:12806)
	mod_commands: add echo api command (echo back exact input) and add expand api command (executes another api command with variable expansion) (r:13101,13102)
	mod_commands: add "stun" fsapi command (r:13137)
	mod_commands: mod_commands: if no bind ip specified for stun fsapi command, use the guess ip (r:13140)
	mod_commands: add show channels like %foo% (no % in the string implies wrapped in %, no spaces allowed in match string (r:13193)
	mod_commands: Add max-sessions to status (MODAPP-254/r:13373)
	mod_commands: remove double safe_free's (r:13453,13454,13455)
	mod_commands: add nat_map API (r:13531)
	mod_commands: add show distinct_channels (mix of show channels and show calls) (r:13944)
	mod_commands: allow xml_locate to just take section (r:14317)
	mod_conference: fix read terminating on single digit when called from a meta-app inside a conference (MODAPP-226/r:12466)
	mod_conference: recording members should be invisible to commands (MODAPP-233/r:12582)
	mod_conference: add lock caller_control (r:12787)
	mod_conference: only first call to a conf requires a profile; fix bgdial (MODAPP-245/r:12809)
	mod_conference: add conference_enforce_security variable to bypass or require pin or locked flag (r:12863)
	mod_conference: clean up pin prommpting and make local override stay local (r:12864)
	mod_conference: keep new conference locked the whole time during transfer (r:12988)
	mod_conference: allow setting the moh_sound from a variable (r:13042)
	mod_conference: set perpetual_sound from channel var (r:13048)
	mod_conference: increase sanity timer for conference auto-record to 120 seconds (r:13104)
	mod_conference: serialize (r:13116)
	mod_conference: dist-dtmf flag in conference (r:13191)
	mod_conference: add transfer action to caller-controls (r:13194)
	mod_conference: fix dtmf queue issue in dist-dtmf feature (MODAPP-268/r:13206)
	mod_conference: add execute_application to custom controls (r:13216)
	mod_conference: disallow spaces in conference names (MODAPP-219/r:13313)
	mod_console: Fix mod_console (missing FD_ZERO before FD_SET) (r:13126)
	mod_conference: add conference wait-mod flags and member moderator flag to delay starting a conference until someone with a moderator flag has joined (r:13442)
	mod_conference: add member-type header to relevant events (r:13471)
	mod_conference: make endconf count cumulative (r:13526)
	mod_conference: don't transfer back to the same conference (r:13638)
	mod_conference: Add Job-UUID to conference bgdial  (MODAPP-304/r:14161)
	mod_curl: Add mod_curl (API for querying Web resources) and moved mod_http to experimental because of probable leaks (r:14092)
	mod_curl: Add ability to make a post request from dialplan (MODAPP-307/r:14348)
	mod_dahdi_codec: delay init of resources until the first time they are actually used to avoid unnecessary waste of resources in hardware codec (r:12962)
	mod_dahdi_codec: set mod_dahdi_codec dahdi transcoding device sockets to non-blocking to avoid hanging when there is no data and just return 0 bytes frame (MODCODEC-8/r:13257)
	mod_dahdi_codec: added proper waiting (up to 10ms) for the DAHDI transcoder output frame (r:13262)
	mod_dahdi_codec: return silence frame in dahdi codec when there is no output from the decoder (r:13265)
	mod_dahdi_codec: add 30ms G729 codec in mod_dahdi_codec (r:13404)
	mod_dialplan_xml: add min of day to time rules (r:14378)
	mod_dingaling: fix crash when unloading/reload mod_dingaling (LBDING-13/r:12612)
	mod_dingaling: fix no sound if phone picked up after 5th dingaling on Gtalk client (MODENDP-198/r:12641)
	mod_dingaling: fix core dump when calling Gtalk enabled FS using leg_timeout (MODENDP-199/r:12641)
	mod_dingaling: dl_login command line Usage and parameter doesn't match (MODENDP-202/r:12680)
	mod_dingaling: fix unload mod_dingaling core dump (MODENDP-204/r:12680)
	mod_dingaling: fix small memory (r:13374)
	mod_dingaling: fix potential core dump (MODENDP-230/r:14296)
	mod_dptools: prevent system crash when "user" in originate dialstring (FSCORE-313/r:12395)
	mod_dptools: added sched_heartbeat and enable_heartbeat dialplan apps (r:12491)
	mod_dptools: fix b & c legs not bridging when attended transfer used (FSCORE-334/r:12649)
	mod_dptools: Custom events fired from event application bypass filtering mechanism (MODAPP-249/r:12894)
	mod_dptools: change inline limit from 4 to 128 apps (MODAPP-253/r:12957)
	mod_dptools: improvements to presence function (r:13164)
	mod_dptools: add group_recurse_variables and user_recurse_variables to {} vars (default is true, set to false to not pass vars down to user or group channels) (r:13241,13246)
	mod_dptools: add param dial-timeout to directory (MODAPP-271/r:13247)
	mod_dptools: add set current_application_response channel variable (MODAPP-273/r:13298)
	mod_dptools: fix SEGV in strncasecmp call (MODAPP-278/r:13317)
	mod_dptools: fix leaks (r:13446, 13447, 13448)
	mod_dptools: add sleep_eat_digits channel variable (r:14102)
	mod_easyroute: fix leak (r:13449)
	mod_enum: fix enum_auto_route (MODAPP220/r:12243)
	mod_erlang_event: Bind to 0.0.0.0 instead of 127.0.0.1 by default; like most erlang nodes do. (r:12249)
	mod_erlang_event: Reply appropriately to net_adm:ping() (r:13066)
	mod_erlang_event: snprintf needs a format string too, and write has the warn_unused_result attribute set, so store the return value somewhere (r:13090)
	mod_erlang_event: fix erlang autoconf macro to deal with edge cases (MODEVENT-45/r:13288)
	mod_erlang_event: fix FreeSWITCH crashing when sending traffic (FSCORE-379/r:13746)
	mod_event_socket: disallow unloading mod_event_socket from event socket (r:12326,12334)
	mod_event_socket: fix api_exec crash on OSX (MODEVENT-40,r:12349)
	mod_event_socket: move connect command to always work on outbound socket not just the first time (r:12723)
	mod_event_socket: add nat-map option to mod_event_socket (r:13567)
	mod_event_socket: sendevent Fire events with correct event-name and subclass (MODEVENT-41/r:14354)
	mod_fax: can't print base 8 number that has a 9 in it.. use stringified version instead (r:13312)
	mod_fifo: consumer callback waiting an option in fifo.conf.xml (r:12685)
	mod_fifo: add fire events on bridge in fifo (r:12791)
	mod_fifo: fix fifo re-parse crash (FSCORE-343/r:12855)
	mod_fifo: fix uuid_transfer into mod_conference audio issue (MODAPP-259/r:13030)
	mod_fifo: add  fifo_orbit_dialplan and fifo_orbit_context vars (MODAPP-189/r:13038)
	mod_fifo: don't do wrapup when agent is in nowait mode or call has ended (r:13094)
	mod_fifo: don't call all members on on-hook mode and round robin based on next_avail (MODAPP-272/r:13240,13242)
	mod_fifo: abandon outbound calls when they are not needed, place queue name in the caller id (r:13240)
	mod_fifo: add "fifo_destroy_after_use" variable to allow quick destruction of one-time use FIFO queues (MODAPP-287/r:13897)
	mod_file_string: add new module, allows for playing ! delimited lists of sound files (r:13182)
	mod_flite: three new voices - rms, awb (male), slt (female) (r:12166)
	mod_flite: mod_flite_shutdown not called/used (MODASRTTS-15/r:13270)
	mod_g723_1: add more ptimes (r:14095)
	mod_iax: make mod_iax async (r:13021)
	mod_iax: autoflush these channels that use queues (r:13057)
	mod_lcr: allow channel vars in custom_sql (r:12198)
	mod_lcr: Add ifdefs around odbc calls in shutdown function (r:13268)
	mod_lcr: Adjust handling of strip parameters so entire number can be stripped (MODAPP-279/r:13365)
	mod_lcr: honor effective_caller_id_number for CID override (r:13611)
	mod_lcr: Add variable expansion in cid regexp processing (MODAPP-309/r:14287)
	mod_lcr: add "as xml" option (r:14311)
	mod_limit: Add events and a shutdown function (r:12497)
	mod_limit: close odbc handle (r:12632)
	mod_limit: Add more error checking to hash api/app (r:13007)
	mod_limit: prevent multiple bindings of the same event_hooks to make code simpler in mod_limit (MODAPP-264/r:13113)
	mod_limit: Move over-limit messages down to INFO (MODAPP-308/r:14275)
	mod_limit: Refactor and add limit_hash_execute application (see wiki) (r:14401)
	mod_local_stream: Add show_local_stream [stream name] (MODFORM-27/r:13165)
	mod_local_stream: fail over to default if desired stream is not found (r:13184,13185,13186)
	mod_local_stream: refactor resampling in mod_local_stream (r:13222)
	mod_loopback: mod_lopback: other_loopback _leg_uuid variables are sometimes unset while executing the dialplan (MODENDP-210/r:12905)
	mod_loopback: destroy codec (r:12936)
	mod_loopback: fix loopback channels that stay alive (FSCORE-349/r:12944,12953)
	mod_loopback: flush queued frames on audio sync event (r:13018)
	mod_loopback: autoflush these channels that use queues (r:13057)
	mod_loopback: pass flush indications across to the b leg (r:13146)
	mod_loopback: make mod_loopback render silence to prevent livelock (r:13147)
	mod_loopback: drop excess frames on loopback channel (r:13156,13157,13158)
	mod_loopback: add loopback_bowout variable (set to false to skip auto-bowout) (r:13162)
	mod_loopback: mod_loopback plays noise instead of silence (MODENDP-216/r:13214)
	mod_lua: windows build changes to support externally built modules. (MODLANG-101/r:12237)
	mod_lua: fix visibility support (FSCORE-302/r:12239-12240)
	mod_lua: fix windows build (FSBUILD-149/r:12919)
	mod_lua: fix memory leak (MODLANG-111/r:13298)
	mod_lua: fix error checking to be more reasonable (MODLANG-116/r:13993)
	mod_lua: fix  memory leak in xml binding (MODLAN-113/r:14158)
	mod_managed: fix recursive file record during file playback issue (r:13551/IRC-00)
	mod_managed: New mod_managed with multi-appdomain (reloading) and scripting support (r:14364)
	mod_memcache: add new module, mod_memcache; API for memcached (r:12871)
	mod_memcache: make -ERR for API call less verbose  (r:12949)
	mod_memcache: add hook reloadxml event (r:12950)
	mod_memcache: unbind the reloadxml event handler on shutdown (r:12954)
	mod_memcache: Fix C99 error, move var declaration out of the "for()" statement (no -std=c99?) (r:12959)
	mod_memcache: cleanup xml config handling (r:13050,13051)
	mod_memcache: fix compile failure (MODAPP-292/r:13883)
	mod_native_file: add fh->pos setting (MODFORM-34/r:14388)
	mod_nibblebill: mod_nibblebill question: DB Error while updating cash (MODAPP-229/r:12907)
	mod_nibblebill: Updated mod_nibblebill to work properly w/ postgres fields  (r:13031)
	mod_nibblebill: Added feature mod_nibblebill to check balance and transfer the caller to an alternate dialplan context & extension if they deplete their funds. (r:13432)
	mod_nibblebill: cleanup, fix leak (r:13463)
	mod_nibblebill: fix segfault (MODAPP-286/r:13662)
	mod_nibblebill: Fixed nibblebill to bill on call start (r:13753)
	mod_nibblebill: code before declaration (FSBUILD-172/r:13755)
	mod_opal: Fixed transmission of Q.931 Calling-Party-Number, and DisplayName on H.323 Setup (r:12347)
	mod_opal: allocate frame from session pool so it will not go out of scope (r:12811)
	mod_opal: disable visibility support broken in newer gcc (MODENDP-190/r:12923)
	mod_opal: add dtmf method to mod_opal (r:13002)
	mod_opal: Applied patch to fix some race conditions on call termination (r:13014)
	mod_opal: prevent endless loop (r:13034)
	mod_opal: Fixed ability to send a string as user indications (DTMF) (r:13049)
	mod_opal: try to flag CNG frames (MODOPAL-6/r:13098)
	mod_opal: Fixed incorrect include path (r:13239)
	mod_perl: Libtool build fix for mod_perl: use LIBTOOL_LIB_EXTEN to make libtool-2.2 happy and CXXLINK since we are linking a C++ lib (r:13080)
	mod_pocketsphinx: Revamp mod_pocketsphinx to use jsgf format (r:12224)
	mod_pocketsphinx: now you can specify the dictionary to use in the config file (r:13747)
	mod_pocketsphinx: add language weight (r:13910)
	mod_portaudio: fix audio issue in portaudio (r:12669)
	mod_python: fix core dump when using startup script (MODLANG-106/r:12648)
	mod_python: fix python failing during loading startup modules (MODLANG-105/r:12658)
	mod_python: fix EventConsumer::pop() can deadlock mod_python (MODLANG-109/r:12849)
	mod_python: fix seg on unload when scripts are running (MODLANG-107/r:13721)
	mod_say_en: fix missing "at" in time readback, change from cardinal to ordinal numbers on dates, e.g. "January 20th" vs. "January 20" (MODAPP-263/r:13099)
	mod_say_en: tweak grammar (MODAPP-310/r:14351)
	mod_say_es: Add stub phrase_es.xml (r:13019)
	mod_say_ru: add Russian language say module and config files (r:12966-12971)
	mod_say_ru: change encoding on phrase_ru.xml from cp1251 to utf8 (r:12974)
	mod_say_ru: add .wav and small fix (r:13000)
	mod_say_ru: change char million,thousand to enum (r:13009)
	mod_say_ru: rename currency (r:13024)
	mod_say_ru: change dollars and cents to rubles and kopecks (r:13025)
	mod_say_ru: add ru_ip (r:13026)
	mod_say_ru: Update phrase_ru.xml to include v1.0.8 sound prompts (r:13044,13045)
	mod_say_ru: add russian language support and makefile targets (r:14091)
	mod_say_ru: add mod_say_ru to windows build (r:14246)
	mod_shell_stream: Initial commit (r:13851)
	mod_shout: Fix race conditions; fix stutter when first 64k is drained from shoutcast stream (r:13218)
	mod_shout: bring media back so telecast of a no-media call will work. (MODFORM-28/r:13895)
	mod_shout: add mp3 improvements in seeking, cleanup (MODFORM-32/r:14223)
	mod_skel: add more example code and info (r:12459)
	mod_skypiax: move to trunk (r:12167)
	mod_skypiax: fix hang on invalid mod_skypiax.conf.xml (MODSKYPIAX-21/r:12320-12322,12343)
	mod_skypiax: modified configs/startskype.sh to specify which unix user will start the Skype client instance (r:12937)
	mod_skypiax: fixed assignment before declaration on Windows VC++ (r:12956)
	mod_skypiax: great startup speedup in Windows (using only one Broadcast sendmessage for each interface hunting) (r:13083)
	mod_skypiax: when repeatedly you try to connect to non-existing Skype account in a short period, the Skype client send you back the two halves of the message 'ERROR 92 CALL: Unrecognised identity' inverted in a way that breaks the flux of the API messages. Maybe an anti-spam feature? Anyway, let's try to work around it and restore sanity with a 1 second delay (r:13663)
	mod_skypiax: the Skype client sends us BOTH inband and out_of_band DTMFs, no way to shut the inbands. Let's intercept the out_of_bands ONLY if we are not bridged (eg: IVR, so not to waste CPU in detecting inband), but not propagate the out_of_band DTMFs if we are bridged (inband ones will be propagated) (r:13664)
	mod_skypiax: now it manages the REMOTEHOLD status, when the remote party puts the call on-hold (r:13667)
	mod_skypiax: removed FARMING ifdefs (r:13810)
	mod_skypiax: added XInitThreads() to configs/client.c, now is kind of a reliable tool (r:14125)
	mod_skypiax: added directory 'alsa', contains customized snd-dummy ALSA driver (r:14335)
	mod_skypiax: added directory 'kernel', contains .config file for compilation of kernel ******** (64bit) tickless and 100HZ. (r:14336)
	mod_skypiax: added directory 'configs/multiple-instance-same-skype-username', contains configuration file and script to launch Skype clients with multiple instances of the same skype user (r:14337)
	mod_skypiax: added configs/windows-service directory, contains batch files needed to start skype instances as service, and then start FreeSWITCH (r:14359)
	mod_skypiax: patch from Muhammad Shahzad for adding and removing interfaces on the fly (r:14367)
	mod_skypiax: manage the 'BUSY' call status message (r:14371)
	mod_skypiax: manage the 'WAITING_REDIAL_COMMAND' call status message (r:14372)
	mod_skypiax: patch from Seven Du for hunting IDLE channels in a round-robin way (RR interface vs ANY interface). patch from Seven Du for removing interface as #'interface_id' and #'interface_name'. gmaruzz (meh) patch interface_remove() not to alter the global 'running' variable (it would cause all running signaling and API thread to exit) but to use a newly added tech_pvt->running variable. Also, changed behavior of interface_exists() for correct identification when using #interface_name and #interface_id. PLEASE TEST IT HEAVILY (r:14410)
	mod_skypiax: fixed crash when you set an interface as 'sk' console, then remove the interface, then use the 'sk' command to send a message to the API (it try to use a non-existing interface) (r:14411)
	mod_skypiax: now interprets correctly the_interface whichever it is: interface_id, interface_name, skype_username. And reload works too :-) (r:14414)
	mod_skypiax: fix segfault when used with record_session (MODSKYPIAX-35/r:14444)
	mod_skypiax: fix STATE_PRERING/STATE_DOWN issue (MODSKYPIAX-42/r:14453)
	mod_sndfile: fix off by one error (MODFORM-29/r:13890)
	mod_sndfile:  update pos on the file handle on reads (MODFORM-33/r:14377)
	mod_sofia: allow you to set invite, to and from params independently (r:12287)
	mod_sofia: allow add params to a contact that isn't a gateway (r:12289)
	mod_sofia: add sip_route_uri var to set proxy route (r:12408)
	mod_sofia: add sip_send_to Sofia dial param (MODEND-195/r:12409)
	mod_sofia: add siptrace on/off to sofia command (r:12410,12412)
	mod_sofia: add network_ip and network_port to registration ODBC table and registration event (MODENDP-194/r:12414)
	mod_sofia: add configurable outgoing cid types (r:12427)
	mod_sofia: send SIP INFO DTMF to the bridged leg in bypass media mode (FSCORE-263)
	mod_sofia: add extension-in-contact param for rare cases when provider makes you say a certain contact user (r:12511)
	mod_sofia: add contact-host param to gateways (r:12531)
	mod_sofia: add sip_from_display var and fix backwards switch_strisr calls (r:12566)
	mod_sofia: remove Redundant NTATAG_DEFAULT_PROXY (MODENDP-197/r:12583)
	mod_sofia: reset rtp timer on unhold operation (r:12617)
	mod_sofia: fix G.722 call with broken ptime triggers crash after transfer between contexts (FSCORE-337/r:12647)
	mod_sofia: fix presence subscription not automatically refreshed due to wrong Contact in 202 (refer accepted) response. (MODENDP-201/r:12657)
	mod_sofia: fix random segfaults (FSCORE-322/r:12676)
	mod_sofia: fix registration issue (r:/12715)
	mod_sofia: add username and realm to sip db (r:12785)
	mod_sofia: fix subscriptions  being checked even though it belongs to other host (MODAPP-207/r:12792)
	mod_sofia: reduce exponential mwi notifies (r:12801)
	mod_sofia: don't auto-disable, just warn on RFC2833 in transcoded call (r:12815)
	mod_sofia: add 603 for now (r:12883)
	mod_sofia: make nat options ping configurable and leave it off by default (r:12897)
	mod_sofia: continue_on_fail broken on SVN12881 (MODENDP-209/r:12902)
	mod_sofia: add rtp-autofix-timing (defauts to true even when not present) set to false to disable it (FSCORE-281/r:12903,12904)
	mod_sofia: add set r_sdp_codec_string to list of codecs offered in inbound call dsp (MODENDP-206/r:12946)
	mod_sofia: add sip_call_info variable (SFSIP-138/r:12977)
	mod_sofia: move rtp stats up to where they will be more useful (r:13017)
	mod_sofia: fix FreeSWITCH/SIPX transfer issue (SFSIP-86/r:13032)
	mod_sofia: ndlb_sendrecv_in_session var NDLB-sendrecv-in-session profile option to reverse the effects of MODENDP-148 (r:13084)
	mod_sofia: add sip info to event_socket gateway (r:13088)
	mod_sofia: switch_add_event_header needs a format specifier, or >=gcc-4.3 will be very unhappy (r:13089)
	mod_sofia: add calls count and failed calls count to sofia profile sorted by direction (r:13103)
	mod_sofia: only pass 2833 while bridged and while bridged to another channel that uses our RTP stack (r:13131)
	mod_sofia: send-message-query-on-register profile param, set it to false to disable default of true (r:13139)
	mod_sofia: fix segfault in sofia xmlstatus profile command (MODENDP-214/r:13141)
	mod_sofia: add contact lookup (r:13169)
	mod_sofia: parse out alert-info and call-info from infos (r:13173)
	mod_sofia: add arbitrary notify (r:13187,13189)
	mod_sofia: add experimental NDLB-connectile-dysfunction-2.0 (r:13197~13203)
	mod_sofia: allow register-transport (MODENDP-217/r:13226)
	mod_sofia: fix mem corruption on failed register (r:13229,13230)
	mod_sofia: Ignore ptime when missing instead of using @0i (MODENDP-206/r:13231)
	mod_sofia: fix evil send myself a refer bug (FSCORE-350/r:13237)
	mod_sofia: Add sofia profile params to fine-tune timers (r:13245)
	mod_sofia: Fix incoming anonymous from MetaSwitch will not bridge (SFSIP-144/r:13266)
	mod_sofia: Fix command "sofia xmlstatus profile internal reg" returns invalid xml text (SFSIP-145/r:13267)
	mod_sofia: Allow retrieval of redirect contact uri parameters via channel variable (MODSOFIA-7/r:13271)
	mod_sofia: Fix Polycom SRTP (r:13295)
	mod_sofia: preserve the transport on SLA interactions, still needs work for TLS but should work for UDP and TCP (r:13303)
	mod_sofia: now pickup, barge-in and various other things will work with SLA is turned on (r:13306)
	mod_sofia: Add ability to supply call-id when using SEND_INFO (MODENDP-219/r:13357)
	mod_sofia: Allow sending the flush/reboot all the registration of a domain (MODENDP-187/r:13363)
	mod_sofia: Fix double re-register problem (SFSIP-143/r:13384)
	mod_sofia: make autoflush on bridge the default (r:13389)
	mod_sofia: add contact-user profile param to override default contact username for a profile (MODSOFIA-8/r:13397)
	mod_sofia: add proxy-follow-redirect flag (experimental) (r:13402)
	mod_sofia: add proxy-follow-redirect flag (experimental) (r:13403)
	mod_sofia: Update CID on Polycom when doing an Attended transfer, Make send_display work with Polycom and others, add patch with mods from SFSIP-111 (r:13492/SFSIP-111)
	mod_sofia: handle vegastream broken sip info packets (r:13506)
	mod_sofia: Rewrite sofia_glue_get_user_host (r:13543)
	mod_sofia: Fix NOTIFY message sending incorrect information (MODENDP-225/r:13719)
	mod_sofia: add sofia tracelevel <level> and tracelevel param in <settings> (r:13735)
	mod_sofia: Fix wrong contact header in SIP on incoming calls and NAT (SFSIP-151/r:13736)
	mod_sofia: increase compat on NDLB-to-in-200-contact to handle NAT (r:13759)
	mod_sofia: fix segfault (FSCORE-382/r:13774)
	mod_sofia: Fix multicast replication of sofia registrations (r:13786)
	mod_sofia: don't put params in chan names on sofia urls (r:13841)
	mod_sofia: Fix mod_sofia is not looking fs_path for correct url if endpoint is behind NAT or proxy (MODENDP-221/r:13865)
	mod_sofia: Move sip-allow-multiple-registrations and similar variables from variables to params (MODENDP-222/r:13875)
	mod_sofia: fix segfault on refer with no content-type or body (SFSIP-153/r:13889)
	mod_sofia: fix segfault on reinvite on nomedia call when using uuid_media (r:13894)
	mod_sofia: use the register proxy for the R-URI on register when register-proxy is set (MODSOFIA-12/r:13911)
	mod_sofia: Dereferencing a null pointer causes segfault: sofia_handle_sip_r_invite doesn't check profile->extsipip before use (FSCORE-386/r:13937)
	mod_sofia: preserve transport on route (SFSIP-157/r:13948)
	mod_sofia: do not set route_uri unless there is actually one to go to (r:13952)
	mod_sofia: P-Asserted-Identity and P-Preferred-Identity headers now available as channel vars (BOUNTY-7 and MODENDP-146/r:13977)
	mod_sofia: this one was my fault, it shouldn't set them as sip_h just in case.  Moved it to set the full header into sip_HEADERNAME so you can use it as you see fit or re-export it to a sip_h on the b-leg if needed unchanged. (r:14081)
	mod_sofia: manually handle bye to delay the 200 OK till after the call is torn down (reversible with a define) (r:14121)
	mod_sofia: Have I said how dumb sip is sometimes? (r:14142)
	mod_sofia: fix SFSIP-163, don't blindly copy the extrtpip when ip is defined. (r:14209)
	mod_sofia: add sip_auto_answer_detected var set when we get intercom request on inbound invite. (r:14220)
	mod_sofia: Gateway Config option caller-id-in-from disables/ignores option from-domain (MODSOFIA-16/r:14244)
	mod_sofia: Fix OPTIONS ping sends OPTIONS of many gateways to one gateway (SFSIP-162/r:14249,14250)
	mod_sofia: Fix problem w/OCS after svn14200 with transport=tcp (MODSOFIA-17/r:14251)
	mod_sofia: Send outbound INVITES to outbound proxy (SFSIP-165/r:14263)
	mod_sofia: do not auto-set from-domain value (r:14304)
	mod_sofia: fix MODSOFIA-18 (r:14319)
	mod_sofia: add some err checking to sqlite case for nonce checking (r:14322)
	mod_sofia: Also support 300 redirections (r:14329)
	mod_sofia: Mitel interop (MODSOFIA-15/r:14332)
	mod_sofia: Fix issue of gateway parameters register-proxy and outbound-proxy being interchanged (SFSIP-167/r:14344)
	mod_sofia: Fix Multiple ODBC table creation issues (SFSIP-168/r:14346)
	mod_sofia: Optionally rewrite fs_path element of sip contact string on multicasted registrations (MODENDP-231/r:14353)
	mod_sofia: add sofia_gateway_data api (r:14425)
	mod_spidermonkey: fix segfaults on dtmf callback (FSCORE-327/r:12577)
	mod_spidermonkey: fix error loading mod_spidermonkey due to missing PR_* symbols (r:12934,12939)
	mod_spidermonkey: add destroy method to js (r:13016)
	mod_spidermonkey: Let session.destroy() take the cause as a string too (r:13020)
	mod_spidermonkey: add error checking on switch_core_db_column_text (r:14323)
	mod_spy: add new module, mod_spy (MODAPP-260/r:13035,13036)
	mod_syslog: Keep the indent string in memory (LOGGER-1/r:12852)
	mod_t38gateway: Introduction of the skeleton of a media bug implementing a T.38 gateway, so the
	related infrastructure work can take place around it (r:13082)
	mod_voicemail: remove endless loop on leaving message (MODAPP-233/r:12315-12316)
	mod_voicemail: add indexes to mod_voicemail (r:12341)
	mod_voicemail: fix audio cutoffs in voicemail_leave_main (MODAPP-225/r:12367)
	mod_voicemail: fix profile id mismatch when changing password via menu (MODAPP-236/r:12602)
	mod_voicemail: fix incorrect (NULL) recipient stored for VM on dynamic binding of FS directory. (MODAPP-240/r:12850)
	mod_voicemail: fix segfault on vm_boxcount when box has "sip:" prefix (MODAPP-250/r:12915)
	mod_voicemail: add parameter "login-keys" to allow user to customize which key is used to log in (MODAPP-251/r:12916)
	mod_voicemail: change len to seconds (r:13127)
	mod_voicemail: message-query-exact-match global param in settings section of voicemail to assume profile names match domain names (r:13138)
	mod_voicemail: allow unload and reload of mod_voicemail (MODAPP-177/r:13145)
	mod_voicemail: expand the right variable (r:13272)
	mod_voicemail: rework vm_boxcount api and add an optional profile parameter (r:13283)
	mod_voicemail: add voicemail disk quota (MODAPP-173/r:13314)
	mod_voicemail: abolish mailbox attribute in users and use number-alias attribute instead (MODAPP-218/r:13377,13378,13379,13380,13381)
	mod_voicemail: Implement new config parser (r:13878,13879)
	mod_voicemail: Implement reload in non-blocking way (r:13884)
	mod_voicemail: fix playback control keys being non-functional (MODAPP-302/r:14090)
	mod_voicemail: Fix seg fault and do some cleanup (r:14107)
	mod_voicemail: Flush DTMF Before Tone (MODAPP-312/r:14380)
	mod_voicemail: add option to move to next and previous message(MODAPP-313/r:14386) 
	mod_voicemail: allow Voicemail FF REW key to have configurable millisec interval AND allow REW to limit at the begining of the recording. (MODAPP-311/r:14389)
	mod_voicemail: allow to skip the info section of a message by pressing a key (MODAPP-314/r:14392) 
	mod_voicemail: fix isten to the msg again option after pressing # to stop info msg (MODAPP-316/r:14395)
	mod_unimrcp: add to trunk (r:13788,13789,13790,13791)
	mod_unimrcp: make mod_unimrcp compile with gcc 4.3 (MODUNIMRCP-1/r:13833)
	mod_unimrcp: mrcp_profile for unimrcpserver 0.6.0 (MODASRTTS-18/r:13835)
	mod_unimrcp: use paths referenced to the project file dir (r:13840)
	mod_unimrcp: Added LPCM (16-bit linear PCM) codec, which is used internally in host order; while L16 is RFC3551 defined 16-bit linear PCM codec in network order. (r:13859)
	mod_unimrcp: add speech_channel_set_state_unlocked() (r:14049)
	mod_xml_cdr: Implement optional directory rotation for mod_xml_cdr (XML-6/r:14300)
	mod_xml_cdr: use switch_true instead of atoi (r:14342)
	mod_xml_curl: fix data fetch (MODXMLINT-48/r:12586)
	mod_xml_curl mod_xml_cdr: fix ssl verify peer option and add cookie options (MDXMLINT-51/MDXMLINT-52/r:14208)
	mod_xml_rpc: xmlrpc fails to check security restrictions (MDXMLINT-53/r:13912)
	mod_xml_rpc: add virtual-host (default to true) and default-domain params (MDXMLINT-45/r:14349)
	scripts: Add -days option to set expiration time of certificates in gentls_cert (r:13825)
	scripts: Add zrtp enrollment script, we do auto enrollment and verification (r:13843)
	scripts: Add AT&T System 25 PBX style call pickup (FSSCRIPTS-16/FSSCRIPTS-17/r:13896)
	support: Add print_list gdb macro (r:12687)	
	switch_cpp: fix leak (FSCORE-394/r:14097)


freeswitch (1.0.3)

	build: add targets cd-sounds[-install] and cd-moh[-install] for 48k sounds (r:11151)
	build: autoconf detect odbc library (FSBUILD-8)
	build: fix sound install on windows build (r:11635,11638)
	build: fix configure --sysconfdir (FSBUILD-84)
	build: fix uclibc build (MODLANG-99)
	build: fix adduser in debian (FSBUILD-122, FSBUILD-102)
	core: fix buffering issues (r:11101,11145,11152-11157,11162,11191,11200)
	core: fix c leg no hangup when converting attended to blind transfer before b leg answers (MODENDP-165/r:11061)
	core: fix codec and media handling issues (r:11104) 
	core: fix double close of file handles and add recording of native files (r:11108-11113,11482,11483)
	core: fix file resampling issue (r:11090)
	core: fix incorrect call progress timestamps in timetable (r:11186-11187/FSCORE-268)
	core: fix media handling issues (r:11079-11082)
	core: fix multiple 2833 dtmf handling issues (r:11149,11261,11262,11266,11293,11294,11338/FSCORE-266,FSCORE-273)
	core: send more event types verbos bridge,unbridge,park,unpark (r:11097-11098)
	core: Prevent media setup on failed originates (r:11462/FSCORE-279)
	core: fix recorded soundfiles had random data at end of file (r:11491/MODAPP-205)
	core: fix user for windows service (r:11538/FSCORE-277)
	core: modify variable expansion code to expand in more places and to fix potential security issue from injecting variables (r:11569,11570)
	core: look for soundfiles in more locations based on rate (r:11601/MODFORM-23)
	core: state machine veto behavior changed (r:11610)
	core: add enable_file_write_buffering variable (r:11677)
	core: fix garbled audio on media bug during bridge using stateful codecs (FSCORE-288)
	core: fix tone detect running multiple bugs when detecting multiple tones
	core: add {instant_ringback=true} to make ringback not wait for indication to generate ringback
	core: fix segfault from race condition on multiple reloadxml calls (MOODAPP-211)
	core: modify xml locking so phrases do not lock the xml for the duration of playing them
	core: replace resampler with the speexdsp resampler
	core: fix windows calling convention on threads launched that return a value to fix shutdown segfault (FSCORE-298)
	core: do not auto-export origination_caller_id_* to avoid confusion (r:12052)
	core: API visibility support (GCC/SUNCC) (FSCORE-264)
	core: fix leak in exposed event class serialize method (r:12068)
	core: add volume as possible return value from input callback on embedded languages (r:12114)
	core: add resampler to seech handles (r:12141)
	core: add api.getTime to embedded languages (r:12149)
	freeswitch: allow you to specify -htdocs dir at runtime. (r:11614)
	fs_cli: add "debug" command to change the esl debug level at runtime (r:11057)
	iksemel: update to 1.3 (r:11645)
	libesl: fix disconnect failure (r:11078,11083)
	libesl: fix solaris build (r:11067,11068)
	libesl: add c++ wrapper and swigged wrappers for multiple scripting languages
	libg722_1: fix dct4.h code generator to include the "f" (r:11188-11189,11367)
	libilbc: update to new library from Steve Underwood
	mod_amrwb: add amr wideband passthrough codec (r:11971)
	mod_cepstral: fix failure return code handling (MODASRTTS-9)
	mod_conference: add 'conference xml_list' and 'conference [conf_name] xml_list' (r:11062-11063)
	mod_conference: make conference verbose-events param to control if events have all the channel data or not (r:11073-11077)
	mod_conference: add MINTWO flag to end conference when down to 1 participant (r:11523)
	mod_conference: refactor conference record function (r:11626)
	mod_conference: add conference list summary command (MODAPP-197)
	mod_conference: fix Deadlock or coredump on conference commands play, transfer (MODAPP-209)
	mod_dahdi_codec: added (MODCODEC-7)
	mod_dialplan_xml: make previous auto hunt feature optional and off by defaule use auto_hunt=true session or global variable to enable (r:12144)
	mod_dptools: Add failure_causes channel variable (r:12058)
	mod_easyroute: add configuration file example for custom-query (r:11055)
	mod_easyroute: add custom-query configuration option (r:11054)
	mod_easyroute: fix build error when not configured for odbc (r:11478)
	mod_easyroute: fix memory leak (r:11611)
	mod_erlang_event: add ability to spawn a process (module/function) outbound on a specified node. (r:11460,11477)
	mod_erlang_event: Fix some issues with standing up a new outbound listener and cleaning up after a failed session (r:11479)
	mod_erlang_event: Fix setting up a listener for an outbound session if one doesn't already exist (r:11488)
	mod_erlang_event: add "erlang" fscli command (r:11488)
	mod_erlang_event: Monitor spawned outbound processes for premature exits (r:11489)
	mod_erlang_event: Allow the event encoding strategy to be configurable; choices are string or binary (r:11495)
	mod_erlang_event: Allow certain tuple elements to be binaries or strings, to reduce conversion requirements on the erlang side (r:11496)
	mod_erlang_event: Support sending a message to a registered process to request a pid (when spawning won't cut it) (r:11499)
	mod_erlang_event: Ensure events received while a pid session is being created aren't discarded but are queued instead (r:11500)
	mod_erlang_event: Add freeswitch.erl - An erlang module to make talking to mod_erlang_event more painless (r:11525)
	mod_erlang_event: use rpc:call instead of spawn and to make the registered process argument to handlecall optional (r:11542)
	mod_event_socket: add ability to use a comma sep list of events on event-sink create-listener (r:11056)
	mod_event_socket: add debug logging to event-sink (r:11060)
	mod_event_socket: fix race condition (r:11680,12146)
	mod_dptools: add all modifier to break command (r:11557,11558)
	mod_dptools: add sound_test application (r:11658)
	mod_fax: Dont hangup after sending/receiving faxes
	mod_fifo: pause media bugs while not in a bridge (r:11466,11490)
	mod_fifo: allow unpark during chime list playing (r:11555/MODAPP-206)
	mod_fifo: fix outbound fifos doesn't check if the consumer is in the fifo in question. (r:11561/MODAPP-207)
	mod_fifo: Fix segfault when no argument were supplied to fifo_member call (MODAPP-210)
	mod_lcr: added (r:11180,11184,11532,11609)
	mod_limit: fix memory corruption caused by race condition when using limit hash (r:11070-11071)
	mod_limit: Fix transfer bug, fix leak and make the channel hangup if the extension is \!hangup_cause (r:11604,11932)
	mod_limit: add write different channel variables per realm_id (r:11608)
	mod_limit: Make max argument optional on the limit app, set the limit_usage variable to current count after inserting call in the db (r:11955)
	mod_lua: Create empty argv table when no args are passed to a Lua script (r:11559)
	mod_lua: use dll for lua windows build (FSCORE-299)
	mod_openmrcp: removed (r:11176-11179)
	mod_opal: added
	mod_pocketsphinx: fix leak (r:11974)
	mod_portaudio: fix stuck channels on outbound calls (r:11160,11470,11471,11472,11475,11476,11485)
	mod_python: fix build when site dir is not /usr/lib/python2.4 (r:12070)
	mod_say_en: add short form date/time (MODAPP-180)
	mod_sofia: add auto-rtp-bugs profile option to make rtp bug compensation configurable  (r :11146-11147)
	mod_sofia: add support in sdp for a=maxptime (r:11103)
	mod_sofia: fix codec change race condition (r:11143)
	mod_sofia: fix notify event wasn't allowing content-length 0 (r:11106/MODENDP-167)
	mod_sofia: fix sending extra sdp in 200 OK when using 100rel in violation of sdp o/a protocol draft-ietf-sipping-sip-offeranswer-10 (r:11088)
	mod_sofia: fix sip_auto_answer=true (r:11069)
	mod_sofia: improve outbound registration error message (r:11059)
	mod_sofia: reset media timeout on re-invite (r:11161)
	mod_sofia: fix segfault due to missing contact header in invite (r:11463/MODENDP-177)
	mod_sofia: allow <params> tag in gateways as well as <variables> with direction inbound/outbound (default both) and call counter (r:11468)
	mod_sofia: add support or SLA, works with Polycom and Snom(Sylantro mode). (r:11562/MODENDP-179)
	mod_sofia: tolerate missing user in the request uri (r:11636)
	mod_sofia: Add purpose=gateways and profile=[name] so xml_curl requests make sense (MDXMLINT-46)
	mod_sofia: Add disable-srv and disable-naptr params to sip profiles (default false) (MODENDP-183)
	mod_sofia: add outbound-proxy param (MODENDP-184)
	mod_sofia: fix segfault with stun-enabled=false (SFSIP-120)
	mod_sofia: Profile Name in Expire Event is incorrect (MODENDP-185)
	mod_sofia: add "scrooge" mode to "inbound-codec-negotiation" (r:11881)
	mod_sofia: Add context to reconfig_sofia (r:12080)
	mod_sofia: fix segfault when calling from a Cisco 7940 using bypass_media (FSCORE-301)
	mod_sofia: ilbc to default to 30 if no mode= fmtp is defined on the inbound (r:12110)
	mod_sofia: fix challenge-realm (r:12113)
	mod_sofia: Segmentation fault when running killgw command on sofia profile without specifying a gateway (MODENDP-189)
	mod_sofia: gateways will inherit the context from its parent unless manually provided (r:12138)
	mod_sndfile: Add IMA ADPCM support (MODFORM-22)
	mod_spidermonkey: fix loading of spidermonkey modules (r:11084-11085)
	mod_spidermonkey: block some unwanted behaviours in  session.originate
	mod_spidermonkey_socket: fix gc blocking (MODLANG-97)
	mod_xml_rpc: fixed authentication using @domain syntax (r:11064)
	mod_xml_rpc: fix http content types sent in responses (r:11099,11148,11150)
	mod_voicemail: voicemail insert into the proper fields (MODAPP-190)
	mod_voipcodecs: add  G.726 24k (r:12083)
	sofia-sip: update to current sofia-sip repository
	spandsp: sync to latest snapshot and fix windows build
	speex: updated to 1.2rc1
	sqlite: fix random assert on windows (FSCORE-292)


freeswitch (1.0.2)

	all: don't add module interfaces before returning from error conditions in module load functions (MDXMLINT-36)
	all: fixed multiple memory leaks
	all: improved module unloading/reloading support
	build: add support for --switchconfdir (FSBUILD-84)
	build: fixed netbsd build
	build: make freeswitch stop graceflly with /etc/init.d/freeswitch stop on debian add working dir to start-stop-dir so freeswitch dumps core in workdir
	build: multiple packaging fixes
	build: user freeswitch not added to audio group on deb install (FSBUILD-95)
	Configuration: many updates to default configuration
	core: Add ability to choose uuid from originate string, originate_uuid var (use at your own risk) 
	core: add bridge_generate_comfort_noise option for bridge to generate comfort noise to the A leg when there is no audio on the B leg
	core: add chan vars to param event during hangup hook
	core: add exec directive to xml preprocessor (not available on windows)
	core: add force_transfer_dialplan and force_transfer_context variables
	core: add hashing to event header lookup 
	core: add hits to tone_detect
	core: add last_dtmf_duration variable
	core: add msleep function to swigged languages
	core: add park_after_bridge variable
	core: add per leg timeouts and specific cause codes in reject_on_single_fail
	core: add runtime selection of the module dir (FSCORE-198)
	core: add scheduler support for heartbeat
	core: add session heartbeat feature
	core: add session.destroy psuedo method to sort of destroy a session at least for the sake of FS
	core: add session.unsetInputCallback
	core: add strftime format string validation for user supplied values
	core: add vars param to switch_ivr_originate for recursion (MODAPP-175)
	core: added a "group" concept to the user directory
	core: added ability to do dns lookup to find ip with host: like stun: (FSCORE-219)
	core: added better locking for codec changes during a call
	core: added current_application and current_application_data variables
	core: added error/ magic endpoint so modules can return error causes in situations like sofia_contact
	core: added read_result channel variable
	core: added support for "F" to indicate flash in dtmf (FSCORE-213)
	core: allow calls to be stolen from originate
	core: allow you to get the privacy bits in the caller_profile
	core: change dso code to load symbols local
	core: changes core flags to be array based so we have more
	core: eavesdrop causes the people being eavesdropped on to not hear ach other (MODAPP-140)
	core: expose time table to variable interface via caller field lookup 
	core: fix 100% cpu when sending parked call to moh (FSCORE-234)
	core: fix bridge app to make sure both channels are ready for media when one is only in ringing state
	core: fix buffer overflow (FSCORE-188)
	core: fix conference dial by allowing multiple braces in originate, fix bad pointer op (FSCORE-208)
	core: fix double detection of DTMF in IVR (FSCORE-221)
	core: fix hangup_after_bridge is false on a bridge started with the intercept app
	core: fix issue where pid file is accidentally truncated
	core: fix ivr timeout (FSCORE-181)
	core: fix memory leak in alias tab completion code
	core: fix min digits in read app
	core: fix out-of-bounds pointer in variable expansion (FSCORE-171)
	core: fix segfault in media bugs when in bypass media (FSCORE-193)
	core: fix segfault on gtalk to sip calls (FSCORE-212)
	core: fix segfault on reloadxml (FSCORE-176)
	core: fix segfault on trasfering eavesdopping party (FSCORE-210)
	core: fix segfault using switch_system function (FSCORE-196)
	core: fix session.bridge
	core: fix setting effective_caller_id_name / effective_caller_id_number on bridge dialstring (MODAPP-122)
	core: fix stream_raw_write (MODAPP-145)
	core: fix using resampling on ringback file
	core: fixed performance bottleneck in sqlite db's
	core: fixed race in reloadxml
	core: increment app before execute in case it returns to execute it will go to the next item in the list and not the same
	core: ivr menu max_failures and max_timeouts now default to 3 if not specified or invalid (less than 1) values are specified (FSCORE-244)
	core: ivr_menu max-timeouts option, result in ivr_menu_status var (FSCORE-183)
	core: let b legs use park_after_bridge too
	core: make events less verbose unless verbose_events is set
	core: parse private events during originate
	core: pass pdd data to a leg on oubound calls using bridge
	core: prevent crash in crazy situation with xml interface lookup failures (FSCORE-169)
	core: reduce cpu requirement for generated comfort noise
	core: remove interface names from core db on unload
	core: reworked timing resulting in significant performance increase and better rtp timing
	core: rewrite switch_play_and_get_digits (MODAPP-166)
	core: session.recordFile never terminates (MODLANG-79)
	core: session.transfer make dialplan and context optional
	core: set_user app now sets domain vars as well as user vars
	core: tone_detect not triggering app after tone detection (MODAPP-182)
	core: unprivileged user setting bigger stack for switch_system thread failure (FSCORE-197)
	core: user_exists returns false when fetching a user from XML Curl or other xml interfaces
	libesl: added c event socket library and fs_cli
	libsndfile: fix autoconf 2.62 support (LBSNDF-5)
	mod commands: add "all" modifier to "break" command
	mod_celt: added new module
	mod_commands: Add support for more than 2 variables to uuid_setvar_multi (MODAPP-171)
	mod_commands: Add support for passing the cause of hangup to the uuid_kill command (FSCORE-217)
	mod_commands: add attr lookup to user_data
	mod_commands: add domain_exists fsapi command
	mod_commands: add eval fsapi command
	mod_commands: add flush_dtmf app and uuid_flush_dtmf api command
	mod_commands: add fsctl send_sighup, fsctl shutdown asap, unsched_api commands
	mod_commands: add fsctl shutdown [elegant|restart|cancel]
	mod_commands: add new syntax to uuid_setvar to allow you to unset a var. <uuid> <var> [value]   (MODAPP-167)
	mod_commands: add reload fsapi command to reload a module
	mod_commands: add system fsapi and application (MODAPP-138)
	mod_commands: added hupall fsapi command
	mod_commands: added strftime_tz api command
	mod_commands: break all now stops broadcast too
	mod_commands: fix api command sent through sched_api was getting the last char lopped off
	mod_commands: fix race on transfer with -both
	mod_commands: fix system dialplan app problems (MODAPP-86)
	mod_commands: only send content-type on status when it really is http.
	mod_conference: add fsapi to stop async playback too
	mod_conference: add video caps to mod_conference with video follow audio
	mod_conference: better sound prefix handling when using say: and allow say: on kick sounds.
	mod_conference: fix race in record
	mod_conference: fix runaway thread when floor holder has no video and other people do have video
	mod_conference: fix seg when kicking many members quickly (MODAPP-129)
	mod_conference: fix segfault on invalid chat event
	mod_conference: perpetual sound does not auto-mute, you can do that yourself if you want it 
	mod_dialplan_xml: add Hunt- vars in dialplan lookup after transfer
	mod_dialplan_xml: fail call on extensions with nested conditions
	mod_dingaling: (LBDING-7) fix segfault on os x
	mod_dingaling: end call on ice timeout
	mod_dingaling: fix presence on jabber to be less protocol ambiguous
	mod_dingaling: fix segfault (LBDING-10)
	mod_dingaling: update to support latest client from google
	mod_dptools: add a mechanism to tell if a file played from sendmsg over event socket
	mod_dptools: add playback_terminator support to phrase and say app
	mod_dptools: add playback_terminator_used variable (MODAPP-132)
	mod_dptools: add presence application
	mod_dptools: fix originate api not parsing users properly (FSCORE-246)
	mod_dptools: fix record and record_session to create directory if it does not exist (FSCORE-250)
	mod_dptools: fixed limit and + parsing bug in record_session app (MODAPP-148)
	mod_dptools: remove_bugs added to remove all media bugs on a session
	mod_erlang_event: add new module
	mod_event_socket:  missing : after Content-Length in event socket (MODEVENT-33)
	mod_event_socket: add event socket listener filters
	mod_event_socket: add stateful listener fsapi commands for ajax-y type event interface over http
	mod_event_socket: fix arg parsing errors (MODEVENT-34)
	mod_event_socket: fix shutdown segfault race (MODEVENT-32)
	mod_event_socket: inbound connection to event_socket can now take over an existing channel with 'myevents <uuid>' to take on the behaviour of an outbound socket
	mod_event_socket: let any channel get messages
	mod_event_socket: make event socket wait for hangup on outbound mode and send disconnect message
	mod_expr: fix endless loop
	mod_fax: new module
	mod_fifo: add fifo_consumer_wrapup_time var (MODAPP-117)
	mod_fifo: added callback agents
	mod_fifo: honor keyword silence (MODAPP-118)
	mod_flite: added windows build
	mod_fsv: fix in a windows enviroment opening the record file in text mode. (MODAPP-169)
	mod_http: added new module
	mod_java: updated to new module api to support read/write locks on interface
	mod_limit: accept dialplan context for transfer (MODAPP-161)
	mod_limit: added hashtable based limit functions
	mod_limit: prevent empty error log message (MODAPP-134)
	mod_local_stream: add start_local_stream and stop_local_stream fsapi commands to start/stop dynamically (MODFORM-13)
	mod_local_stream: fix leak and improve error checking
	mod_local_stream: fix seg when no timer name specified in config file. (MODFORM-16)
	mod_loopback: add new module
	mod_lua: add local scripts directory support (MODLANG-86)
	mod_lua: don't eval blank string
	mod_lua: fix originate
	mod_lua: fix segfault (MODLANG-77)
	mod_lua: update to lua 5.1.4 (MODLANG-87)
	mod_lumenvox: removed
	mod_managed: new module replaces mod_mono now supports native .net runtime on windows as well
	mod_opal: added to trunk (still very beta)
	mod_perl: fix segfault (MODLANG-77)
	mod_pocketsphinx: fix rpm build
	mod_portaudio: fix cpu race on inbound call to pa when no ring file is set
	mod_radius_cdr: dictionary update for cause code changes (MODEVENT-27)
	mod_radius_cdr: fix unload (MODEVENT-29)
	mod_shout: add stereo recording broadcast support
	mod_shout: added windows build
	mod_shout: fix segfault when recording mp3's (MODFORM-12)
	mod_shout: improved stability of mp3 decoding
	mod_siren: added new module
	mod_sndfile added support to record 16bit for the various rates including 48kHz
	mod_sofia: Add filter to "sofia status profile XXX" (MODENDP-138)
	mod_sofia: Add force-register-db-domain which works in conjunction with force-register-domain. 
	mod_sofia: Add optional <variables> and <params> tag to <gateway> tag.
	mod_sofia: Challenge the right realm when to_host is outside the users domain. (MODENDP-136)
	mod_sofia: Improve notify messages through a proxy (MODENDP-147)
	mod_sofia: MWI for multiple domains (MODAPP-126)
	mod_sofia: Move "a=sendrecv" from session to media section of SDP (MODENDP-148)
	mod_sofia: add 200 OK re-invite without sdp
	mod_sofia: add custom sofia::gateway_state event (MODENDP-112)
	mod_sofia: add fire events for the refer SIP NOTIFY event package (MODENDP-152)
	mod_sofia: add more params for xml_curl directory lookup
	mod_sofia: add new auto vals for challenge-realm param <param name="challenge-realm" value="auto_from|auto_to|<hardcoded_val>"/>
	mod_sofia: add option to turn of auto_restart of sofia profiles on ip change
	mod_sofia: add params to use sip callid as uuid on inbound calls and uuid as sip callid on outbound calls
	mod_sofia: add parsing of Privacy header for privacy info (MODENDP-133)
	mod_sofia: add proto_specific_hangup_cause to both legs
	mod_sofia: add proxy 3pcc mode
	mod_sofia: add redirect variable to channel as well as partner channe (MODENDP-135)
	mod_sofia: add sip-forbid-register to user params to refuse to let a certian user register
	mod_sofia: add sip: into register-proxy when it's not specified
	mod_sofia: add sip_history_info var for inbound invites.
	mod_sofia: add sip_via_protocol variable
	mod_sofia: add sofia xmlstatus (MODENDP-156)
	mod_sofia: add support for params other than Replaces in Refer-To (MODENDP-143)
	mod_sofia: add support for profiles sharing databases so that you can have a domain that uses multiple profiles for split dns type setups
	mod_sofia: add support for refer transfer involving multiple machines 
	mod_sofia: add support to send a notify in the invite dialog by specifying the uuid of the call. (SFSIP-92)
	mod_sofia: add suppress_from_cidname var to not have display name in from header (MODENDP-153)
	mod_sofia: added sip_hangup_disposition variable
	mod_sofia: allow send_message and notify events to send a message/notify without a body if needed.
	mod_sofia: append -1 .. -N postfix after any X-headers as vars that have the same name
	mod_sofia: cache auth_gateway_name in sofia for challenged bye
	mod_sofia: cancel proxy or no-media mode if you purposely answer or pre_answer
	mod_sofia: correct result code mapping for Unallocated Number (MODENDP-124)
	mod_sofia: disable 100rel by default
	mod_sofia: don't accept crypto in the RTP/AVP (MODENDP-126)
	mod_sofia: don't put CN in sdp answer if it was not in the offer.
	mod_sofia: fix Incorrect IP address shows up in SDP "o" field when multiple external IPs available and FS not bound to first (MODENDP-132)
	mod_sofia: fix Wrong RTP media port destination after reinvite/UNHOLD (SFSIP-82)
	mod_sofia: fix bug on linksys where they lie about the ptime and handle linksys transfer problem
	mod_sofia: fix chat (send an IM) assumes that the user's profile is the same as their domain, which isn't necessarily so (SFSIP-83)
	mod_sofia: fix dtmf handling of broken info dtmf endpoints
	mod_sofia: fix eyebeam presence to be RFC compliant (MODENDP-144)
	mod_sofia: fix ip change detection when in proxy mode
	mod_sofia: fix register_proxy ignoring the paramaters (MODENDP-121)
	mod_sofia: fix remote session refresh triggers request glare (MODENDP-131)
	mod_sofia: fix rtp auto adjust running when it should not
	mod_sofia: fix rtp sent to wrong port after some re-INVITE scenarios (MODENDP-141)
	mod_sofia: fix sending of cn packets across bridge when we shouldn't
	mod_sofia: fix sqlite issue with select of the sip contact
	mod_sofia: fixed segfault on invalid presence payload
	mod_sofia: gateway ping needs to look for 501 (SFSIP-78)
	mod_sofia: handle multi contact register responses and register timeout better
	mod_sofia: improve gateway resilience
	mod_sofia: log ip and port you get reply to invite from
	mod_sofia: make multiple-registations=true use the contact method and call-id option to do it the old way
	mod_sofia: make proxy mode pull the port from m=image as well
	mod_sofia: make register-proxy preserve the url composed from proxy but target the packets to desired address (MODENDP-121)
	mod_sofia: many fixes for sonus rtp issues silence_when_idle=400 chanvar to send generated silence duing sleeps etc
	mod_sofia: many fixes in presence handling
	mod_sofia: passthrough t.38 fixes
	mod_sofia: pick ipv4 or ipv6 based on sipip instead of having mixed in sdp
	mod_sofia: send NOTIFY on TCP/UDP depending on the SUBSCRIBE (SFSIP-104)
	mod_sofia: setting profile option multiple-registrations=contact key multi reg off the contact string
	mod_sofia: wait for a reply on refer
	mod_soundtouch: fixes and improvements, many options changed (MODAPP-149)
	mod_soundtouch: updated to new module api
	mod_spidermonkey: Segmentation fault in check_hangup_hook at mod_spidermonkey.c:1589 (MODLANG-74)
	mod_spidermonkey: fix bug in apiExecute
	mod_spidermonkey: fix memory pool handling and leaks
	mod_spidermonkey: limit recursion busting through the stack (FSCORE-202)
	mod_spidermonkey: make session.getVariable return blank string not the word false 
	mod_spidermonkey_curl: add optional content-type arg
	mod_spidermonkey_odbc: fix numRows and add numCols
	mod_spidermonkey_odbc: fix segfault (MODLANG-75)
	mod_stress: new module for voice stress analysis
	mod_syslog: don't log blank lines (FSCORE-163)
	mod_tone_stream: let silence_stream://0 indicate perpetual silence
	mod_vmd: add new module to detect voicemail "beep"
	mod_voicemail: Add vm_alternate_greet_id param to directory entry (MODAPP-174)
	mod_voicemail: Patch to add voicemail preference controlling date announcement new param 'play-date-announcement' to values 'first' 'last' or 'never'  defaults to first to retain previous behavior (MODAPP-121)
	mod_voicemail: Update mwi light after delete vm via web. (MODAPP-124)
	mod_voicemail: add ability to get to options without listening to every saved message (MODAPP-115)
	mod_voicemail: add ability to skip greeting when leaving a voicemail. (MODAPP-181)
	mod_voicemail: add change-pass-key config file option
	mod_voicemail: add forwarding support
	mod_voicemail: add local dtmf driven alternat vm pass
	mod_voicemail: add proper notification of a vm message being too short
	mod_voicemail: add support for auth via a1-hash
	mod_voicemail: add the "storage-dir" parameter to be set on a per-user basis (MODAPP-133)
	mod_voicemail: add voicemail_greeting_path variable
	mod_voicemail: added voicemail_alternate_greet_id variable
	mod_voicemail: allow changing of password from voicemail to update user directory if using non-static config (MODAPP-156)
	mod_voicemail: created email date (int overflow)  (MODAPP-125)
	mod_voicemail: don't try to deliver vm when no file was recorded. (MODAPP-133)
	mod_voicemail: fix MWI with xml_curl used for directory (MODAPP-176)
	mod_voicemail: fix Voicemail messages occasionally lost / stranded (MODAPP-178)
	mod_voicemail: fix invalid event after message deleted (MODAPP-170)
	mod_voicemail: fix mwi for phones with multiple registrations problem (MODAPP-153)
	mod_voicemail: fix voicemail segfault on incorrect password (FSCORE-187)
	mod_voicemail: fix voicemail_inject error handling (MODAPP-133)
	mod_voicemail: fix voicemail_inject usage api call
	mod_voicemail: improve error checking (MODAPP-142)
	mod_voicemail: localize notification emails (MODAPP-139)
	mod_voicemail: make more multi-domain friendly (MODAPP-162)
	mod_voicemail: make playback created file macros optional (MODAPP-150)
	mod_voicemail: recognize operator key in more places (MODAPP-159)
	mod_voicemail: web interface displays incorrect created / last heard dates (MODAPP-123)
	mod_wanpipe: removed
	mod_xml_cdr: add https support
	mod_xml_cdr: add optional a-leg prefix to xml cdr filenames (MDXMLINT-39)
	mod_xml_cdr: add support for fallback webserver for cdr posting (FSCORE-238)
	mod_xml_curl: Allow specification of HTTP method, and dynamic expansion of variables in URI. (MDXMLINT-41)
	mod_xml_curl: added redirect following (max 10)
	mod_xml_ldap: almost a complete rewrite of this module
	mod_xml_rpc: allow setting of global realm without a global user (MDXMLINT-45)
	mod_xml_rpc: fix multiple segfaults
	mod_xml_rpc: fix segfault on originate via http
	sofia-sip: updated to 1.12.10 (plus a few patches)

freeswitch (1.0.1)

	FIX: prevent intercept race condition that can also be solved with continue_on_fail=originator_cancel
	FIX: NULL dereference detected by klockwork (www.klockwork.com)
	FIX: don't open failed local stream (MODFORM-9)
	FIX: instability in mod_local_stream in failure scenarios
	FIX: xmlrpc-c build on OS X 10.4 (FSBUILD-47)
	ENHANCEMENT: Added tab completion on many api commands in console
	ENHANCEMENT: polycom BLF support
	FIX: many sip NAT related fixes in mod_sofia
	FIX: support sip unregister with Contact: *
	FIX: multiple segfaults in xmlrpc-c
	FIX: sip unregister event being skipped
	FIX: hangup properly on malformed sip 3pcc calls being used as a way to ping
	ADD: enable-3pcc sofia profile param, it is now disabled by default.
	ADD: presence events to sip proxy mode
	ADD: legs param to cdr_csv 
	ADD: support for perl as an embedded lanugage
	ENHANCEMENT: many new api's and functions to the embedded languages including api support, xml interface support, auto start scripts,  and many new objects
	CHANGE: python embedded language api changed to match perl, lua, java
	FIX: many stability fixes in embedded langauges perl, lua, java, python
	ADD: failed_xml_cdr magic channel variable
	FIX: access free memory error in mod_sofia when using respond app
	ENHNACEMENT: make global_setvar only have 2 fields so you can set foo=bar=blah w/o quotes
	FIX: mod_spidermonkey keep hangup hook in the session thread
	ENHANCEMENT: mod_ldap added sasl support and search filters
	ADD: answered, waitForAnswer and mediaReady methods to embedded language Session object
	ENHANCEMENT: mod_voicemail param change to allow notification emails using templates
	ADD: per user acl in sofia
	FIX: deadlock in mod_portaudio
	ENHANCEMENT: blank username in sip will trigger a lookup for the user "nobody"
	ADD: import variable to import variables from a peer channel at time of originate
	FIX: api type fix for c++ modules when incorrectly using enums
	FIX: eliminate need for escaped , in [] on originate
	ADD: NDLB-force-rport option to force behavior as if rport was sent in the via
	ENHANCEMENT: honor execute_on_answer on outbound legs too
	ADD: execute_on_ring variable
	FIX: Seg fault in CoreSession() class destructor
	ADD: per channel caller id in originate
	ADD: sip_outgoing_call_id variable
	FIX: multiple memory leaks in mod_sofia
	FIX: find_local_ip IPv6 support
	ADD: variable expansion to on execute vars.(FSCORE-114)
	ADD: count optional arg to show calls and show channels (MODAPP-103)
	FIX: MODEVENT-25 (WSAWOULDBLOCK error on socket send in windows) in event socket
	FIX: multiple fixes to the logic in mod_say_zh
	ADD: inter digit timeout to swigged embedded languages getDigits method. (MODLANG-65)
	ADD: Linksys P-RTP-Stat SIP header values (SFSIP-66)
	FIX: small leak in core
	ADD: progress_timeout var to originate
	UPDATE: portaudio library
	FIX: added timeout to iax read
	ADD: 'pa rescan' to portaudio to look for new devices
	FIX: wait for broadcast to start when starting async hold to avoid race
	FIX: mod_rss, don't always play the first news feed
	FIX: mod_rss inverval to use the session inteval (audio problems on 30ms channels)
	ADD: Path: support in mod_sofia on register
	FIX: mod_shout record stream
	ENHANCEMENT: mod_voicemail support for effective_caller_id_name/number
	ADD: url encode/decode api calls
	FIX: "nua()" in debug information in sofia instead of the real function name
	FIX: better handling of sips: uris
	FIX: don't seg when using more than SWITCH_MAX_CODECS and bump SWITCH_MAX_CODECS to 50 (we have more than 30 in tree) (MODFORM-10)
	ADD: mod_yaml
	FIX: segfault on freeswitch startup if installed directories are removed
	FIX: segfault when intercept with inbound_late_negotiation=true set
	FIX: dont flood logs with eavesdrop messages (MODAPP-101)
	FIX: don't destroy a codec that has not been created (MODAPP-101)
	ENHANCEMENT: allows the "eavesdrop_group" variable to contain several groups, comma separated.  (MODAPP-101)
	FIX: cross compile (FSBUILD-53)
	FIX: add header that Nuaunce considers mandatory (MODASRTTS-5)
	ADD: write locks to the core and a function to unregister event bindings (adds better ability to unload modules)
	ENHANCEMENT: make modules unbind events and un-reserve subclasses on module unload
	ADD: removable xml hook bindings
	ADD: EventConsumer object to embedded languages so you can make event handlers
	FIX: sending CN with supress-cng true
	FIX: segfault in the event system when trying to remove NULL event
	ADD: flags to turn off srtp auth and rtp auto adj (FSCORE-149 && MODENDP-115)
	FIX: use lighter math and avoid infinite loop in port allocator  (FSCORE-148)
	ENHANCEMENT: let conference pin entry start during prompt (MODAPP-111)
	ADD: mod_pocketsphinx
	FIX: Misuse of SQLRowCount, issues with MSSQL (MODAPP-105)
	FIX: segfaults in mod_python with dtmf callback
	ENHANCEMENT: mod_conference auto-record parameter  (MODAPP-112)
	ENHANCEMENT: reload support to many modules
	FIX: mod_sofia add replaces to supported header
	ENHANCEMENT: add args callback to sleep so you can process dtmf and events while "sleeping"
	ADD: mod_flite
	ENHANCEMENT: switch_xml converted back to c code and support double globs on windows
	ENHANCEMENT: mod_sofia support for adding and removing gateways without restarting profiles
	ADD: extract contact header info into A channel when unhandled 3xx response is received (MODENDP-116)
	FIX: outbound event_socket + late negotiation
	ADD: copy_xml_cdr variable
	ADD: silence_stream (like tone_stream but silent)
	ADD: module_exists api call
	ADD: emailer implementation for windows
	ADD: wait_for_silence application
	FIX: no error message generated if OS is unable to load a module ( due to dependency/installation issues )
	FIX: segfault in media bugs
	FIX: acl lists not correctly matching all ip adresses
	FIX: mod_spidermonkey exit() does not stop script when called from the hangup callback (return "exit" from the callback)
	FIX: mod_syslog works again
	FIX: crash on terminal resize
	FIX: audio problems on big endian
	ENHANCEMENT: Disable multiple registrations on a per-device basis (MODENDP-117)
	ADD: fifo_consumer_exit_key variable (MODAPP-100)
	ADD: cidr based user auth in mod_sofia
	ADD: uuid_send_dtmf fsapi command (MODAPP-114)
	ADD: server registration fiels to sip_registration database (MODENDP-118)
	FIX: use a variable, realm or to host to find gateway when it's not obvious (handles challenged REFER)
	ADD: timeout to curl run in javascript
	ADD: voicemail_inject fsapi command
	ADD: reboot option for sip phones to flush_inboud_reg sofia profile api command
	FIX: add small padding to end of mp3 to avoid cut off mp3 recording
	FIX: patch multiple SDP connection lines in sdp for proxy media mode (MODENDP-109)
	FIX: don't parse ringback varable in proxy situations
	ADD: per call vm recording ext with vm_message_ext variable
	ADD: sip_bye_h prefix to add headers to bye
	ENHANCEMENT: more interfaces available in show fsapi command
	FIX: don't leak in buffers on realloc fail
	FIX: fail out of a conference call if write fails
	ADD: auto ip-change detection
	ADD: mod_snom
	FIX: mod_sofia don't send sipfrag on transfer to cisco so they don't hang up the call



freeswitch (1.0.0)

	Enhanced sofia sip nat handling
	Many fixes found by Klockwork (www.klocwork.com)
	Added disable_app_log variable
	Fixed mod_local_stream with rates on windows
	Fixed finding of files in rate dirs on windows
	Fixed memory corruption from sofia_contact function
	Added sofia profile param NDLB-received-in-nat-reg-contact
	Added sofia profile param aggressive-nat-detection
	Fixed video sip calls in proxy media mode
	Added bridge_terminate_key var
	Update xmlrpc-c lib to trunk revision from upstream, fix windows xmlrpc
	Enhanced nat handling in proxy media mode in sip
	Add progress media to timetable so you can calculate pdd
	Fixed seg when using unicast on socket when call has no read_codec
	Fixed missed log events on busy box
	Added -bleg to intercept
	Enhance configure detection of python
	Fixed build on solaris and freebsd for several modules
	Added param "vm-email-only" to make voicemail sent by email only (previously default behavior)
	Added param "vm-mailto-notify" to allow sending a notification email
	Fixed mod_java build
	Fixed mwi failures for some devices that don't subscribe
	Removed fsapi functions (killchan, transfer, session_displace, reject)
	Removed fsapi functions (session_record, broadcast, hold, media)
	Many updates to sofia-sip library including over 100 fixes
	
freeswitch (1.0.rc6)

	Changed to not allow pass_2833 on transcoded calls (it never worked, now it will tell you)
	Enhanced sofia sip nat handling
	Fix libedit build on solaris
	Fix session timers in mod_sofia
	Fix conference fire-call
	Change: add var_event down into the endpoints so chans with no parents can still pass options
	Added enable-post-var param to xml_rpc
	Fix mod_lua build on solaris
	Many fixes found by Klockwork (www.klocwork.com)
	Add unregister event in mod_sofia
	Enhance python configure detection
	Add vm_boxcount api func
	Fixed att_xfer issue
	Fix sip now includes the Allow-Events header in more places

freeswitch (1.0.rc5)

	Changed internal state names to avoid confusion
	Fixed video negotiation
	Enhanced accuracy of windows timer
	Fixed mod_ldap build
	Added dialplan and context to sql table for channels
	Multiple fixes to mod_lua and mod_perl
	Fixed logic bug in fifo causing segfault
	internal changes to sip stack so we can remove a hash redundant to the stack
	Fixed multiple memory leaks in mod_sofia
	Fixed event fetch segfault on sip subscribe
	Fixed segfault on timer rollover in sofia on 64bit
	Fixed audio timing issues in mod_portaudio
	Changed names of sip profiles in default config to avoid confusion
	Fixed memory usage leak-like behavior when playing files requiring resampling
	Removed some unused api's
	Fix rtp timeout when playing moh
	Removed some un-needed libraries and files from tree
	Fixed multiple issues in sip stack including multiple segfaults
	Added support for sip transfers on bypass_media and proxy_media calls
	Added say application
	Fixed --disable-debug configure option
	Enhanced switch_cpp wrapper (and perl, python, lua, java)
	Fixed segfault on inavalid stun response
	Fixed configure help output
	Fixed segfault on mp3 playback
	Fixed assert on invalid sdp (missing m= line)
	Added configurable windows service name
	Fixed proxy mode call transition to non proxy call
	Fixed solaris build of voipcodecs
	Fixed sofia seg when call failure edge case

freeswitch (1.0.rc4)

	Add tab completion in cli
	Add "inline" dialplan
	Fixed segfault in enum
	Enhance enum to fork dial equal priority entries
	Added auto-reload to enum
	Fixed odbc bug is mod_sofia presence handling
	Add presence for conference and dial an eavesdrop
	Fix stack overflow segfault when recursively parking calls
	Fixed race is sofia registration handling
	Enhance sofia registration, unregister on keep-alive OPTIONS failure
	Added internal routing loop detection/avoidance
	Fixed race in bgapi in event socket
	Fixed vars to execute apps before bridge "bridge_pre_execute_aleg_app" and "bridge_pre_execute_bleg_app"
	Fixed re-setting sound prefix to no prefix after a pharse
	Enhanced setting of bracket vars from originate so they show in the CHANNEL_ORIGINATE event
	Add "enable-timer" and "enable-100rel" options to turn off default behaviors in sofia
	Add originate_timeout to originate vars
	Fixed hanging channels in mod_portaudio
	Added auto time sync on vps migration to different hardware
	Fixed seg on transfer when both legs are not sip
	Added configurable dtmf duration defaults
	Enhanced voicemail, allow interruption of hello message
	Fixed voicemail to not light up light on saved messages
	Enhance mod_amr honor disable dtx in fmtp (MODCODEC-3)
	Fixed bootstrap to install automake dependencies so you can use tarball without same version of automake installed
	Fixed MODLANG-56 (bad audio on originate and javascript streamFile)
	Added hold/unhold dialplan apps
	Enhanced sofia error checking to outlaw 0.0.0.0 in sofia ip params
	Backport fixes from sofia-sip tree
	Fixed MSVC build
	Fixed segfault on sip SUBSCRIBE with Expires: 0
	Added mod_say_zh
	Added --with-pyton and --with-pyton-config configure options
	Added mod_lua
	Enhanced switch_cpp wrapper in core and normalized interfaces for perl, python, lua, and java
	Fixed multiple issues in cpp wrapper and the languages perl, python, lua and java
	Added back mod_perl
	Added sofia gateway option ping to adjust options ping frequency
	Added .net event socket lib to contrib
	Fixed passing of exact response codes of sip across a bridge
	Added mod_reference, reference endpoint module
	Enhanced build so you can now make commented out modules using "make mod_name"
	
freeswitch (1.0.rc3)

        Enhance xml menu system
        Fixes upstream from sofia-sip library
        Enhance mod_fifo
        Added close method to ODBC spidermonkey class
        Fix multiple bugs in the cpp wrapper used in mod_java and mod_python
        Fix hung sip channel issue using respond app or on re-invite with bypass media after 1xx or 2xx responses

freeswitch (1.0.rc2)

	Fixed speex protocol negotiation issues (8k vs 16k)
	Fixed mod_iax race conditions
	Fixed ptime negotiation issues when re-packetizing
	Added ip based acl lists

freeswitch (1.0.rc1)


	

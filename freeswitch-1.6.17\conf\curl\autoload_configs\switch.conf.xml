<configuration name="switch.conf" description="Core Configuration">

  <cli-keybindings>
    <key name="1" value="$${function_key_1}"/>
    <key name="2" value="$${function_key_2}"/>
    <key name="3" value="$${function_key_3}"/>
    <key name="4" value="$${function_key_4}"/>
    <key name="5" value="$${function_key_5}"/>
    <key name="6" value="$${function_key_6}"/>
    <key name="7" value="$${function_key_7}"/>
    <key name="8" value="$${function_key_8}"/>
    <key name="9" value="$${function_key_9}"/>
    <key name="10" value="$${function_key_10}"/>
    <key name="11" value="$${function_key_11}"/>
    <key name="12" value="$${function_key_12}"/>
  </cli-keybindings> 
  
  <settings>
    <!--Colorize the Console -->
    <param name="colorize-console" value="true"/>
    <!--Include full timestamps in dialplan logs -->
    <param name="dialplan-timestamps" value="false"/>
    <!--Most channels to allow at once -->
    <param name="max-sessions" value="1000"/>
    <!--Most channels to create per second -->
    <param name="sessions-per-second" value="30"/>
    <!-- Default Global Log Level - value is one of debug,info,notice,warning,err,crit,alert -->
    <param name="loglevel" value="debug"/>
    <!--Try to catch any crashes that can be recoverable (in the context of a call)-->
    <param name="crash-protection" value="false"/>
    <!--<param name="max_dtmf_duration" value="192000"/>-->
    <!--<param name="default_dtmf_duration" value="8000"/>-->
    <!--
         If you want to send out voicemail notifications via Windows you'll need to change the mailer-app
         variable to the setting below:
     
       <param name="mailer-app" value="msmtp"/>
 
         Donot change mailer-app-args.
         You will also need to download a sendmail clone for Windows (msmtp). This version works without issue:
         http://msmtp.sourceforge.net/index.html. Download and copy the .exe to %winddir%\system32.
         You'll need to create a small config file for smtp credentials (host name, authentication, tls, etc.) in
         %USERPROFILE%\Application Data\ called "msmtprc.txt". Below is a sample copy of this file:
 
         ###################################
         # The SMTP server of the provider.
         account provider
         host smtp.myisp.com
         from <EMAIL>
         auth login
         user johndoe
         password mypassword
 
         # Set a default account
         account default : provider
       ###################################
         
 -->    
    <param name="mailer-app" value="sendmail"/>
    <param name="mailer-app-args" value="-t"/>
    <param name="dump-cores" value="yes"/>
    <!--RTP port range -->
    <!--<param name="rtp-start-port" value="16384"/>-->
    <!--<param name="rtp-end-port" value="32768"/>-->
  </settings>

</configuration>


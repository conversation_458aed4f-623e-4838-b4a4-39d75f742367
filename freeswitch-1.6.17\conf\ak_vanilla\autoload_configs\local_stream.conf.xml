<configuration name="local_stream.conf" description="stream files from local dir">
  <!-- fallback to default if requested moh class isn't found -->
  <directory name="default" path="$${sounds_dir}/music/8000">
    <param name="rate" value="8000"/>
    <param name="shuffle" value="true"/>
    <param name="channels" value="1"/>
    <param name="interval" value="20"/>
    <param name="timer-name" value="soft"/>
    <!-- list of short files to break in with every so often -->
    <!--<param name="chime-list" value="file1.wav,file2.wav"/>-->
    <!-- frequency of break-in (seconds)-->
    <!--<param name="chime-freq" value="30"/>-->
    <!-- limit to how many seconds the file will play -->
    <!--<param name="chime-max" value="500"/>-->
  </directory>

  <directory name="moh/8000" path="$${sounds_dir}/music/8000">
    <param name="rate" value="8000"/>
    <param name="shuffle" value="true"/>
    <param name="channels" value="1"/>
    <param name="interval" value="20"/>
    <param name="timer-name" value="soft"/>
  </directory>

  <directory name="moh/16000" path="$${sounds_dir}/music/16000">
    <param name="rate" value="16000"/>
    <param name="shuffle" value="true"/>
    <param name="channels" value="1"/>
    <param name="interval" value="20"/>
    <param name="timer-name" value="soft"/>
  </directory>

  <directory name="moh/32000" path="$${sounds_dir}/music/32000">
    <param name="rate" value="32000"/>
    <param name="shuffle" value="true"/>
    <param name="channels" value="1"/>
    <param name="interval" value="20"/>
    <param name="timer-name" value="soft"/>
  </directory>

  <directory name="moh/48000" path="$${sounds_dir}/music/48000">
    <param name="rate" value="48000"/>
    <param name="shuffle" value="true"/>
    <param name="channels" value="1"/>
    <param name="interval" value="10"/>
    <param name="timer-name" value="soft"/>
  </directory>

</configuration>

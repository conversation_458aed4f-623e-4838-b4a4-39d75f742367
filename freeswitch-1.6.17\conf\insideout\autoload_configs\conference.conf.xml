<!-- http://wiki.freeswitch.org/wiki/Mod_conference --> 
<!-- None of these paths are real if you want any of these options you need to really set them up -->
<configuration name="conference.conf" description="Audio Conference">
  <!-- Advertise certain presence on startup . -->
  <advertise>
    <room name="3001@$${domain}" status="FreeSWITCH"/>
  </advertise>

  <!-- These are the default keys that map when you do not specify a caller control group -->	
  <!-- Note: none and default are reserved names for group names -->	
  <caller-controls>
    <group name="default">
      <control action="mute" digits="0"/>
      <control action="deaf mute" digits="*"/>
      <control action="energy up" digits="9"/>
      <control action="energy equ" digits="8"/>
      <control action="energy dn" digits="7"/>
      <control action="vol talk up" digits="3"/>
      <control action="vol talk zero" digits="2"/>
      <control action="vol talk dn" digits="1"/>
      <control action="vol listen up" digits="6"/>
      <control action="vol listen zero" digits="5"/>
      <control action="vol listen dn" digits="4"/>
      <control action="hangup" digits="#"/>
    </group>
  </caller-controls>

  <!-- Profiles are collections of settings you can reference by name. -->
  <profiles>
    <!--If no profile is specified it will default to "default"-->
    <profile name="default">
      <!-- Domain (for presence) -->
      <param name="domain" value="$${domain}"/>
      <!-- Sample Rate-->
      <param name="rate" value="8000"/>
      <!-- Number of milliseconds per frame -->
      <param name="interval" value="20"/>
      <!-- Energy level required for audio to be sent to the other users -->
      <param name="energy-level" value="300"/>

      <!--Can be | delim of waste|mute|deaf waste will always transmit data to each channel
          even during silence -->
      <!--<param name="member-flags" value="waste"/>-->

      <!-- Name of the caller control group to use for this profile -->
      <!-- <param name="caller-controls" value="some name"/> -->
      <!-- TTS Engine to use -->
      <!--<param name="tts-engine" value="cepstral"/>-->
      <!-- TTS Voice to use -->
      <!--<param name="tts-voice" value="david"/>-->

      <!-- If TTS is enabled all audio-file params beginning with -->
      <!-- 'say:' will be considered text to say with TTS -->
      <!-- Set a default path here so you can use relative paths in the other sound params-->
      <param name="sound-prefix" value="$${base_dir}/sounds/en/us/callie"/>
      <!-- File to play to acknowledge succees -->
      <!--<param name="ack-sound" value="beep.wav"/>-->
      <!-- File to play to acknowledge failure -->
      <!--<param name="nack-sound" value="beeperr.wav"/>-->
      <!-- File to play to acknowledge muted -->
      <param name="muted-sound" value="conference/conf-muted.wav"/>
      <!-- File to play to acknowledge unmuted -->
      <param name="unmuted-sound" value="conference/conf-unmuted.wav"/>
      <!-- File to play if you are alone in the conference -->
      <param name="alone-sound" value="conference/conf-alone.wav"/>
      <!-- File to play endlessly (nobody will ever be able to talk) -->
      <!--<param name="perpetual-sound" value="perpetual.wav"/>-->
      <!-- File to play when you're alone (music on hold)-->
      <param name="moh-sound" value="$${hold_music}"/>
      <!-- File to play when you join the conference -->
      <param name="enter-sound" value="tone_stream://%(200,0,500,600,700)"/>
      <!-- File to play when you leave the conference -->
      <param name="exit-sound" value="tone_stream://%(500,0,300,200,100,50,25)"/>
      <!-- File to play when you ae ejected from the conference -->
      <param name="kicked-sound" value="conference/conf-kicked.wav"/>
      <!-- File to play when the conference is locked -->
      <param name="locked-sound" value="conference/conf-locked.wav"/>
      <!-- File to play when the conference is locked during the call-->
      <param name="is-locked-sound" value="conference/conf-is-locked.wav"/>
      <!-- File to play when the conference is unlocked during the call-->
      <param name="is-unlocked-sound" value="conference/conf-is-unlocked.wav"/>
      <!-- File to play to prompt for a pin -->
      <param name="pin-sound" value="conference/conf-pin.wav"/>
      <!-- File to play to when the pin is invalid -->
      <param name="bad-pin-sound" value="conference/conf-bad-pin.wav"/>
      <param name="comfort-noise-level" value="1400"/>
      <!-- Conference pin -->
      <!--<param name="pin" value="12345"/>-->
      <!-- Default Caller ID Name for outbound calls -->
      <param name="caller-id-name" value="$${outbound_caller_name}"/>
      <!-- Default Caller ID Number for outbound calls -->
      <param name="caller-id-number" value="$${outbound_caller_id}"/>
      <!-- Suppress start and stop talking events -->
      <!-- <param name="suppress-events" value="start-talking,stop-talking"/> -->
      <!-- enable comfort noise generation -->
      <param name="comfort-noise" value="true"/>
      <!-- Uncomment auto-record to toggle recording every conference call. -->
      <!-- Another valid value is   shout://user:<EMAIL>/live.mp3   -->
      <!--
      <param name="auto-record" value="$${base_dir}/recordings/${conference_name}_${strftime(%Y-%m-%d-%H-%M-%S)}.wav"/>
      -->
    </profile>

    <profile name="wideband">
      <param name="domain" value="$${domain}"/>
      <param name="rate" value="16000"/>
      <param name="interval" value="20"/>
      <param name="energy-level" value="300"/>
      <param name="sound-prefix" value="$${base_dir}/sounds/en/us/callie"/>
      <param name="muted-sound" value="conference/conf-muted.wav"/>
      <param name="unmuted-sound" value="conference/conf-unmuted.wav"/>
      <param name="alone-sound" value="conference/conf-alone.wav"/>
      <param name="moh-sound" value="$${hold_music}"/>
      <param name="enter-sound" value="tone_stream://%(200,0,500,600,700)"/>
      <param name="exit-sound" value="tone_stream://%(500,0,300,200,100,50,25)"/>
      <param name="kicked-sound" value="conference/conf-kicked.wav"/>
      <param name="locked-sound" value="conference/conf-locked.wav"/>
      <param name="is-locked-sound" value="conference/conf-is-locked.wav"/>
      <param name="is-unlocked-sound" value="conference/conf-is-unlocked.wav"/>
      <param name="pin-sound" value="conference/conf-pin.wav"/>
      <param name="bad-pin-sound" value="conference/conf-bad-pin.wav"/>
      <param name="caller-id-name" value="$${outbound_caller_name}"/>
      <param name="caller-id-number" value="$${outbound_caller_id}"/>
      <param name="comfort-noise" value="true"/>
      <!--<param name="tts-engine" value="flite"/>-->
      <!--<param name="tts-voice" value="kal16"/>-->
    </profile>

    <profile name="ultrawideband">
      <param name="domain" value="$${domain}"/>
      <param name="rate" value="32000"/>
      <param name="interval" value="20"/>
      <param name="energy-level" value="300"/>
      <param name="sound-prefix" value="$${base_dir}/sounds/en/us/callie"/>
      <param name="muted-sound" value="conference/conf-muted.wav"/>
      <param name="unmuted-sound" value="conference/conf-unmuted.wav"/>
      <param name="alone-sound" value="conference/conf-alone.wav"/>
      <param name="moh-sound" value="$${hold_music}"/>
      <param name="enter-sound" value="tone_stream://%(200,0,500,600,700)"/>
      <param name="exit-sound" value="tone_stream://%(500,0,300,200,100,50,25)"/>
      <param name="kicked-sound" value="conference/conf-kicked.wav"/>
      <param name="locked-sound" value="conference/conf-locked.wav"/>
      <param name="is-locked-sound" value="conference/conf-is-locked.wav"/>
      <param name="is-unlocked-sound" value="conference/conf-is-unlocked.wav"/>
      <param name="pin-sound" value="conference/conf-pin.wav"/>
      <param name="bad-pin-sound" value="conference/conf-bad-pin.wav"/>
      <param name="caller-id-name" value="$${outbound_caller_name}"/>
      <param name="caller-id-number" value="$${outbound_caller_id}"/>
      <param name="comfort-noise" value="true"/>
      <!--<param name="tts-engine" value="flite"/>-->
      <!--<param name="tts-voice" value="kal16"/>-->
    </profile>
    <!--
    <profile name="cdquality">
      <param name="domain" value="$${domain}"/>
      <param name="rate" value="48000"/>
      <param name="interval" value="10"/>
      <param name="energy-level" value="300"/>
      <param name="sound-prefix" value="$${base_dir}/sounds/en/us/callie"/>
      <param name="muted-sound" value="conference/conf-muted.wav"/>
      <param name="unmuted-sound" value="conference/conf-unmuted.wav"/>
      <param name="alone-sound" value="conference/conf-alone.wav"/>
      <param name="moh-sound" value="$${hold_music}"/>
      <param name="enter-sound" value="tone_stream://%(200,0,500,600,700)"/>
      <param name="exit-sound" value="tone_stream://%(500,0,300,200,100,50,25)"/>
      <param name="kicked-sound" value="conference/conf-kicked.wav"/>
      <param name="locked-sound" value="conference/conf-locked.wav"/>
      <param name="is-locked-sound" value="conference/conf-is-locked.wav"/>
      <param name="is-unlocked-sound" value="conference/conf-is-unlocked.wav"/>
      <param name="pin-sound" value="conference/conf-pin.wav"/>
      <param name="bad-pin-sound" value="conference/conf-bad-pin.wav"/>
      <param name="caller-id-name" value="$${outbound_caller_name}"/>
      <param name="caller-id-number" value="$${outbound_caller_id}"/>
      <param name="comfort-noise" value="true"/>
    </profile>
    -->
  </profiles>
</configuration>

<include>
 <user id="SEP001120AABBCC">
   <params>
    <!-- for devices requesting firmware via SCCP, like ATA186
    <param name="skinny-firmware-version" value="ATA030101SCCP04"
    <param name="skinny-soft-key-set-set" value="default"
    -->
    <param name="foo" value="bar"/>
   </params>
   <skinny>
    <buttons>
      <!--
      position: 1..
      type: one of line, speed-dial
      label: button label
      -->
      <!--
      value is the directory number (or user)
      caller-name is shown to the calling party during call
      -->
      <button position="1" type="Line" label="Line 1" value="1101" caller-name="Calling as 1101"/>
      <button position="3" type="Line" label="Shared Line 10" value="1110" caller-name="Calling as 1110"/>
      <!--
      value is the directory number to call
      -->
      <button position="5" type="SpeedDial" label="Call 1001" value="1001"/>
      <!--
      value is the URL
      -->
      <button position="6" type="ServiceUrl" label="Some URL" value="http://phone-xml.berbee.com/menu.xml"/>
    </buttons>
   </skinny>
  </user>
</include>




1、修改configure.ac
   把eval confdir="${sysconfdir}/freeswitch" 改为eval confdir="${sysconfdir}"
2、修改Makefile.am Makefile.in
   把samples-conf: config-vanilla  改为 samples-conf: config-ak_vanilla
   把config-%:目标里面的find查找文件改为全部查找find . -name \*
3、
./rebootstrap.sh

./configure --prefix=$AKCS_PBX_INSTALL_PATH --sysconfdir=$AKCS_PBX_INSTALL_PATH/conf --with-scriptdir=$AKCS_PBX_INSTALL_PATH/scripts \
   --with-scriptdir=$AKCS_PBX_INSTALL_PATH/log  --with-dbdir=$AKCS_PBX_INSTALL_PATH/db --with-rundir= $AKCS_PBX_INSTALL_PATH/run \
   CPPFLAGS=-I$AKCS_PBX_AK_SRC/pbxmod/include LDFLAGS=-L$AKCS_PBX_AK_SRC/pbxmod/lib 

make
make install




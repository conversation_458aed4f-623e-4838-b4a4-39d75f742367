package.path = package.path..";/usr/local/freeswitch/scripts/?.lua"
local akcs_util = require("akcs_util")

-- 1欧洲 3美国 9中国 
gateway_num = 0	--安装时替换
function SipDisableLog(msg)
  local date=os.date("%Y-%m-%d %H:%M:%S");
  local file = io.open("/usr/local/freeswitch/sip_enable.log","a")
  file:write(date.." "..msg)
  file:close()
end

-- 生成trace_id
trace_id = akcs_util.generateTraceId(17)
session:setVariable("session_trace_id", trace_id);

-- callkit唯一标识,INVITE时带给app
session:setVariable("sip_h_X-Trace-ID", trace_id)

--freeswitch.consoleLog("NOTICE","lua akcs call dialplan...\n");
caller = session:getVariable("caller_id_number");
--多套房场景下用主站点的sip发起呼叫
session:setVariable("main_site_caller", caller);
caller_name_display = session:getVariable("caller_id_name");
callee = session:getVariable("destination_number");
--是否是跨地域的opensips集群，is_opensips_cluster为true代表现在处于第二台fs
is_opensips_cluster = session:getVariable("is_opensips_cluster");
--新版本APP新增Caller-Site头,用来处理多套房公用一个sip的情况,若不存在Caller-Site头则设置为caller
caller_site = session:getVariable("akcs_caller_site");
if(caller_site == "" or caller_site == nil) then
    caller_site = caller;
else
    caller_main_site = freeswitch.request_main_site_sip(caller_site, trace_id);
    --如果Caller-Site的主站点和实际的caller一致才可替换
    if(caller == caller_main_site) then
        caller = caller_site;
    end
end


if(gateway_num == 9) then  -- 中国
	session:setVariable("is_landline_final","true");
else
	session:setVariable("is_landline_final","false");
end

local dbh = freeswitch.Dbh("freeswitch","dbuser01","Ak@56@<EMAIL>");

caller_name = caller_name_display;
caller_num = caller;
local my_query_caller = string.format("select devicenode,communityid,groupname,type,communityType, deviceAttribute,sipEnable,opensipsNode,deleted from userinfo where username='%s' limit 1", caller);
local my_query_callee = string.format("select devicenode,communityid,groupname,type,communityType,deviceAttribute,sipEnable,opensipsNode from userinfo where username='%s' limit 1", callee);
local my_query_caller_smarthome = string.format("select username,groupname,groupring,opensipsNode from smarthome_userinfo where username='%s' limit 1", caller);
--查询数据库找到对应的节点信息
--主叫可能是纯家居的sip  被叫一定是对讲的sip
check_smarthome = 1;
assert(dbh:connected());
dbh:query(my_query_callee,function(row)
   req_callee_devicenode=string.format("%s",row.devicenode);
   req_callee_groupname=string.format("%s",row.groupname);
   req_callee_communityid=string.format("%s",row.communityid);
   req_callee_type=string.format("%s",row.type);
   req_callee_communityType=string.format("%s",row.communityType);
   req_callee_deviceAttribute=string.format("%s",row.deviceAttribute);
   req_callee_sipEnable=string.format("%s",row.sipEnable);
   req_callee_opensips_node=string.format("%s",row.opensipsNode);
end);
dbh:query(my_query_caller,function(row)
   --查到主叫是对讲的sip 说明是对讲的呼叫
   check_smarthome = 0;
   req_caller_devicenode=string.format("%s",row.devicenode);
   req_caller_groupname=string.format("%s",row.groupname);
   req_caller_communityid=string.format("%s",row.communityid);
   req_caller_type=string.format("%s",row.type);
   req_caller_communityType=string.format("%s",row.communityType);
   req_caller_deviceAttribute=string.format("%s",row.deviceAttribute);
   req_caller_sipEnable=string.format("%s",row.sipEnable);
   req_caller_opensips_node=string.format("%s",row.opensipsNode);
   req_caller_deleted=string.format("%s",row.deleted);
end);
if (check_smarthome) then
    dbh:query(my_query_caller_smarthome,function(row)
       req_caller_groupname=string.format("%s",row.groupname);
       req_caller_opensips_node=string.format("%s",row.opensipsNode);
    end);
end    
dbh:release();

--akcs呼叫规则
if (req_caller_deleted == "1") then
    freeswitch.consoleLog("notice", " call failed. caller sip deleted. sip is:" .. caller .. "\n");
	return
end

call_enable = 0;
if (req_caller_communityType == "1" and req_callee_groupname == req_caller_groupname) then
    call_enable = 1;--单住户同个家庭能互呼
elseif (req_caller_communityType == "0" and req_callee_communityid == req_caller_communityid) then
	call_enable = 1;--同个社区下能互通
elseif (check_smarthome and req_callee_groupname == req_caller_groupname) then    
    call_enable = 1;
end	

-- 欧洲特殊处理：2019年我们产品还没有社区的，用单住户扩展为社区来用遗留的数据
if (gateway_num == 1) then
    arr = {1001,1024,1048,1063,1071,1079,108,1081,1147,171,177,179,189,190,202,218,242,243,355,371,376,386,417,
    429,433,448,480,484,499,507,511,539,55,555,558,570,607,664,668,690,696,705,707,711,742,752,83,844,847,849,887,889,919,979}
    for i = 0, #arr do
        if arr[i] == tonumber(req_callee_communityid) then
            call_enable = 1
        end
    end  
end  


if (call_enable == 1) then
    callee_landline = "";
    if(gateway_num == 9 or req_callee_type == "7") then -- 中国
        land_result = freeswitch.request_land_number(callee,0); --0代表通过sip号查询 1代表通过sip群组号查询
        freeswitch.consoleLog("notice", "china callee_land is:"..land_result.." reg_callee_type is:"..req_callee_type.." callee is "..callee.."\n");
        if(land_result ~= "") then
            data = akcs_util.split(land_result, '-');        
            phone_code = data[1];	
            callee_landline = data[2];	
            session:setVariable("is_landline_final","true");
            if(phone_code == "86") then		
                callee_landline = ",sofia/gateway/ippbx/"..callee_landline;
            elseif(phone_code == "81") then
                callee_landline = ",sofia/gateway/twilio-credentials-jp/"..phone_code..callee_landline;			
            else 
                callee_landline = ",sofia/gateway/twilio-credentials/"..phone_code..callee_landline;				
            end
        end
    end
    
    --呼叫	
    if(req_caller_opensips_node == "" or req_callee_opensips_node == "" or is_opensips_cluster == "true" or req_callee_opensips_node  == req_caller_opensips_node) then
        if(req_callee_type == "6" or req_callee_type == "7") then
            session:setVariable("initial_callee_id_number", callee);
            callee_fake = freeswitch.request_main_site_sip(callee, trace_id);
            freeswitch.consoleLog("notice", "request_main_site_sip callee is: "..callee.." callee_fake is: "..callee_fake.." trace_id is: "..trace_id.."\n");
            if(callee_fake ~= "" and callee_fake ~= nil) then
                session:setVariable("original_"..callee_fake, callee);
                callee = callee_fake;         
            end
        end

        --通话记录需要caller_id_number
        session:setVariable("caller_id_number", caller);
        --dtmf开门白名单需要effective_caller_id_number
        session:setVariable("effective_caller_id_number", caller);
        
        callstring = "user/"..callee..callee_landline;
        session:execute("bridge",callstring);
    else
        callstring = "sofia/gateway/"..req_callee_opensips_node.."/"..callee..callee_landline;
        session:execute("bridge",callstring);
    end
elseif(call_enable == 0) then
    freeswitch.consoleLog("notice", " call reject. cross family or project\n");
    return 1;
end

<?xml version="1.0" encoding="utf-8"?>
   <extension name="akcs_cluster_groupcall">
      <condition field="destination_number" expression="^[1-9]\d*[0]\d{5}$">
        <action application="export" data="dialed_extension=${destination_number}"/>
        <action application="set" data="transfer_ringback=$${hold_music}"/>
        <action application="set" data="call_timeout=90"/>
        <action application="set" data="hangup_after_bridge=true"/>
        <!--<action application="set" data="continue_on_fail=NORMAL_TEMPORARY_FAILURE,USER_BUSY,NO_ANSWER,TIMEOUT,NO_ROUTE_DESTINATION"/> -->
        <action application="set" data="inherit_codec=true"/>
        <action application="set" data="continue_on_fail=true"/>
        <action application="set" data="execute_on_answer=sched_hangup +360 alloted_timeout" />
        <action application="export" data="nolocal:rtcp_mux=0"/>
        <action application="hash" data="insert/${domain_name}-call_return/${dialed_extension}/${caller_id_number}"/>
        <action application="hash" data="insert/${domain_name}-last_dial_ext/${dialed_extension}/${uuid}"/>
        <action application="set" data="called_party_callgroup=${user_data(${dialed_extension}@${domain_name} var callgroup)}"/>
        <action application="hash" data="insert/${domain_name}-last_dial_ext/${called_party_callgroup}/${uuid}"/>
        <action application="hash" data="insert/${domain_name}-last_dial_ext/global/${uuid}"/>
        <action application="hash" data="insert/${domain_name}-last_dial/${called_party_callgroup}/${uuid}"/>
        <action application="export" data="origination_caller_id_name=${caller_id_name"/>
        <condition field="destination_number" expression="^(\d{15})$">
            <action application="lua" data="akcs_cluster_smarthome_groupcall.lua"/>
            <anti-action application="lua" data="akcs_cluster_groupcall.lua"/>
        </condition>
      </condition>
    </extension>	
</include>

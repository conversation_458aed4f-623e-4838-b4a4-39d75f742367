<!--
    NOTICE:

    This context is usually accessed via the external sip profile sitting on port 5080.
    
    It is recommended to have separate inbound and outbound contexts.  Not only for security
    but clearing up why you would need to do such a thing.  You don't want outside un-authenticated
    callers hitting your default context which allows dialing calls thru your providers and results 
    in Toll Fraud.
-->

<!-- http://wiki.freeswitch.org/wiki/Dialplan_XML -->
<include>
  <context name="public">

    <extension name="unloop">
      <condition field="$${unroll_loops}" expression="^true$"/>
      <condition field="${sip_looped_call}" expression="^true$">
	<action application="deflect" data="${destination_number}"/>
      </condition>
    </extension>
    <!--
	Tag anything pass thru here as an outside_call so you can make sure not
	to create any routing loops based on the conditions that it came from 
	the outside of the switch.  
    -->
    <extension name="outside_call" continue="true">
      <condition>
	<action application="set" data="outside_call=true"/>
      </condition>
    </extension>

    <extension name="call_debug" continue="true">
      <condition field="$${call_debug}" expression="^true$" break="never">
	<action application="info"/>
      </condition>
    </extension>

    <!-- Try to get domain_name from the sip_req_params variable -->
    <extension name="set_domain" continue="true">
      <condition field="${domain_name}" expression="^$"/>
      <condition field="source" expression="mod_sofia"/>
      <condition field="${sip_req_params}" expression="domain_name=([A-Z-a-z0-9.]+)">
	<!-- We need to export this so the B-Leg will have it after transfer too. -->
	<action application="export" data="domain_name=$1"/>
	<anti-action application="export" data="domain_name=${sip_req_host}"/>
      </condition>
    </extension>

    <extension name="public_extensions">
      <condition field="destination_number" expression="^(10[01][0-9])$">
	<action application="transfer" data="$1 XML default"/>
      </condition>
    </extension>
    
    <!--
	You can place files in the public directory to get included.
    -->
    <X-PRE-PROCESS cmd="include" data="public/*.xml"/>
    <!--
	If you have made it this far lets challenge the caller and if they authenticate
	lets try what they dialed in the default context. (commented out by default)
    -->
    <!--
    <extension name="check_auth" continue="true">
      <condition field="${sip_authorized}" expression="^true$" break="never">
	<anti-action application="respond" data="407"/>
      </condition>
    </extension>
    
    <extension name="transfer_to_default">
      <condition>
	<action application="transfer" data="${destination_number} XML default"/>
      </condition>
    </extension>
    -->
  </context>
</include>

<include>
  <macro name="funny_prompts" pause="750">
    <input pattern="(.*)">
      <match>
        <action function="play-file" data="ivr/ivr-wakey_wakey_sunshine.wav"/>
        <action function="play-file" data="ivr/ivr-no_no_no.wav"/>
        <action function="play-file" data="ivr/ivr-did_you_mean_to_press_key.wav"/>
        <action function="play-file" data="ivr/ivr-seriously_mean_to_press_key.wav"/>
        <action function="play-file" data="ivr/ivr-oh_whatever.wav"/>
        <action function="play-file" data="ivr/ivr-one_more_mistake.wav"/>
        <action function="play-file" data="ivr/ivr-congratulations_you_pressed_star.wav"/>
        <action function="play-file" data="ivr/ivr-engineers_busy_assisting_other_sales.wav"/>
        <action function="play-file" data="ivr/ivr-message_self_destruct.wav"/>
        <action function="play-file" data="ivr/ivr-all_your_call_are_belong_to_us.wav"/>
        <action function="play-file" data="ivr/ivr-love_those_touch_tones.wav"/>
        <action function="play-file" data="ivr/ivr-yes_we_have_no_bananas.wav"/>
        <action function="play-file" data="ivr/ivr-dude_you_suck.wav"/>
        <action function="play-file" data="ivr/ivr-on_hold_indefinitely.wav"/>
        <action function="play-file" data="ivr/ivr-youre_doing_it_wrong.wav"/>
        <action function="play-file" data="ivr/ivr-were_asterisk_free.wav"/>
        <action function="play-file" data="ivr/ivr-douche_telecom.wav"/>
        <action function="play-file" data="ivr/ivr-asterisk_like_syphilis.wav"/>
        <action function="play-file" data="ivr/ivr-freeguipy.wav"/>
        <action function="play-file" data="ivr/ivr-terribly_wrong_awkward.wav"/>
        <action function="play-file" data="ivr/ivr-it_was_that_bug.wav"/>
        <action function="play-file" data="ivr/ivr-concentrate.wav"/>
        <action function="play-file" data="ivr/ivr-founder_of_freesource.wav"/>
        <action function="play-file" data="ivr/ivr-cold_foolish.wav"/>
        <action function="play-file" data="ivr/ivr-trollover_minutes.wav"/>
        <action function="play-file" data="ivr/ivr-yuno_silent_drill.wav"/>
        <action function="play-file" data="ivr/ivr-beacuase.wav"/>
      </match>
    </input>
  </macro>
</include>
#
# FreeSWITCH auto-build Makefile (Solaris 11.1 64bit Tonka Truck Edition)
# http://www.freeswitch.org
# put this file anywhere and type gmake to
# create a fully-built freeswitch.git from scratch
# in that same directory.
#
#
FSPREFIX=/usr/local/freeswitch
PREFIX=/usr/local/
DOWNLOAD=http://files.freeswitch.org/downloads/libs
JP=v8d
SSL=1.0.1j
SQLITE=autoconf-3080403
PCRE=8.35
CURL=7.35.0
SPEEX=1.2rc1
LIBEDIT=20140618-3.1
LDNS=1.6.17
PKGCFG=0.28
PERL=5.18.2

freeswitch: deps has-git freeswitch.git/Makefile
	cd freeswitch.git && gmake

freeswitch.git/Makefile: freeswitch.git/configure
	export PATH=$PATH:$(PREFIX)/bin
	cd freeswitch.git && PKG_CONFIG_PATH=$(PREFIX)/lib/pkgconfig ./configure --enable-64 LDFLAGS='-L$(PREFIX)/lib -Wl,-rpath=$(PREFIX)/lib' CFLAGS='-I$(PREFIX)/include' --prefix=$(FSPREFIX)

freeswitch.git/configure: freeswitch.git/bootstrap.sh
	cd freeswitch.git && sh bootstrap.sh

freeswitch.git/bootstrap.sh: has-git
	test -d freeswitch.git || git clone https://freeswitch.org/stash/scm/fs/freeswitch.git freeswitch.git

install: freeswitch
	cd freeswitch.git && gmake install cd-sounds-install cd-moh-install

install-git:
	pkg install git autoconf automake libtool libjpeg gcc-45 wget

has-git: 
	@git --version || (echo "please install git by running 'gmake install-git'" && false)

clean:
	@rm -rf openssl* ldns* jpeg* pcre* perl* pkg-config* speex* sqlite* libedit* curl* *~
	(cd freeswitch.git && git clean -fdx && git reset --hard HEAD && git pull)

libjpeg: jpeg-8d/.done

jpeg-8d/.done:
	(test -d jpeg-8d) || (wget -4 -O jpegsrc.$(JP).tar.gz $(DOWNLOAD)/jpegsrc.$(JP).tar.gz && tar zxfv jpegsrc.$(JP).tar.gz)
	(cd jpeg-8d && CFLAGS=-m64 LDFLAGS=-m64 ./configure --prefix=$(PREFIX) && gmake && sudo gmake install && touch .done)

openssl: openssl-$(SSL)/.done
openssl-$(SSL)/.done: openssl-$(SSL)
openssl-$(SSL):
	(test -d $@) || (wget -4 -O $@.tar.gz $(DOWNLOAD)/$@.tar.gz && tar zxfv $@.tar.gz)
	(cd $@ && CFLAGS=-m64 LDFLAGS=-m64 ./Configure --prefix=$(PREFIX) solaris64-x86_64-gcc shared && gmake && sudo gmake install && touch .done)

sqlite: sqlite-$(SQLITE)/.done
sqlite-$(SQLITE)/.done: sqlite-$(SQLITE)
sqlite-$(SQLITE):
	(test -d $@) || (wget -4 -O $@.tar.gz $(DOWNLOAD)/$@.tar.gz && tar zxfv $@.tar.gz)
	(cd $@ && CFLAGS=-m64 LDFLAGS=-m64 ./configure --prefix=$(PREFIX) && gmake && sudo gmake install && touch .done)

pcre: pcre-$(PCRE)/.done
pcre-$(PCRE)/.done: pcre-$(PCRE)
pcre-$(PCRE):
	(test -d $@) || (wget -4 -O $@.tar.gz $(DOWNLOAD)/$@.tar.gz && tar zxfv $@.tar.gz)
	(cd $@ && CXXFLAGS=-m64 CFLAGS=-m64 LDFLAGS=-m64 ./configure --prefix=$(PREFIX) && gmake && sudo gmake install && touch .done)

curl: curl-$(CURL)/.done
curl-$(CURL)/.done: curl-$(CURL)
curl-$(CURL):
	(test -d $@) || (wget -4 -O $@.tar.gz $(DOWNLOAD)/$@.tar.gz && tar zxfv $@.tar.gz)
	(cd $@ && CFLAGS=-m64 LDFLAGS=-m64 ./configure --prefix=$(PREFIX) && gmake && sudo gmake install && touch .done)

speex: speex-$(SPEEX)/.done
speex-$(SPEEX)/.done: speex-$(SPEEX)
speex-$(SPEEX):
	(test -d $@) || (wget -4 -O $@.tar.gz $(DOWNLOAD)/$@.tar.gz && tar zxfv $@.tar.gz)
	(cd $@ && CFLAGS=-m64 LDFLAGS=-m64 ./configure --prefix=$(PREFIX) && gmake && sudo gmake install && touch .done)

libedit: libedit-$(LIBEDIT)/.done
libedit-$(LIBEDIT)/.done: libedit-$(LIBEDIT)
libedit-$(LIBEDIT):
	(test -d $@) || (wget -4 -O $@.tar.gz $(DOWNLOAD)/$@.tar.gz && tar zxfv $@.tar.gz)
	(cd $@ && CFLAGS=-m64 LDFLAGS=-m64 ./configure --prefix=$(PREFIX) && gmake && sudo gmake install && touch .done)

ldns: openssl ldns-$(LDNS)/.done
ldns-$(LDNS)/.done: ldns-$(LDNS)
ldns-$(LDNS):
	(test -d $@) || (wget -4 -O $@.tar.gz $(DOWNLOAD)/$@.tar.gz && tar zxfv $@.tar.gz)
	(cd $@ && CFLAGS=-m64 LDFLAGS=-m64 ./configure --with-ssl=$(PREFIX) --prefix=$(PREFIX) && gmake && sudo gmake install && touch .done)

pkg-config: openssl pkg-config-$(PKGCFG)/.done
pkg-config-$(PKGCFG)/.done: pkg-config-$(PKGCFG)
pkg-config-$(PKGCFG):
	(test -d $@) || (wget -4 -O $@.tar.gz $(DOWNLOAD)/$@.tar.gz && tar zxfv $@.tar.gz)
	(cd $@ && CFLAGS=-m64 LDFLAGS=-m64 ./configure --with-internal-glib --prefix=$(PREFIX) && sudo gmake uninstall && gmake && sudo gmake install && \
	touch .done)

perl: openssl perl-$(PERL)/.done
perl-$(PERL)/.done: perl-$(PERL)
perl-$(PERL):
	(test -d $@) || (wget -4 -O $@.tar.gz $(DOWNLOAD)/$@.tar.gz && tar zxfv $@.tar.gz)
	(cd $@ && CFLAGS=-m64 LDFLAGS=-m64 ./configure.gnu -Dcc=gcc --prefix=$(PREFIX) && gmake && sudo gmake install && touch .done)

deps: has-git libjpeg openssl sqlite pcre curl speex libedit ldns pkg-config perl

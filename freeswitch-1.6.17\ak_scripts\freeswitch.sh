#!/bin/bash
ACMD="$1"
FREESWITCH_BIN="/usr/local/freeswitch/bin/freeswitch"

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_freeswitch()
{
	nohup $FREESWITCH_BIN >/dev/null 2>&1 &
    if [ -z "`ps -fe|grep "freeswitchrun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/freeswitch/scripts/freeswitchrun.sh >/dev/null 2>&1 &
    fi
	echo "Start freeswitch successful"
}

stop_freeswitch()
{
    echo "Begin to stop freeswitchrun.sh"
    freeswitchrunid=`ps aux | grep -w freeswitchrun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${freeswitchrunid}" ];then
	    echo "freeswitch.sh is running at ${freeswitchrunid}, will kill it first."
	    kill -9 ${freeswitchrunid}
    fi
    echo "Begin to stop freeswitch"
    kill -9 `pidof freeswitch`
    sleep 2
    echo "Stop freeswitch successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep 5060 | grep freeswitch | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_freeswitch
    else
        echo "freeswitch is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep 5060 | grep freeswitch | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "freeswitch is already stopping"
    else
        stop_freeswitch
    fi
    ;;
  restart)
    stop_freeswitch
    sleep 1
    start_freeswitch
    ;;
  status)
    cnt=`ss -alnp | grep 5060 | grep freeswitch | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m freeswitch is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m freeswitch is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit
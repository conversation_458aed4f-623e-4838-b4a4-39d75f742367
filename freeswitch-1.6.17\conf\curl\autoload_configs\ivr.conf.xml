<configuration name="ivr.conf" description="IVR menus">
  <menus>
    <!-- demo IVR setup -->
    <!-- demo IVR, Main Menu -->
    <menu name="demo_ivr"
          greet-long="phrase:demo_ivr_main_menu"
          greet-short="phrase:demo_ivr_main_menu_short"
          invalid-sound="ivr/ivr-that_was_an_invalid_entry.wav"
          exit-sound="voicemail/vm-goodbye.wav"
          timeout ="10000"
	  inter-digit-timeout="2000"
	  max-failures="3"
	  max-timeouts="3"
	  digit-len="4">
      <entry action="menu-exec-app" digits="1" param="bridge sofia/$${domain}/<EMAIL>"/>
      <entry action="menu-exec-app" digits="2" param="transfer 9996 XML default"/>    <!-- FS echo -->
      <entry action="menu-exec-app" digits="3" param="transfer 9999 XML default"/>    <!-- MOH -->
      <entry action="menu-sub" digits="4" param="demo_ivr_submenu"/>  <!-- demo sub menu -->
      <entry action="menu-exec-app" digits="5" param="transfer 1234*256 enum"/>    <!-- Screaming monkeys -->
      <entry action="menu-exec-app" digits="/^(10[01][0-9])$/" param="transfer $1 XML features"/> 
      <entry action="menu-top" digits="9"/>          <!-- Repeat this menu -->
    </menu>
    <!-- Demo IVR, Sub Menu -->
    <menu name="demo_ivr_submenu"
        greet-long="phrase:demo_ivr_sub_menu"
        greet-short="phrase:demo_ivr_sub_menu_short"
        invalid-sound="ivr/ivr-that_was_an_invalid_entry.wav"
        exit-sound="voicemail/vm-goodbye.wav"
        timeout="15000"
        max-failures="3"
        max-timeouts="3">
      <entry action="menu-top" digits="*"/>
     </menu>

  </menus>


</configuration>


<!-- TTS sample; non-functional but it demonstrates say: and TTS -->
<!--
    <menu name="demo3"
          greet-long="say:Press 1 to join the conference, Press 2 to join the other conference"
          greet-short="say:Press 1 to join the conference, Press 2 to join the other conference"
          invalid-sound="say:invalid extension"
          exit-sound="say:exit sound"
          timeout ="15000"
          max-failures="3">
      <entry action="menu-exit" digits="*"/>
      <entry action="menu-playback" digits="1" param="say:You pressed 1"/>
      <entry action="menu-call-transfer" digits="2" param=


"1000"/>
      <entry action="menu-call-transfer" digits="3" param="1001"/>
    </menu>
-->

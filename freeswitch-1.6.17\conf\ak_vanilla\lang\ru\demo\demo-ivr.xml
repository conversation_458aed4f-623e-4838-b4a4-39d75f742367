<include>

  <macro name="demo_ivr_count">
    <input pattern="^(\d+)$">
      <match>
        <action function="play-file" data="voicemail/vm-you_have.wav"/>
        <action function="say" data="$1" method="pronounced" type="name_spelled"/>
        <action function="play-file" data="voicemail/vm-messages.wav"/>
      </match>
    </input>
  </macro>

  <macro name="demo_ivr_main_menu" pause="250"> <!-- See conf/autoload_config/ivr.conf.xml for an example on how to use this macro in an IVR -->
    <input pattern="(.*)">
      <match>
        <!-- string together several existing sound files to create one long greeting -->
        <action function="play-file" data="ivr/ivr-welcome_to_freeswitch.wav"/>
        <action function="play-file" data="ivr/ivr-this_ivr_will_let_you_test_features.wav"/>
        <action function="play-file" data="ivr/ivr-you_may_exit_by_hanging_up.wav"/>
        <!-- note that you can do more than just play files, e.g. have pauses and do TTS -->

        <!-- Menu option 1: Call FreeSWITCH conference-->
        <action function="play-file" data="ivr/ivr-enter_ext_pound.wav"/>
        <action function="execute" data="sleep(1500)"/>
        <action function="play-file" data="ivr/ivr-to_call_the_freeswitch_conference.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/1.wav"/>

        <!-- Menu option 2: Do FreeSWITCH echo test -->
        <action function="play-file" data="ivr/ivr-to_do_a_freeswitch_echo_test.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/2.wav"/>

        <!-- Menu option 3: Listen to Music on Hold -->
        <action function="play-file" data="ivr/ivr-to_listen_to_moh.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/3.wav"/>

        <!-- Menu option 4: Hear a sample submenu -->
        <action function="play-file" data="ivr/ivr-to_hear_sample_submenu.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/4.wav"/>

        <!-- Menu option 5: Listen to screaming monkeys -->
        <action function="play-file" data="ivr/ivr-to_hear_screaming_monkeys.wav"/>

        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/5.wav"/>

        <!-- Menu option 9: Repeat these options -->
        <action function="play-file" data="ivr/ivr-to_repeat_these_options.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/9.wav"/>
        <action function="execute" data="sleep(2000)"/>
      </match>
    </input>
  </macro>

  <!-- The following macro is the same as demo_ivr_main_menu except it is the "short" version -->
  <!-- The short version has all the options but not the initial greeting -->
  <macro name="demo_ivr_main_menu_short" pause="250">
    <input pattern="(.*)">
      <match>
        <!-- Menu option 1: Call FreeSWITCH conference-->
        <action function="execute" data="sleep(1000)"/>
        <action function="play-file" data="ivr/ivr-to_call_the_freeswitch_conference.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/1.wav"/>

        <!-- Menu option 2: Do FreeSWITCH echo test -->
        <action function="play-file" data="ivr/ivr-to_do_a_freeswitch_echo_test.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/2.wav"/>

        <!-- Menu option 3: Listen to Music on Hold -->
        <action function="play-file" data="ivr/ivr-to_listen_to_moh.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/3.wav"/>

        <!-- Menu option 4: Hear a sample submenu -->
        <action function="play-file" data="ivr/ivr-to_hear_sample_submenu.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/4.wav"/>

        <!-- Menu option 5: Listen to screaming monkeys -->
        <action function="play-file" data="ivr/ivr-to_hear_screaming_monkeys.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/5.wav"/>

        <!-- Menu option 9: Repeat these options -->
        <action function="play-file" data="ivr/ivr-to_repeat_these_options.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/9.wav"/>
        <action function="execute" data="sleep(2000)"/>
      </match>
    </input>
  </macro>

  <!-- The following macro is the "long" greeting for the demo_ivr_sub_menu -->
  <macro name="demo_ivr_sub_menu">
    <input pattern="(.*)">
      <match>
        <action function="play-file" data="ivr/ivr-welcome_to_freeswitch.wav"/>
        <action function="play-file" data="ivr/ivr-sample_submenu.wav"/>

        <!-- Menu option *: Return to top menu -->
        <action function="play-file" data="ivr/ivr-to_return_to_previous_menu.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/star.wav"/>

      </match>
    </input>
  </macro>

  <!-- The following macro is the same as demo_ivr_sub_menu except it is the "short" version -->
  <!-- The short version has all the options but not the initial greeting -->
  <macro name="demo_ivr_sub_menu_short">
    <input pattern="(.*)">
      <match>
        <!-- Menu option *: Return to top menu -->
        <action function="play-file" data="ivr/ivr-to_return_to_previous_menu.wav"/>
        <action function="play-file" data="ivr/ivr-please.wav"/>
        <action function="play-file" data="voicemail/vm-press.wav"/>
        <action function="play-file" data="digits/star.wav"/>

      </match>
    </input>
  </macro>

</include>
<!--
For Emacs:
Local Variables:
mode:xml
indent-tabs-mode:nil
tab-width:2
c-basic-offset:2
End:
For VIM:
vim:set softtabstop=2 shiftwidth=2 tabstop=2 expandtab:
-->

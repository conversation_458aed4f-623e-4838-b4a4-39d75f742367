#!/bin/sh
#fs日志配置/usr/local/freeswitch/conf/autoload_configs/logfile.conf.xml
#50M=52428800 保存100个
#按现在美国的日志看一天一个G的日志，大约可以保存5天的日志

#大于10M 说明还没有压缩进行压缩处理  只要压缩不大于10M就可以用这个条件进行处理
if [ `ls -l --block-size=k /usr/local/freeswitch/log/freeswitch.log.1 | awk '{print $5}' |  tr -cd "[0-9]"` -gt 10240 ]; then
    gzip /usr/local/freeswitch/log/freeswitch.log.1
    mv /usr/local/freeswitch/log/freeswitch.log.1.gz /usr/local/freeswitch/log/freeswitch.log.1
fi
#解压时候要重命名回去
#cp /usr/local/freeswitch/log/freeswitch.log.1 /root/freeswitch.log.1.gz
#gunzip /root/freeswitch.log.1.gz


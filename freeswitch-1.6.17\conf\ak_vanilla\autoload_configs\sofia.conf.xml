<configuration name="sofia.conf" description="sofia Endpoint">

  <global_settings>
    <param name="log-level" value="0"/>
    <!-- <param name="auto-restart" value="false"/> -->
    <param name="debug-presence" value="0"/>
    <!-- <param name="capture-server" value="udp:homer.domain.com:5060"/> -->
    
    <!-- 
    	the new format for HEPv2/v3 and capture ID    
	
	protocol:host:port;hep=2;capture_id=200;

    -->
    
	<param name="capture-server" value="udp:127.0.0.1:8535;hep=3;capture_id=100"/>
	<param name="is-single-evn" value="0"/>
  </global_settings>

  <!--
      The rabbit hole goes deep.  This includes all the
      profiles in the sip_profiles directory that is up
      one level from this directory.
  -->
  <profiles>
    <X-PRE-PROCESS cmd="include" data="../sip_profiles/*.xml"/>
  </profiles>

</configuration>

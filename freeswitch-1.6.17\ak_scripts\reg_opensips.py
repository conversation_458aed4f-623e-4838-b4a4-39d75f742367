#!/usr/bin/python3

import argparse
import json
import subprocess
import logging
import requests
from logging import handlers
from configobj import ConfigObj


class Logger(object):
    level_relations = {
        'debug': logging.DEBUG,
        'info': logging.INFO,
        'warning': logging.WARNING,
        'error': logging.ERROR,
        'crit': logging.CRITICAL
    }  # 日志级别关系映射

    def __init__(self, filename, level='info', when='D', backCount=6,
                 fmt='%(asctime)s - %(pathname)s[line:%(lineno)d] - %(levelname)s: %(message)s'):
        self.logger = logging.getLogger(filename)
        format_str = logging.Formatter(fmt)  # 设置日志格式
        self.logger.setLevel(self.level_relations.get(level))  # 设置日志级别
        sh = logging.StreamHandler()  # 往屏幕上输出
        sh.setFormatter(format_str)  # 设置屏幕上显示的格式
        th = handlers.TimedRotatingFileHandler(filename=filename, when=when, backupCount=backCount,
                                               encoding='utf-8')  # 往文件里写入#指定间隔时间自动生成文件的处理器
        # 实例化TimedRotatingFileHandler
        # interval是时间间隔，backupCount是备份文件的个数，如果超过这个个数，就会自动删除，when是间隔的时间单位，单位有以下几种：
        # S 秒
        # M 分
        # H 小时、
        # D 天、
        # W 每星期（interval==0时代表星期一）
        # midnight 每天凌晨
        th.setFormatter(format_str)  # 设置文件里写入的格式
        self.logger.addHandler(sh)  # 把对象加到logger里
        self.logger.addHandler(th)


log = Logger('/var/log/reg_opensips.log', level='debug')


def exec_shell(cmd, executer='/bin/bash'):
    # status, output = subprocess.getstatusoutput(cmd)
    p = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True, executable=executer)

    output = ''
    while True:
        buff = p.stdout.readline()
        if buff == b'' and p.poll() is not None:
            break
        output = output + buff.decode('utf8')
    p.wait()
    status = p.returncode
    if status:
        output = (p.stderr.read()).decode('utf8')
    return status, output


class Operation(object):
    def __init__(self, port, ops):
        self.port = port
        self.opensips_inner_ip, self.local_inner_ip = self.read_server_conf()
        self.opensips_inner_ip = ops
        self.basic_url = "http://%s:%d" % (self.opensips_inner_ip, self.port)
        self.ipv6 = self.get_server_ipv6()
        log.logger.debug("OPENSIPS_INNER_IP=%s,LOCAL_INNER_IP=%s,IPV6=%s,BASIC_URL=%s" % (
            self.opensips_inner_ip, self.local_inner_ip, self.ipv6, self.basic_url))

    def online(self):
        log.logger.debug("online start")
        if self.local_inner_ip == "" or self.opensips_inner_ip == "":
            log.logger.debug("local_inner_ip is null or opensips_inner_ip is null,end")
        json_data = dict()
        json_data["cmd"] = "online"
        json_data["local_inner_ip"] = self.local_inner_ip
        r = requests.get(self.basic_url, params=json_data)
        log.logger.debug("status_code=%d,content=%s" % (r.status_code, r.content))

        if len(self.ipv6) > 0:
            json_data["local_inner_ip"] = "[%s]" % self.ipv6
            r = requests.get(self.basic_url, params=json_data)
            log.logger.debug("status_code=%d,content=%s" % (r.status_code, r.content))
        log.logger.debug("online end")

    def offline(self):
        log.logger.debug("offline start")
        if self.local_inner_ip == "" or self.opensips_inner_ip == "":
            log.logger.debug("local_inner_ip is null or opensips_inner_ip is null,end")
        json_data = dict()
        json_data["cmd"] = "offline"
        json_data["local_inner_ip"] = self.local_inner_ip
        r = requests.get(self.basic_url, params=json_data)
        log.logger.debug("status_code=%d,content=%s" % (r.status_code, r.content))

        if len(self.ipv6) > 0:
            json_data["local_inner_ip"] = "[%s]" % self.ipv6
            r = requests.get(self.basic_url, params=json_data)
            log.logger.debug("status_code=%d,content=%s" % (r.status_code, r.content))

        log.logger.debug("offline end")

    def register_ipv4(self, free_switch_ip):
        log.logger.debug("register_ipv4 start,ip=" + free_switch_ip)
        if free_switch_ip == "" or self.opensips_inner_ip == "":
            log.logger.debug("free_switch_ip is null or opensips_inner_ip is null,end")
        json_data = dict()
        json_data["cmd"] = "regipv4"
        json_data["local_inner_ip"] = free_switch_ip
        r = requests.get(self.basic_url, params=json_data)
        log.logger.debug("status_code=%d,content=%s" % (r.status_code, r.content))
        log.logger.debug("register_ipv4 end")

    def register_ipv6(self, free_switch_ip):
        log.logger.debug("register_ipv6 start,ip=" + free_switch_ip)
        if free_switch_ip == "" or self.opensips_inner_ip == "":
            log.logger.debug("free_switch_ip is null or opensips_inner_ip is null,end")
        json_data = dict()
        json_data["cmd"] = "regipv6"
        json_data["local_inner_ip"] = "[%s]" % self.ipv6
        r = requests.get(self.basic_url, params=json_data)
        log.logger.debug("status_code=%d,content=%s" % (r.status_code, r.content))
        log.logger.debug("register_ipv6 end")

    def read_server_conf(self):
        config = ConfigObj("/usr/local/freeswitch/server.conf")
        opensips_inner_ip = config["OPENSIPS_INNER_IP"]
        config_etc_ip = ConfigObj("/etc/ip")
        local_inner_ip = config_etc_ip["SERVER_INNER_IP"]
        log.logger.debug("read /usr/local/freeswitch/server.conf,/etc/ip,item is OPENSIPS_INNER_IP,SERVER_INNER_IP");
        return opensips_inner_ip, local_inner_ip

    def get_server_ipv6(self):
        config_etc_ip = ConfigObj("/etc/ip")
        local_ipv6 = config_etc_ip["SERVERIPV6"]
        log.logger.debug("read /etc/ip,item is SERVERIPV6")
        return local_ipv6


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="freeswitch register opensips")
    parser.add_argument(
        "-o",
        "--operation",
        default="",
        help="operation=online/offline/regipv4/regipv6 freeswitch to opensips",
        choices=['online', 'offline', 'regipv4', 'regipv6'],
    )

    parser.add_argument(
        "-f",
        "--fsip",
        default="",
        help="register freeswitch ip to opensips,when regipv4/regipv6 use",
    )

    parser.add_argument(
        "-p",
        "--port",
        type=int,
        default=8525,
        help="Specify the port on which the server listens,this argument is optional,default is 8525",
    )
    
    parser.add_argument(
        "-s",
        "--ops",
        default="",
        help="register to opensips inner ip",
    )

    args = parser.parse_args()
    operation = Operation(args.port, args.ops)
    oper_args = args.operation

    if oper_args == 'online':
        operation.online()
    elif oper_args == 'offline':
        operation.offline()
    elif oper_args == 'regipv4':
        free_switch_ip = args.fsip
        operation.register_ipv4(free_switch_ip)
    elif oper_args == 'regipv6':
        free_switch_ip = args.fsip
        operation.register_ipv6(free_switch_ip)
    else:
        log.logger.debug("invalid operation:" + oper_args)

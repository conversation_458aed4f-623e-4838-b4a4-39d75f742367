The Initial Developer of the Original Code is
<PERSON> <<EMAIL>>
Portions created by the Initial Developer are Copyright (C)
the Initial Developer. All Rights Reserved.

The PRIMARY AUTHORS are (and/or have been):

 <PERSON> <<EMAIL>> - Primary developer of all core components
 	and many of the included modules.  Much of freeswitch is based on his work.
 
 <PERSON> <<EMAIL>> - Windows porter and responsible for the 
  windows\msvc build system.


And here is an inevitably incomplete list of MUCH-APPRECIATED CONTRIBUTORS --
people who have submitted patches, reported bugs, and generally made Freeswitch
that much better:

 <PERSON> - For countless hours of work on BSD and Mac support, finding countless bugs, 
  and moral support.  Xcode project files.
 <PERSON> - <krice AT freeswitch.org> - Stable Branch Maintainer, xmlcdr, sofia improvements, load testing, 1 liners here and there.
 <PERSON> <<EMAIL>> - git migration, Debian packaging, ZRTP integration, mod_prefix, and many other improvements
 <PERSON> - For his help making mod_exosip possible (which we are now getting rid of but oh well), 
               and for just being a swell guy!
 <PERSON><PERSON> "cyp<PERSON><PERSON>" <PERSON> (michal.bi<PERSON><PERSON>  AT voiceworks.pl) - Solaris porting, and autotools enhancements, debian, rpm and solaris packaging.
 <PERSON> <<EMAIL>> - All around cool guy (mod_syslog)
 Johny Kadarisman <<EMAIL>>
 Yossi Neiman of Cartis Solutions, Inc. <freeswitch AT cartissolutions.com>  -  implementation of mod_cdr (perldd, mysql, csv)
 Stefan Knoblich - Sofia TLS, various patches and support.  Thanks.
 Justin Unger - <justinunger at gmail dot com> Lots of help with patches and SIP testing. Thanks! 
 Paul D. Tinsley - Various patches and support. <pdt at jackhammer.org> 
 Neal Horman <neal at wanlink dot com> - conference improvements, switch_ivr menu additions and other tweaks.
 Johny Kadarisman <jkr888 at gmail.com> - mod_python fixups.
 Michael Murdock <mike at mmurdock dot org> - testing, documentation, bug finding and usability enhancements.
 Matt Klein <<EMAIL>>
 Jonas Gauffin <jonas at gauffin dot org> - mod_cdr_odbc, mod_spidermonkey_socket, Bugfixes and additions in mod_spidermonkey_odbc and mod_spidermonkey, .net event socket library.
 Damjan Jovanovic <moctodliamgtavojtodnajmad backwards> - mod_java
 Juan Jose Comellas <<EMAIL>> - Patch to switch_utils for arg parsing.
 Dale Thatcher <freeswitch at dalethatcher dot com> - Additions to mod_conference.
 Simon Perreault & Marc Blanchet from Viagenie.ca - IPv6 Support.
 Peter Olsson <<EMAIL>> - mod_v8, and other various patches.
 Seven Du <<EMAIL>> - core video stuff and video transcoding related modules, and other patches here and there.

A big THANK YOU goes to:

Justin Cassidy - Build related cleanups and automatic build setup.
Bret McDanel - Javascript Documentation, constant feedback and input, many other things I am sure I am forgetting.

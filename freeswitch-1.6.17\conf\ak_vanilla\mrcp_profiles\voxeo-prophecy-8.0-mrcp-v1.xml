<include>
  <!-- Voxeo Prophecy 8.0 MRCPv1 -->
  <profile name="voxeo-prophecy8.0-mrcp1" version="1">
    <param name="server-ip" value="************"/>
    <param name="server-port" value="554"/>
    <param name="resource-location" value=""/>
    <param name="speechsynth" value="synthesizer"/>
    <param name="speechrecog" value="recognizer"/>
    <!--param name="rtp-ext-ip" value="auto"/-->
    <param name="rtp-ip" value="auto"/>
    <param name="rtp-port-min" value="4000"/>
    <param name="rtp-port-max" value="5000"/>
    <!--param name="playout-delay" value="50"/-->
    <!--param name="max-playout-delay" value="200"/-->
    <!--param name="ptime" value="20"/-->
    <param name="codecs" value="PCMU PCMA L16/96/8000"/>

    <!-- Add any default MRCP params for SPEAK requests here -->
    <synthparams>
    </synthparams>

    <!-- Add any default MRCP params for RECOGNIZE requests here -->
    <recogparams>
      <!--param name="start-input-timers" value="false"/-->
    </recogparams>
  </profile>
</include>

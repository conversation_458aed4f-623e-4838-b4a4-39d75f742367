#!/bin/bash
PROCESS_NAME="freeswitch"
PROCESS_START_CMD="/usr/local/freeswitch/bin/freeswitch -nonat"
LOG_FILE=/var/log/freeswitchrun.log
PROCESS_PID_FILE=/usr/local/freeswitch/run/freeswitch.pid

PROCESS_COMMON_SCRIPTS="/usr/local/freeswitch/scripts/common.sh"
source $PROCESS_COMMON_SCRIPTS

#指定odbc库路径 不然会出现odbc连接不到库问题
export  LD_LIBRARY_PATH=/usr/local/odbc/lib:/usr/local/freeswitch/akcslibs:$LD_LIBRARY_PATH

while [ 1 ]
do
    common_run_pid_detect $PROCESS_NAME $PROCESS_PID_FILE "$PROCESS_START_CMD" $LOG_FILE
    COMMON_FIRST_RUN=0
	sleep 5
done

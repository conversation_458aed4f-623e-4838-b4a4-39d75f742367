package.path = package.path..";/usr/local/freeswitch/scripts/?.lua"
local akcs_util = require("akcs_util")

-- 生成trace_id
trace_id = akcs_util.generateTraceId(18)
session:setVariable("session_trace_id", trace_id);

--freeswitch.consoleLog("NOTICE","lua akcs group call dialplan...\n");
caller = session:getVariable("caller_id_number");
session:setVariable("main_site_caller", caller);
callee = session:getVariable("destination_number");
caller_name = session:getVariable("caller_id_name");

session:setVariable("is_landline_final","false");
session:setVariable("is_only_smarthome","1");

local dbh = freeswitch.Dbh("freeswitch","dbuser01","Ak@56@<EMAIL>");
-- 群呼叫不包含发起方
local my_query_callee = string.format("select username,groupname,opensipsNode from smarthome_userinfo where groupname='%s' and groupring='1' and username!='%s'", callee, caller);
local my_query_caller = string.format("select username,groupname,opensipsNode from userinfo where username='%s' limit 1 UNION select username,groupname,opensipsNode from smarthome_userinfo where username='%s' limit 1", caller, caller);
local groupcallstring = ""

--查询数据库找到对应的节点信息
assert(dbh:connected());

dbh:query(my_query_caller,function(row)
   req_caller_opensips_node=string.format("%s",row.opensipsNode);
end);

dbh:query(my_query_callee,function(row)
   req_callee_username=string.format("%s",row.username);
   if req_callee_username ~= caller then
        req_callee_opensips_node=string.format("%s",row.opensipsNode);
        if(req_caller_opensips_node == "" or req_callee_opensips_node == "" or req_callee_opensips_node  == req_caller_opensips_node) then
            groupcallstring = groupcallstring.."user/"..req_callee_username..",";
        else
            groupcallstring = groupcallstring.."sofia/gateway/"..req_callee_opensips_node.."/"..req_callee_username..",";
        end
   end
end);

--WARNING
freeswitch.consoleLog("NOTICE","groupcallstring="..groupcallstring.."\n");
dbh:release();

callstring = groupcallstring;
session:execute("bridge",callstring);



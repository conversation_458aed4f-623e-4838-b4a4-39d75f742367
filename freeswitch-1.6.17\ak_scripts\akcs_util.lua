local akcs_util = {}

--lua不支持longlong类型,并且没有直接支持雪花算法,通过动态库的方式比较耗性能,生成18位随机数字代替trace_id
function akcs_util.generateTraceId(length)
	math.randomseed(os.time() + os.clock())
    local number = "1"
    for i = 1, length do
        number = number .. tostring(math.random(0, 9))
    end
    return number
end

function akcs_util.split(str,reps)
    local resultStrList = {}
    string.gsub(str,'[^'..reps..']+',function (w)
        table.insert(resultStrList,w)
    end)
    return resultStrList
end

return akcs_util
<?xml version="1.0" encoding="utf-8"?>
<!--
    NOTICE:
    
    This context is usually accessed via authenticated callers on the sip profile on port 5060 
    or transfered callers from the public context which arrived via the sip profile on port 5080.
    
    Authenticated users will use the user_context variable on the user to determine what context
    they can access.  You can also add a user in the directory with the cidr= attribute acl.conf.xml
    will build the domains ACL using this value.

expression则是设置匹配的正则表达式，这里直接关乎的这个拨号计划是否可以匹配上，freeswitch的正则和平常我们使用的正则都是一样的，只要搞懂这些正则，即可写好一个拨号计划。
1. ^表示匹配以XXX开头的 如：^1234

| 表示或的意思   比如 ^1234|5678
[] 表示匹配一个范围 比如：[0-9] 表示匹配0-9的数字
\d  表示匹配一个数字，比如 \d 就相同于匹配0-9
+   表示匹配至少一个或者多个，比如 \d+ 表示匹配至少1个数字
*   表示匹配任意0个或者多个  比如\d*  表示匹配0个或者多个
$  表示匹配什么结尾的，如： 456$ 表示匹配456结尾的
{}  表示精确匹配位数，如 \d{5} 表示精确匹配5位数字
 .  表示匹配任意一个字符

-->
<!-- http://wiki.freeswitch.org/wiki/Dialplan_XML -->
<include>
  <context name="default">
    <!--
      You can place files in the default directory to get included.
        注意以下拨号计划的顺序不能调整，否则可能存在匹配错误的风险
        akcs_call,
        akcs_groupcall,
        telnyx_test,
        japan,
        canada,
        us_*,
        china,
        international
    -->
    <X-PRE-PROCESS cmd="include" data="akcs_call.xml"/>
    <X-PRE-PROCESS cmd="include" data="akcs_groupcall.xml"/>
    <X-PRE-PROCESS cmd="include" data="telnyx_test.xml"/>
    <X-PRE-PROCESS cmd="include" data="japan.xml"/>
    <X-PRE-PROCESS cmd="include" data="canada.xml"/>
    <X-PRE-PROCESS cmd="include" data="us_newyork.xml"/>
    <X-PRE-PROCESS cmd="include" data="us_california.xml"/>
    <X-PRE-PROCESS cmd="include" data="us_florida.xml"/>
    <X-PRE-PROCESS cmd="include" data="us_newjersey.xml"/>
    <X-PRE-PROCESS cmd="include" data="us_texas.xml"/>
    <X-PRE-PROCESS cmd="include" data="china.xml"/>
    <X-PRE-PROCESS cmd="include" data="international.xml"/>
  </context>
</include>

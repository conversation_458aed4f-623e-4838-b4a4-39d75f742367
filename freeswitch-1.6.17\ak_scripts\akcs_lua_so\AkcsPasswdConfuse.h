#ifndef __AKCS_PASSWD_CONFUSE_H_
#define __AKCS_PASSWD_CONFUSE_H_
#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>

/*
freeswitch 那里的lua动态库需要用纯c的接口，所以统一改为c的接口
*/
#ifdef __cplusplus
extern "C" {
#endif

int CreateRandStr(char *str, int len);
int GetConfuseOffsetIndex(char element);
int PasswdDecode(const char *src, int srclen, char *dst, int dst_len);
int PasswdEncode(const char *origin_code, int code_len, char *dst, int dst_len);

#ifdef __cplusplus
}
#endif

#endif

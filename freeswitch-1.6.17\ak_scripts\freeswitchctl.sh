#!/bin/bash
ACMD="$1"
WORK_DIR="/usr/local/freeswitch/scripts"

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_freeswitch()
{
	sh ${WORK_DIR}/freeswitch.sh start
}

stop_freeswitch()
{
    sh ${WORK_DIR}/freeswitch.sh stop
}

status_freeswitch()
{
	sh ${WORK_DIR}/freeswitch.sh status
}

uninstall_freeswitch()
{
    sh ${WORK_DIR}/freeswitch.sh stop
    kill -9 `ps aux | grep freeswitchrun.sh |grep -v grep | awk '{print $2}'`
    rm -rf /usr/local/freeswitch
}


case $ACMD in
  start)
        start_freeswitch
    ;;
  stop)
        stop_freeswitch
    ;;
  uninstall)
        uninstall_freeswitch
    ;;
  restart)
	stop_freeswitch
    sleep 1
    start_freeswitch
    ;;
  status)
    status_freeswitch
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status|uninstall"
    ;;
esac
exit


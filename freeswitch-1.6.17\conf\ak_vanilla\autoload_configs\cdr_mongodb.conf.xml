<configuration name="cdr_mongodb.conf" description="MongoDB CDR logger">
  <settings>
    <!-- Hostnames and IPv6 addrs not supported (yet) -->
    <param name="host" value="127.0.0.1"/>
    <param name="port" value="27017"/>

    <!-- Namespace format is database.collection -->
    <param name="namespace" value="test.cdr"/>

    <!-- If true, create CDR for B-leg of call (default: true) -->
    <param name="log-b-leg" value="false"/>
  </settings>
</configuration>

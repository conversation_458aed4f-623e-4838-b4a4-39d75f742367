<configuration name="logfile.conf" description="File Logging">
  <settings>
   <!-- true to auto rotate on HUP, false to open/close -->
   <param name="rotate-on-hup" value="true"/>
  </settings>
  <profiles>
    <profile name="default">
      <settings>
        <!-- File to log to -->
	<!--<param name="logfile" value="/var/log/freeswitch.log"/>-->
        <!-- At this length in bytes rotate the log file (0 for never) -->
        <param name="rollover" value="10485760"/>
		<!-- Maximum number of log files to keep before wrapping -->
		<!-- If this parameter is enabled, the log filenames will not include a date stamp -->
		<!-- <param name="maximum-rotate" value="32"/> -->
        <!-- Uncomment to prefix all log lines by the session's uuid  -->
        <param name="uuid" value="true" />
      </settings>
      <mappings>
	<!-- 
	     name can be a file name, function name or 'all' 
	     value is one or more of debug,info,notice,warning,err,crit,alert,all
	     Please see comments in console.conf.xml for more information
	-->
	<map name="all" value="debug,info,notice,warning,err,crit,alert"/>
      </mappings>
    </profile>
  </profiles>
</configuration>

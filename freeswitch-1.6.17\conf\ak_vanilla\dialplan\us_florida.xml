<?xml version="1.0" encoding="utf-8"?>
<!--
	美国 Florida 落地 +18635914605
-->
<include>
	<extension name="Florida">
        <condition field="destination_number" expression="^0(1)(239|305|321|352|386|407|448|561|656|689|727|754|772|786|813|850|863|904|941|954)(\d+)$">
            <action application="set" data="effective_caller_id_number=+18635914605"/>  <!--(a Twilio number) -->
            <action application="set" data="effective_caller_id_name=+18635914605"/>  <!--(a Twilio number) -->
            <action application="set" data="destination_number_country=$1"/>  <!--取国家号位 -->
            <action application="set" data="destination_number_region=$2"/> <!--取地区号位 -->
            <action application="set" data="destination_number_code=$3"/> <!--取号码位 -->
            <action application="set" data="destination_number=$1$2$3"/> <!--设置被叫 -->
            <action application="set" data="landline_calling=1"/><!--对应落地呼叫时支持RTP混淆 -->
            <action application="set" data="execute_on_answer=sched_hangup +360 alloted_timeout" />
            <action application="lua" data="akcs_phonecall.lua"/>
        </condition>
    </extension>
</include>

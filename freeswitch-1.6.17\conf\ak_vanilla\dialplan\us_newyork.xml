<?xml version="1.0" encoding="utf-8"?>
<!--
	美国 NewYork 落地 +18382026226
-->
<include>
    <extension name="NewYork">
        <condition field="destination_number" expression="^0(1)(212|315|332|347|363|516|518|585|607|631|646|680|716|718|838|845|914|917|929|934)(\d+)$">
            <action application="set" data="effective_caller_id_number=+18382026226"/>  <!--(a Twilio number) -->
            <action application="set" data="effective_caller_id_name=+18382026226"/>  <!--(a Twilio number) -->
            <action application="set" data="destination_number_country=$1"/>  <!--取国家号位 -->
            <action application="set" data="destination_number_region=$2"/> <!--取地区号位 -->
            <action application="set" data="destination_number_code=$3"/> <!--取号码位 -->
            <action application="set" data="destination_number=$1$2$3"/> <!--设置被叫 -->
            <action application="set" data="landline_calling=1"/><!--对应落地呼叫时支持RTP混淆 -->
            <action application="set" data="execute_on_answer=sched_hangup +360 alloted_timeout" />
            <action application="lua" data="akcs_phonecall.lua"/>
        </condition>
    </extension>
</include>

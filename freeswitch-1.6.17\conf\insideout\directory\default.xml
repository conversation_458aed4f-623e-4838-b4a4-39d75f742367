<!--
    NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE 
    
    FreeSWITCH works off the concept of users and domains just like email.
    You have users that are in domains <NAME_EMAIL>.
    
    When freeswitch gets a register packet it looks for the user in the directory
    based on the from or to domain in the packet depending on how your sofia profile
    is configured.  Out of the box the default domain will be the IP address of the
    machine running FreeSWITCH.  This IP can be found by typing "sofia status" at the
    CLI.  You will register your phones to the IP and not the hostname by default.
    If you wish to register using the domain please open vars.xml in the root conf
    directory and set the default domain to the hostname you desire.  Then you would
    use the domain name in the client instead of the IP address to register 
    with FreeSWITCH.
    
    NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE 
-->

<include>
  <!--the domain or ip (the right hand side of the @ in the addr-->
  <domain name="$${domain}">
    <params>
      <param name="dial-string" value="{presence_id=${dialed_user}@${dialed_domain}}${sofia_contact(${dialed_user}@${dialed_domain})}"/>
    </params>

    <variables>
      <variable name="record_stereo" value="true"/>
      <variable name="default_gateway" value="$${default_provider}"/>
      <variable name="default_areacode" value="$${default_areacode}"/>
      <variable name="transfer_fallback_extension" value="operator"/>
      <variable name="use_profile" value="external"/>
    </variables>

    <X-PRE-PROCESS cmd="include" data="default/*.xml"/>

  </domain>
</include>

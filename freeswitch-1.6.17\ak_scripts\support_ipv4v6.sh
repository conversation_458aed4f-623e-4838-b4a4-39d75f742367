#!/bin/sh
FREESWITCH_INSTALL_PATH=/usr/local/
FREESWITCH_CMD_NAME="freeswitch -nonat"
FREESWITCH_VARS_FILE=${FREESWITCH_INSTALL_PATH}freeswitch/conf/vars.xml

if [ $# != 1 ]; then
    echo "usage:$0 <1 for ipv4 2 for ipv6>"
    exit 1
fi

if [ $1 -eq 1 ]; then
	echo "support ipv4"
	sed -i "s/X-PRE-PROCESS cmd=\"set\" data=\"domain=\$\${local_ip_v6/X-PRE-PROCESS cmd=\"set\" data=\"domain=\$\${local_ip_v4/g" ${FREESWITCH_VARS_FILE}
fi

if [ $1 -eq 2 ]; then
	echo "support ipv6"
	sed -i "s/X-PRE-PROCESS cmd=\"set\" data=\"domain=\$\${local_ip_v4/X-PRE-PROCESS cmd=\"set\" data=\"domain=\$\${local_ip_v6/g" ${FREESWITCH_VARS_FILE}
fi

freeswitchpid=`ps -fe|grep "${FREESWITCH_CMD_NAME}" |grep -v grep |awk '{print $2}'`
if [ -n "${freeswitchpid}" ];then
        echo "${FREESWITCH_CMD_NAME} is running at ${freeswitchpid}, will kill it first."
        kill -kill ${freeswitchpid}
        sleep 2
fi

<configuration name="switch.conf" description="Core Configuration">

  <cli-keybindings>
    <key name="1" value="help"/>
    <key name="2" value="status"/>
    <key name="3" value="show channels"/>
    <key name="4" value="show calls"/>
    <key name="5" value="sofia status"/>
    <key name="6" value="reloadxml"/>
    <key name="7" value="console loglevel 0"/>
    <key name="8" value="console loglevel 7"/>
    <key name="9" value="sofia status profile internal"/>
    <key name="10" value="sofia profile internal siptrace on"/>
    <key name="11" value="sofia profile internal siptrace off"/>
    <key name="12" value="version"/>
  </cli-keybindings> 
  
  <default-ptimes>
    <!-- Set this to override the 20ms assumption of various codecs in the sdp with no ptime defined -->
    <!-- <codec name="G729" ptime="40"/> -->
  </default-ptimes>
  
  <settings>
    <!-- Colorize the Console -->
    <param name="colorize-console" value="true"/>

    <!--Include full timestamps in dialplan logs -->
    <param name="dialplan-timestamps" value="false"/>

    <!-- Run the timer at 20ms by default and drop down as needed unless you set 1m-timer=true which was previous default -->
    <!-- <param name="1ms-timer" value="true"/> -->

    <!--
	Set the Switch Name for HA environments.
	When setting the switch name, it will override the system hostname for all DB and CURL requests
	allowing cluster environments such as RHCS to have identical FreeSWITCH configurations but run
	as different hostnames.
    -->
    <!-- <param name="switchname" value="freeswitch"/> -->

    <!-- Maximum number of simultaneous DB handles open -->
    <param name="max-db-handles" value="50"/>
    <!-- Maximum number of seconds to wait for a new DB handle before failing -->
    <param name="db-handle-timeout" value="10"/>

    <!-- Minimum idle CPU before refusing calls -->
    <!-- <param name="min-idle-cpu" value="25"/> -->

    <!--
	Max number of sessions to allow at any given time.
	
	NOTICE: If you're driving 28 T1's in a single box you should set this to 644*2 or 1288
	this will ensure you're able to use the entire DS3 without a problem.  Otherwise you'll
	be 144 channels short of always filling that DS3 up which can translate into waste.
    -->
    <param name="max-sessions" value="1000"/>
    <!--Most channels to create per second -->
    <param name="sessions-per-second" value="30"/>
    <!-- Default Global Log Level - value is one of debug,info,notice,warning,err,crit,alert -->
    <param name="loglevel" value="debug"/>

    <!-- Set the core DEBUG level (0-10) -->
    <!-- <param name="debug-level" value="10"/> -->

    <!-- SQL Buffer length within rage of 32k to 10m -->
    <!-- <param name="sql-buffer-len" value="1m"/> -->
    <!-- Maximum SQL Buffer length must be greater than sql-buffer-len -->
    <!-- <param name="max-sql-buffer-len" value="2m"/> -->

    <!-- 
	 The min-dtmf-duration specifies the minimum DTMF duration to use on 
	 outgoing events. Events shorter than this will be increased in duration
	 to match min_dtmf_duration. You cannot configure a dtmf duration on a 
	 profile that is less than this setting. You may increase this value,
	 but cannot set it lower than 400. This value cannot exceed 
	 max-dtmf-duration. -->
    <!-- <param name="min-dtmf-duration" value="400"/> -->

    <!-- 
	 The max-dtmf-duration caps the playout of a DTMF event at the specified
	 duration. Events exceeding this duration will be truncated to this
	 duration. You cannot configure a duration on a profile that exceeds
	 this setting. This setting can be lowered, but cannot exceed 192000. 
	 This setting cannot be set lower than min_dtmf_duration. -->
    <!-- <param name="max-dtmf-duration" value="192000"/> -->

    <!-- 
	 The default_dtmf_duration specifies the DTMF duration to use on
	 originated DTMF events or on events that are received without a
	 duration specified. This value can be increased or lowered. This
	 value is lower-bounded by min_dtmf_duration and upper-bounded by
	 max-dtmf-duration\. -->
    <!-- <param name="default-dtmf-duration" value="2000"/> -->

    <!--
	If you want to send out voicemail notifications via Windows you'll need to change the mailer-app
	variable to the setting below:
	
	<param name="mailer-app" value="msmtp"/>
	
	Do not change mailer-app-args.
	You will also need to download a sendmail clone for Windows (msmtp). This version works without issue:
	http://msmtp.sourceforge.net/index.html. Download and copy the .exe to %winddir%\system32.
	You'll need to create a small config file for smtp credentials (host name, authentication, tls, etc.) in
	%USERPROFILE%\Application Data\ called "msmtprc.txt". Below is a sample copy of this file:
	
	###################################
	# The SMTP server of the provider.
	account provider
	host smtp.myisp.com
	from <EMAIL>
	auth login
	user johndoe
	password mypassword
	
	# Set a default account
	account default : provider
	###################################
	
    -->    

    <param name="mailer-app" value="sendmail"/>
    <param name="mailer-app-args" value="-t"/>
    <param name="dump-cores" value="yes"/>

    <!-- Enable verbose channel events to include every detail about a channel on every event  -->
    <!-- <param name="verbose-channel-events" value="no"/> -->

    <!-- Enable clock nanosleep -->
    <!-- <param name="enable-clock-nanosleep" value="true"/> -->

    <!-- Enable monotonic timing -->
    <!-- <param name="enable-monotonic-timing" value="true"/> -->

    <!-- NEEDS DOCUMENTATION -->
    <!-- <param name="enable-softtimer-timerfd" value="true"/> -->
    <!-- <param name="enable-cond-yield" value="true"/> -->
    <!-- <param name="enable-timer-matrix" value="true"/> -->
    <!-- <param name="threaded-system-exec" value="true"/> -->
    <!-- <param name="tipping-point" value="0"/> -->
    <!-- <param name="timer-affinity" value="disabled"/> -->
    <!-- NEEDS DOCUMENTATION -->

    <!-- RTP port range -->
    <!-- <param name="rtp-start-port" value="16384"/> -->
    <!-- <param name="rtp-end-port" value="32768"/> -->

    <param name="rtp-enable-zrtp" value="true"/>

    <!-- <param name="core-db-dsn" value="pgsql://hostaddr=127.0.0.1 dbname=freeswitch user=freeswitch password='' options='-c client_min_messages=NOTICE' application_name='freeswitch'" /> -->
    <!-- <param name="core-db-dsn" value="dsn:username:password" /> -->
    <!-- 
	 Allow to specify the sqlite db at a different location (In this example, move it to ramdrive for
	 better performance on most linux distro (note, you loose the data if you reboot))
    -->
    <!-- <param name="core-db-name" value="/dev/shm/core.db" /> -->

    <!-- The system will create all the db schemas automatically, set this to false to avoid this behaviour -->
    <!-- <param name="auto-create-schemas" value="true"/> -->
    <!-- <param name="auto-clear-sql" value="true"/> -->
    <!-- <param name="enable-early-hangup" value="true"/> -->

    <!-- <param name="core-dbtype" value="MSSQL"/> -->

    <!-- Allow multiple registrations to the same account in the central registration table -->
    <!-- <param name="multiple-registrations" value="true"/> -->

  </settings>

</configuration>


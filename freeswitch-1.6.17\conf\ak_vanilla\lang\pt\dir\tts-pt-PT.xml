<include><!--This line will be ignored it's here to validate the xml and is optional -->

	<macro name="directory_intro">
		<input pattern="^(last_name)" break_on_match="false">
			<match>
				<action function="speak-text" data="Por favor introduza os primeiras letras do apelido da pessoa"/>
			</match>
		</input>
		<input pattern="^(first_name)" break_on_match="false">
			<match>
				<action function="speak-text" data="Por favor introduza os primeiros dígitos do primeiro nome da pessoa"/>
			</match>
		</input>
		<input pattern="^(last_name):([0-9#*])$" break_on_match="false">
			<match>
				<action function="speak-text" data="Para pesquisar por primeiro nome, marque $2"/>
			</match>
		</input>
		<input pattern="^(first_name):([0-9#*])$" break_on_match="false">
			<match>
				<action function="speak-text" data="Para pesquisar por apelido, marque $2"/>
			</match>
		</input>
	</macro>

	<macro name="directory_min_search_digits">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="Você necessita de especificar um mínimo de $1 letras do nome da pessoa, tente novamente."/>
			</match>
		</input>
	</macro>

	<macro name="directory_result_count">
		<input pattern="^0$" break_on_match="true">
			<match>
				<action function="speak-text" data="Não foram encontrados resultados, tente novamente."/>
			</match>
		</input>
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="$1 resultados encontrados para a sua pesquisa."/>
			</match>
		</input>
	</macro>

	<macro name="directory_result_count_too_large">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="A sua pesquisa devolveu demasiados resultados, por favor tente novamente."/>
			</match>
		</input>

	</macro>

	<macro name="directory_result_last">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="Não há mais resultados"/>
			</match>
		</input>

	</macro>

	<macro name="directory_result_item">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="Resultado número $1"/>
			</match>
		</input>
	</macro>

	<macro name="directory_result_menu">
		<input pattern="^([0-9#*]),([0-9#*]),([0-9#*]),([0-9#*])$">
			<match>
				<action function="speak-text" data="Para seleccionar este resultado marque $1, Para o resultado seguinte marque $2, Para o resultado anterior marque $3, Para iniciar uma nova pesquisa marque $4"/>
			</match>
		</input>
	</macro>

	<macro name="directory_result_at">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="Na extensão $1"/>
			</match>
		</input>
	</macro>
	<macro name="directory_result_say_name">
		<input pattern="^(.*)$">
			<match>
				<action function="speak-text" data="$1"/>
			</match>
		</input>
	</macro>

</include><!--This line will be ignored it's here to validate the xml and is optional -->

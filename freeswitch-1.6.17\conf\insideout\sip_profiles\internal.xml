<!--
    This is a sofia sip profile/user agent.  This will service exactly one ip and port.
    In FreeSWITCH you can run multiple sip user agents on their own ip and port.

    When you hear someone say "sofia profile" this is what they are talking about.
-->

<!-- http://wiki.freeswitch.org/wiki/Sofia_Configuration_Files -->
<profile name="internal">
  <!--aliases are other names that will work as a valid profile name for this profile-->
  <aliases>
    <alias name="local"/>
  </aliases>

  <!-- Outbound Registrations -->
  <gateways>
    <X-PRE-PROCESS cmd="include" data="internal/*.xml"/>
  </gateways>

  <domains>
    <!-- indicator to parse the directory for domains with parse="true" to get gateways-->
    <!--<domain name="$${domain}" parse="true"/>-->
    <!-- indicator to parse the directory for domains with parse="true" to get gateways and alias every domain to this profile -->
    <!--<domain name="all" alias="true" parse="true"/>-->
    <domain name="all" alias="true" parse="false"/>
  </domains>

  <settings>
    <!--
        When calls are in no media this will bring them back to media
        when you press the hold button.
    -->
    <!--<param name="media-option" value="resume-media-on-hold"/> -->
    <!--
        This will allow a call after an attended transfer go back to
        bypass media after an attended transfer.
    -->
    <!--<param name="media-option" value="bypass-media-after-att-xfer"/>-->

    <!-- Can be set to "_undef_" to remove the User-Agent header -->
    <!-- <param name="user-agent-string" value="FreeSWITCH Rocks!"/> -->
    <param name="debug" value="0"/>
    <param name="sip-trace" value="no"/>
    <param name="context" value="public"/>
    <param name="rfc2833-pt" value="101"/>
    <!-- port to bind to for sip traffic -->
    <param name="sip-port" value="$${internal_sip_port}"/>
    <param name="dialplan" value="enum,XML,lcr"/>
    <param name="dtmf-duration" value="100"/>
    <param name="codec-prefs" value="$${global_codec_prefs}"/>
    <param name="use-rtp-timer" value="true"/>
    <param name="rtp-timer-name" value="soft"/>
    <!-- ip address to use for rtp, DO NOT USE HOSTNAMES ONLY IP ADDRESSES -->
    <param name="rtp-ip" value="$${internal_ip_v4}"/>
    <!-- ip address to bind to, DO NOT USE HOSTNAMES ONLY IP ADDRESSES -->
    <param name="sip-ip" value="$${internal_ip_v4}"/>
    <param name="hold-music" value="$${hold_music}"/>
    <!--<param name="apply-nat-acl" value="rfc1918"/>-->
    <!--<param name="aggressive-nat-detection" value="true"/>-->
    <!--<param name="enable-timer" value="false"/>-->
    <!--<param name="enable-100rel" value="true"/>-->
    <!--<param name="minimum-session-expires" value="120"/>-->
    <param name="apply-inbound-acl" value="domains"/>
    <!--<param name="apply-register-acl" value="domains"/>-->
    <!--<param name="dtmf-type" value="info"/>-->
    <param name="record-template" value="$${base_dir}/recordings/${caller_id_number}.${target_domain}.${strftime(%Y-%m-%d-%H-%M-%S)}.wav"/>


    <!-- This setting is for AAL2 bitpacking on G726 -->
    <!-- <param name="bitpacking" value="aal2"/> -->
    <!--max number of open dialogs in proceeding -->
    <!--<param name="max-proceeding" value="1000"/>-->
    <!--session timers for all call to expire after the specified seconds -->
    <!--<param name="session-timeout" value="120"/>-->
    <!-- Can be 'true' or 'contact' -->
    <!--<param name="multiple-registrations" value="contact"/>-->
    <!--set to 'greedy' if you want your codec list to take precedence -->
    <param name="inbound-codec-negotiation" value="generous"/>
    <!-- if you want to send any special bind params of your own -->
    <!--<param name="bind-params" value="transport=udp"/>-->
    <!--<param name="unregister-on-options-fail" value="true"/>-->

    <!-- TLS: disabled by default, set to "true" to enable -->
    <param name="tls" value="$${internal_ssl_enable}"/>
    <!-- additional bind parameters for TLS -->
    <param name="tls-bind-params" value="transport=tls"/>
    <!-- Port to listen on for TLS requests. (5061 will be used if unspecified) -->
    <param name="tls-sip-port" value="$${internal_tls_port}"/>
    <!-- Location of the agent.pem and cafile.pem ssl certificates (needed for TLS server) -->
    <param name="tls-cert-dir" value="$${internal_ssl_dir}"/>
    <!-- TLS version ("sslv23" (default), "tlsv1"). NOTE: Phones may not work with TLSv1 -->
    <param name="tls-version" value="$${sip_tls_version}"/>

    <!--If you don't want to pass through timestamps from 1 RTP call to another (on a per call basis with rtp_rewrite_timestamps chanvar)-->
    <!--<param name="rtp-rewrite-timestamps" value="true"/>-->
    <!--<param name="pass-rfc2833" value="true"/>-->
    <!--If you have ODBC support and a working dsn you can use it instead of SQLite-->
    <!--<param name="odbc-dsn" value="dsn:user:pass"/>-->

    <!--Uncomment to set all inbound calls to no media mode-->
    <!--<param name="inbound-bypass-media" value="true"/>-->

    <!--Uncomment to set all inbound calls to proxy media mode-->
    <!--<param name="inbound-proxy-media" value="true"/>-->

    <!-- Let calls hit the dialplan before selecting codec for the a-leg -->
    <param name="inbound-late-negotiation" value="true"/>

    <!-- Allow ZRTP clients to negotiate end-to-end security associations (also enables late negotiation) -->
    <param name="inbound-zrtp-passthru" value="true"/>

    <!-- this lets anything register -->
    <!--  comment the next line and uncomment one or both of the other 2 lines for call authentication -->
    <!-- <param name="accept-blind-reg" value="true"/> -->

    <!-- accept any authentication without actually checking (not a good feature for most people) -->
    <!-- <param name="accept-blind-auth" value="true"/> -->

    <!-- suppress CNG on this profile or per call with the 'suppress_cng' variable -->
    <!-- <param name="suppress-cng" value="true"/> -->

    <!--TTL for nonce in sip auth-->
    <param name="nonce-ttl" value="60"/>
    <!--Uncomment if you want to force the outbound leg of a bridge to only offer the codec
        that the originator is using-->
    <!--<param name="disable-transcoding" value="true"/>-->
    <!-- Used for when phones respond to a challenged ACK with method INVITE in the hash -->
    <!--<param name="NDLB-broken-auth-hash" value="true"/>-->
    <!-- add a ;received="<ip>:<port>" to the contact when replying to register for nat handling -->
    <!--<param name="NDLB-received-in-nat-reg-contact" value="true"/>-->
    <param name="auth-calls" value="$${internal_auth_calls}"/>
    <!-- Force the user and auth-user to match. -->
    <param name="inbound-reg-force-matching-username" value="true"/>
    <!-- on authed calls, authenticate *all* the packets not just invite -->
    <param name="auth-all-packets" value="false"/>
    <!-- <param name="ext-rtp-ip" value="$${external_rtp_ip}"/> -->
    <!-- <param name="ext-sip-ip" value="$${external_sip_ip}"/> -->
    <!-- rtp inactivity timeout -->
    <param name="rtp-timeout-sec" value="300"/>
    <param name="rtp-hold-timeout-sec" value="1800"/>
    <!-- VAD choose one (out is a good choice); -->
    <!-- <param name="vad" value="in"/> -->
    <!-- <param name="vad" value="out"/> -->
    <!-- <param name="vad" value="both"/> -->
    <!--<param name="alias" value="sip:**********:5555"/>-->

    <!--all inbound reg will look in this domain for the users -->
    <param name="force-register-domain" value="$${domain}"/>
    <!--all inbound reg will stored in the db using this domain -->
    <param name="force-register-db-domain" value="$${domain}"/>
    <!--enable to use presence -->
    <param name="manage-presence" value="true"/>
    <!-- used to share presence info across sofia profiles -->
    <!-- Name of the db to use for this profile -->
    <param name="dbname" value="$${domain}"/>
    <param name="presence-hosts" value="$${domain}"/>
    <!-- ************************************************* -->


    <!--force suscription expires to a lower value than requested-->
    <!--<param name="force-subscription-expires" value="60"/>-->
    <!-- disable register and transfer which may be undesirable in a public switch -->
    <!--<param name="disable-transfer" value="true"/>-->
    <!--<param name="disable-register" value="true"/>-->

    <!-- enable-3pcc can be set to either 'true' or 'proxy', true accepts the call right away, proxy waits until the call has been answered then sends accepts -->
    <!--<param name="enable-3pcc" value="true"/>-->

    <!-- use at your own risk or if you know what this does.-->
    <!--<param name="NDLB-force-rport" value="true"/>-->
    <!--
        Choose the realm challenge key. Default is auto_to if not set.

        auto_from  - uses the from field as the value for the sip realm.
        auto_to    - uses the to field as the value for the sip realm.
        <anyvalue> - you can input any value to use for the sip realm.

        If you want URL dialing to work you'll want to set this to auto_from.

        If you use any other value besides auto_to or auto_from you'll loose
        the ability to do multiple domains.

        Note: comment out to restore the behavior before 2008-09-29

    -->
    <param name="challenge-realm" value="auto_from"/>
    <!--<param name="disable-rtp-auto-adjust" value="true"/>-->
    <!-- on inbound calls make the uuid of the session equal to the sip call id of that call -->
    <!--<param name="inbound-use-callid-as-uuid" value="true"/>-->
    <!-- on outbound calls set the callid to match the uuid of the session -->
    <!--<param name="outbound-use-uuid-as-callid" value="true"/>-->
  </settings>
</profile>

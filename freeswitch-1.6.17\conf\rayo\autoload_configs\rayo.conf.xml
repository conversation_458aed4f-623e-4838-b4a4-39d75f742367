<configuration name="rayo.conf" description="Rayo server config">

	<!-- rayo params -->
	<settings>
		<!-- ends idle calls : unbridged calls that have not been controlled by client in some time -->
		<param name="max-idle-sec" value="300"/>
		<!-- conference profile to use for mixers- sla = shared line appearance / conference /w no audio -->
		<param name="mixer-conf-profile" value="sla"/>
		<!-- if true, to attribute in offer uses URI instead of name/number -->
		<param name="offer-uri" value="true"/>
		<!-- how offers are distributed to clients (all, first, random). -->
		<param name="offer-algorithm" value="all"/>
		<!-- If offer is not answered after timeout, next client is offered (based on algorithm picked).
		     If no other clients are available, the call is rejected.  Set to 0 to disable -->
		<param name="offer-timeout-ms" value="5000"/>
		<!-- if true, channel variables are added to rayo client offer -->
		<param name="add-variables-to-offer" value="false"/>
                <!-- if true, channel variables are added to offer, ringing, answered, and end events sent to rayo clients -->
		<param name="add-variables-to-events" value="false"/>
	</settings>

	<!-- record component params -->
	<record>
		<param name="record-file-prefix" value="$${recordings_dir}/"/>
	</record>

	<!-- input component params -->
	<input>
		<param name="default-recognizer" value="pocketsphinx"/>
	</input>

	<!-- send/receivefax component params -->
	<fax>
		<!-- where to store incoming faxes -->
		<param name="receivefax-file-prefix" value="/tmp/"/>
	</fax>

	<!-- Call progress analysis configuration -->
	<!-- Define CPA detectors.  These are sources for Rayo CPA signal events -->
	<!-- Detectors must fire an event that can be translated by mod_rayo into a CPA signal event -->
	<cpa>
		<!-- map DTMF events to CPA -->
		<detector name="core_dtmf_event">
			<event class="DTMF" value-header="DTMF-Digit" duration-header="DTMF-Duration">
				<signal-type value="dtmf"/>
			</event>
		</detector>

		<!-- map mod_spandsp fax detector to the Rayo CPA events. Fires DETECTED_FAX_* event once and quits. -->
		<detector name="mod_spandsp_fax_ced">
			<start application="spandsp_start_fax_detect" data="event 'Event-Name=CUSTOM,Event-Subclass=DETECTED_FAX_CED' 500 ced"/>
			<stop application="spandsp_stop_fax_detect" data=""/>
			<event class="CUSTOM" subclass="DETECTED_FAX_CED">
				<signal-type value="fax-ced"/>
			</event>
		</detector>

		<detector name="mod_spandsp_fax_cng">
			<start application="spandsp_start_fax_detect" data="event 'Event-Name=CUSTOM,Event-Subclass=DETECTED_FAX_CNG' 500"/>
			<stop application="spandsp_stop_fax_detect" data=""/>
			<event class="CUSTOM" subclass="DETECTED_FAX_CNG">
				<signal-type value="fax-cng"/>
			</event>
		</detector>

		<!-- map mod_spandsp call progress tone detector to Rayo CPA signal events.  Fires DETECTED_TONE events until stopped. -->
		<detector name="mod_spandsp_tone">
			<start application="spandsp_start_tone_detect" data="1"/>
			<stop application="spandsp_stop_tone_detect" data=""/>
			<!-- map tone events to Rayo CPA signal type -->
			<event class="DETECTED_TONE" type-header="Detected-Tone">
				<signal-type header-value="SIT" value="sit"/>
				<signal-type header-value="BUSY_TONE" value="busy"/>
				<signal-type header-value="REORDER_TONE" value="congestion"/>
				<signal-type header-value="RING_TONE" value="ring"/>
			</event>
		</detector>

		<!-- map mod_avmd detector to Rayo CPA beep event.  Fires avmd::beep event once. -->
		<detector name="mod_avmd">
			<start application="avmd" data=""/>
			<stop application="avmd" data="stop"/>
			<event class="CUSTOM" subclass="avmd::beep">
				<signal-type value="beep"/>
			</event>
		</detector>

		<!-- Alternative beep detector using mod_vmd.  Fires vmd::beep events until stopped. -->
		<!--detector name="mod_vmd">
			<start application="vmd" data=""/>
			<stop application="vmd" data="stop"/>
			<event class="CUSTOM" subclass="vmd::beep">
				<signal-type value="beep"/>
			</event>
		</detector-->
	</cpa>

	<!-- XMPP server domain -->
	<domain name="$${rayo_domain_name}" shared-secret="ClueCon">
	<!-- use this instead if you want secure XMPP client to server connections.  Put .crt and .key file in freeswitch/certs -->
	<!--domain name="$${rayo_domain_name}" shared-secret="ClueCon" cert="$${base_dir}/certs/$${rayo_domain_name}.crt" key="$${base_dir}/certs/$${rayo_domain_name}.key"-->

		<!-- Listeners for new Rayo client connections -->
		<!--listen type="c2s" port="5222" address="$${local_ip_v4}" acl="rayo-clients"/-->
		<listen type="c2s" port="5222" address="$${rayo_ip}" acl=""/>

		<!-- Listeners for new server connections -->
		<!--listen type="s2s" port="5269" address="$${local_ip_v4}" acl="rayo-servers"/-->

		<!-- servers to connect to -->
		<!--connect port="5269" address="node.example.com" domain="example.com"/-->

		<!-- Authorized users -->
		<users>
			<user name="usera" password="1"/>
		</users>
	</domain>

	<!-- URI mapping to endpoint / gateway -->
	<dial-gateways>
		<dial-gateway uriprefix="default" dialprefix="sofia/gateway/outbound/" strip="0"/>
		<dial-gateway uriprefix="sip:" dialprefix="sofia/external/" strip="0"/>
		<dial-gateway uriprefix="tel:" dialprefix="sofia/gateway/outbound/" strip="4"/>
		<!-- pass through user and sofia unaltered -->
		<dial-gateway uriprefix="user/" dialprefix="" strip=""/>
		<dial-gateway uriprefix="sofia/" dialprefix="" strip=""/>
		<dial-gateway uriprefix="loopback/" dialprefix="" strip=""/>
	</dial-gateways>

	<!-- IQ request aliases.  Used mainly for testing purposes or for controlling a rayo call via the console -->
	<aliases>
		<alias name="detect" target="call" args="1"><![CDATA[<input xmlns="urn:xmpp:rayo:input:1" mode="cpa"><grammar url="urn:xmpp:rayo:cpa:$1:1"/></input>]]></alias>
		<alias name="detect-once" target="call" args="1"><![CDATA[<input xmlns="urn:xmpp:rayo:input:1" mode="cpa"><grammar url="urn:xmpp:rayo:cpa:$1:1?terminate=true"/></input>]]></alias>
		<alias name="detect-tones" target="call"><![CDATA[<input xmlns="urn:xmpp:rayo:input:1" mode="cpa"><grammar url="urn:xmpp:rayo:cpa:busy:1"/><grammar url="urn:xmpp:rayo:cpa:congestion:1"/><grammar url="urn:xmpp:rayo:cpa:sit:1"/></input>]]></alias>
		<alias name="ping" target="external"><![CDATA[<iq type="get"><ping xmlns="urn:xmpp:ping"/></iq>]]></alias>
		<alias name="dial" target="server" args="2"><![CDATA[<dial xmlns="urn:xmpp:rayo:1" from="$1" to="$2"/>]]></alias>
		<alias name="answer" target="call"><![CDATA[<answer xmlns="urn:xmpp:rayo:1"/>]]></alias>
		<alias name="hangup" target="call"><![CDATA[<hangup xmlns="urn:xmpp:rayo:1"/>]]></alias>
		<alias name="join" target="call" args="1"><![CDATA[<join xmlns="urn:xmpp:rayo:1" call-uri="xmpp:$1"/>]]></alias>
		<alias name="join_mixer_duplex" target="call" args="1"><![CDATA[<join xmlns="urn:xmpp:rayo:1" mixer-name="$1" direction="duplex"/>]]></alias>
		<alias name="join_mixer_send" target="call" args="1"><![CDATA[<join xmlns="urn:xmpp:rayo:1" mixer-name="$1" direction="send"/>]]></alias>
		<alias name="join_mixer_recv" target="call" args="1"><![CDATA[<join xmlns="urn:xmpp:rayo:1" mixer-name="$1" direction="recv"/>]]></alias>
		<alias name="unjoin_mixer" target="call" args="1"><![CDATA[<unjoin xmlns="urn:xmpp:rayo:1" mixer-name="$1"/>]]></alias>
		<alias name="unjoin" target="call"><![CDATA[<unjoin xmlns="urn:xmpp:rayo:1"/>]]></alias>
		<alias name="unjoin_call" target="call" args="1"><![CDATA[<unjoin xmlns="urn:xmpp:rayo:1" call-uri="xmpp:$1"/>]]></alias>
		<alias name="stop" target="component"><![CDATA[<stop xmlns="urn:xmpp:rayo:ext:1"/>]]></alias>
		<alias name="output_bad" target="call"><![CDATA[<output xmlns="urn:xmpp:rayo:output:1" repeat-time="100"></output>]]></alias>
		<alias name="pause" target="output"><![CDATA[<pause xmlns="urn:xmpp:rayo:output:1"/>]]></alias>
		<alias name="resume" target="output"><![CDATA[<resume xmlns="urn:xmpp:rayo:output:1"/>]]></alias>
		<alias name="speed-up" target="output"><![CDATA[<speed-up xmlns="urn:xmpp:rayo:output:1"/>]]></alias>
		<alias name="speed-down" target="output"><![CDATA[<speed-down xmlns="urn:xmpp:rayo:output:1"/>]]></alias>
		<alias name="volume-up" target="output"><![CDATA[<volume-up xmlns="urn:xmpp:rayo:output:1"/>]]></alias>
		<alias name="volume-down" target="output"><![CDATA[<volume-down xmlns="urn:xmpp:rayo:output:1"/>]]></alias>
		<alias name="receivefax" target="call"><![CDATA[<receivefax xmlns="urn:xmpp:rayo:fax:1"/>]]></alias>
		<alias name="sendfax" target="call" args="1"><![CDATA[<sendfax xmlns="urn:xmpp:rayo:fax:1"><document xmlns="urn:xmpp:rayo:fax:1" url="$1"/></sendfax>]]></alias>
		<alias name="record" target="call"><![CDATA[<record xmlns="urn:xmpp:rayo:record:1"/>]]></alias>
		<alias name="record_pause" target="record"><![CDATA[<pause xmlns="urn:xmpp:rayo:record:1"/>]]></alias>
		<alias name="record_resume" target="record"><![CDATA[<resume xmlns="urn:xmpp:rayo:record:1"/>]]></alias>
		<alias name="prompt_barge" target="call">
			<![CDATA[
				<prompt xmlns="urn:xmpp:rayo:prompt:1" barge-in="true">
					<output xmlns="urn:xmpp:rayo:output:1" repeat-times="5">
						<document content-type="application/ssml+xml">
							<![CDATA[<speak><p>Please press a digit.</p></speak>]]]]><![CDATA[>
						</document>
					</output>
					<input xmlns="urn:xmpp:rayo:input:1" mode="dtmf" initial-timeout="5000" inter-digit-timeout="3000">
						<grammar content-type="application/srgs+xml">
							<![CDATA[<grammar mode="dtmf"><rule id="digit" scope="public"><one-of><item>0</item><item>1</item><item>2</item><item>3</item><item>4</item><item>5</item><item>6</item><item>7</item><item>8</item><item>9</item></one-of></rule></grammar>]]]]><![CDATA[>
						</grammar>
					</input>
				</prompt>
			]]>
		</alias>
		<alias name="prompt_no_barge" target="call">
			<![CDATA[
				<prompt xmlns="urn:xmpp:rayo:prompt:1" barge-in="false">
					<output xmlns="urn:xmpp:rayo:output:1" repeat-times="5">
						<document content-type="application/ssml+xml">
							<![CDATA[<speak><p>Please press a digit.</p></speak>]]]]><![CDATA[>
						</document>
					</output>
					<input xmlns="urn:xmpp:rayo:input:1" mode="dtmf" initial-timeout="5000" inter-digit-timeout="3000">
						<grammar content-type="application/srgs+xml">
							<![CDATA[<grammar mode="dtmf"><rule id="digit" scope="public"><one-of><item>0</item><item>1</item><item>2</item><item>3</item><item>4</item><item>5</item><item>6</item><item>7</item><item>8</item><item>9</item></one-of></rule></grammar>]]]]><![CDATA[>
						</grammar>
					</input>
				</prompt>
			]]>
		</alias>
		<alias name="prompt_long" target="call">
			<![CDATA[
				<prompt xmlns="urn:xmpp:rayo:prompt:1" barge-in="true">
					<output xmlns="urn:xmpp:rayo:output:1" repeat-times="100">
						<document content-type="application/ssml+xml">
							<![CDATA[<speak><p>Please press a digit.</p></speak>]]]]><![CDATA[>
						</document>
					</output>
					<input xmlns="urn:xmpp:rayo:input:1" mode="dtmf" initial-timeout="5000" inter-digit-timeout="3000">
						<grammar content-type="application/srgs+xml">
							<![CDATA[<grammar mode="dtmf"><rule id="digit" scope="public"><one-of><item>0</item><item>1</item><item>2</item><item>3</item><item>4</item><item>5</item><item>6</item><item>7</item><item>8</item><item>9</item></one-of></rule></grammar>]]]]><![CDATA[>
						</grammar>
					</input>
				</prompt>
			]]>
		</alias>
		<alias name="prompt_multi_digit" target="call">
			<![CDATA[
				<prompt xmlns="urn:xmpp:rayo:prompt:1" barge-in="true">
					<output xmlns="urn:xmpp:rayo:output:1" repeat-times="100">
						<document content-type="application/ssml+xml">
							<![CDATA[<speak><p>Please press a digit.</p></speak>]]]]><![CDATA[>
						</document>
					</output>
					<input xmlns="urn:xmpp:rayo:input:1" mode="dtmf" initial-timeout="5000" inter-digit-timeout="3000">
						<grammar content-type="application/srgs+xml">
							<![CDATA[<grammar mode="dtmf"><rule id="digit" scope="public"><item repeat="1-4"><one-of><item>0</item><item>1</item><item>2</item><item>3</item><item>4</item><item>5</item><item>6</item><item>7</item><item>8</item><item>9</item></one-of></item></rule></grammar>]]]]><![CDATA[>
						</grammar>
					</input>
				</prompt>
			]]>
		</alias>
		<alias name="prompt_terminator" target="call">
			<![CDATA[
				<prompt xmlns="urn:xmpp:rayo:prompt:1" barge-in="true">
					<output xmlns="urn:xmpp:rayo:output:1" repeat-times="100">
						<document content-type="application/ssml+xml">
							<![CDATA[<speak><p>Please press a digit.</p></speak>]]]]><![CDATA[>
						</document>
					</output>
					<input xmlns="urn:xmpp:rayo:input:1" mode="dtmf" initial-timeout="5000" inter-digit-timeout="3000" terminator="#">
						<grammar content-type="application/srgs+xml">
							<![CDATA[<grammar mode="dtmf"><rule id="digit" scope="public"><item repeat="1-4"><one-of><item>0</item><item>1</item><item>2</item><item>3</item><item>4</item><item>5</item><item>6</item><item>7</item><item>8</item><item>9</item></one-of></item></rule></grammar>]]]]><![CDATA[>
						</grammar>
					</input>
				</prompt>
			]]>
		</alias>
		<alias name="prompt_input_bad" target="call">
			<![CDATA[
				<prompt xmlns="urn:xmpp:rayo:prompt:1" barge-in="true">
					<output xmlns="urn:xmpp:rayo:output:1" repeat-times="100">
						<document content-type="application/ssml+xml">
							<![CDATA[<speak><p>Please press a digit.</p></speak>]]]]><![CDATA[>
						</document>
					</output>
					<input xmlns="urn:xmpp:rayo:input:1" mode="dtf" initial-timeout="5000" inter-digit-timeout="3000" terminator="#">
						<grammar content-type="application/srgs+xml">
							<![CDATA[<grammar mode="dtmf"><rule id="digit" scope="public"><item repeat="1-4"><one-of><item>0</item><item>1</item><item>2</item><item>3</item><item>4</item><item>5</item><item>6</item><item>7</item><item>8</item><item>9</item></one-of></item></rule></grammar>]]]]><![CDATA[>
						</grammar>
					</input>
				</prompt>
			]]>
		</alias>
		<alias name="prompt_output_bad" target="call">
			<![CDATA[
				<prompt xmlns="urn:xmpp:rayo:prompt:1" barge-in="true">
					<output xmlns="urn:xmpp:rayo:output:1" repeat-time="100">
						<document content-type="application/ssml+xml">
							<![CDATA[<speak><p>Please press a digit.</p></speak>]]]]><![CDATA[>
						</document>
					</output>
					<input xmlns="urn:xmpp:rayo:input:1" mode="dtmf" initial-timeout="5000" inter-digit-timeout="3000" terminator="#">
						<grammar content-type="application/srgs+xml">
							<![CDATA[<grammar mode="dtmf"><rule id="digit" scope="public"><item repeat="1-4"><one-of><item>0</item><item>1</item><item>2</item><item>3</item><item>4</item><item>5</item><item>6</item><item>7</item><item>8</item><item>9</item></one-of></item></rule></grammar>]]]]><![CDATA[>
						</grammar>
					</input>
				</prompt>
			]]>
		</alias>
		<alias name="input" target="call">
			<![CDATA[
				<input xmlns="urn:xmpp:rayo:input:1" mode="dtmf" initial-timeout="5000" inter-digit-timeout="3000">
					<grammar content-type="application/srgs+xml">
						<![CDATA[<grammar mode="dtmf"><rule id="digit" scope="public"><one-of><item>0</item><item>1</item><item>2</item><item>3</item><item>4</item><item>5</item><item>6</item><item>7</item><item>8</item><item>9</item></one-of></rule></grammar>]]]]><![CDATA[>
					</grammar>
				</input>
			]]>
		</alias>
		<alias name="input_voice_yesno_unimrcp" target="call">
			<![CDATA[
				<input xmlns="urn:xmpp:rayo:input:1" mode="voice" recognizer="unimrcp">
					<grammar content-type="application/srgs+xml>
					<![CDATA[
						<grammar xmlns="http://www.w3.org/2001/06/grammar"
							xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
							xsi:schemaLocation="http://www.w3.org/2001/06/grammar http://www.w3.org/TR/speech-grammar/grammar.xsd"
							xml:lang="en-US" version="1.0">
								<rule id="yesno"><one-of><item>yes</item><item>no</item></one-of></rule></grammar>
					]]]]><![CDATA[>
					</grammar>
				</input>
			]]>
		</alias>
		<alias name="input_voice_yesno_unimrcp_timeout" target="call">
			<![CDATA[
				<input xmlns="urn:xmpp:rayo:input:1" mode="voice" recognizer="unimrcp" max-silence="5000" initial-timeout="5000">
					<grammar content-type="application/srgs+xml>
					<![CDATA[
						<grammar xmlns="http://www.w3.org/2001/06/grammar"
							xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
							xsi:schemaLocation="http://www.w3.org/2001/06/grammar http://www.w3.org/TR/speech-grammar/grammar.xsd"
							xml:lang="en-US" version="1.0">
								<rule id="yesno"><one-of><item>yes</item><item>no</item></one-of></rule></grammar>
					]]]]><![CDATA[>
					</grammar>
				</input>
			]]>
		</alias>

		<alias name="input_voice_yesno_pocketsphinx" target="call">
			<![CDATA[
				<input xmlns="urn:xmpp:rayo:input:1" mode="voice" recognizer="pocketsphinx" max-silence="5000" initial-timeout="5000">
					<grammar content-type="application/srgs+xml>
					<![CDATA[
						<grammar xmlns="http://www.w3.org/2001/06/grammar"
							xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
							xsi:schemaLocation="http://www.w3.org/2001/06/grammar http://www.w3.org/TR/speech-grammar/grammar.xsd"
							xml:lang="en-US" version="1.0">
								<rule id="yesno"><one-of><item>yes</item><item>no</item></one-of></rule></grammar>
					]]]]><![CDATA[>
					</grammar>
				</input>
			]]>
		</alias>

		<alias name="input_voice_yesno_default" target="call">
			<![CDATA[
				<input xmlns="urn:xmpp:rayo:input:1" mode="voice" max-silence="5000" initial-timeout="5000">
					<grammar content-type="application/srgs+xml>
					<![CDATA[
						<grammar xmlns="http://www.w3.org/2001/06/grammar"
							xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
							xsi:schemaLocation="http://www.w3.org/2001/06/grammar http://www.w3.org/TR/speech-grammar/grammar.xsd"
							xml:lang="en-US" version="1.0">
								<rule id="yesno"><one-of><item>yes</item><item>no</item></one-of></rule></grammar>
					]]]]><![CDATA[>
					</grammar>
				</input>
			]]>
		</alias>

	</aliases>

</configuration>

<configuration name="acl.conf" description="Network Lists">
  <network-lists>

    <list name="dl-candidates" default="allow">
      <node type="deny" cidr="10.0.0.0/8"/>
      <node type="deny" cidr="**********/12"/>
      <node type="deny" cidr="***********/16"/>
    </list>

    <list name="rfc1918" default="deny">
      <node type="allow" cidr="10.0.0.0/8"/>
      <node type="allow" cidr="**********/12"/>
      <node type="allow" cidr="***********/16"/>
    </list>

    <list name="lan" default="allow">
      <node type="deny" cidr="************/24"/>
      <node type="allow" cidr="*************/32"/>
    </list>

    <list name="strict" default="deny">
      <node type="allow" cidr="***************/32"/>
    </list>
    <!--
	This will traverse the directory adding all users 
	with the cidr= tag to this ACL, when this ACL matches
	the users variables and params apply as if they 
	digest authenticated.
    -->
    <list name="domains" default="deny">
      <node type="allow" domain="$${domain}"/>
    </list>

  </network-lists>
</configuration>


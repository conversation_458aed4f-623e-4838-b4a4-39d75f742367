package.path = package.path..";/usr/local/freeswitch/scripts/?.lua"
local akcs_util = require("akcs_util")

-- 生成trace_id
trace_id = akcs_util.generateTraceId(18)
session:setVariable("session_trace_id", trace_id);

--加载c库akcslua.so
--package.cpath = "/usr/local/freeswitch/scripts/?.so;"..package.cpath
--local akcslua = require "akcslua"
--freeswitch.consoleLog("NOTICE","lua akcs call dialplan...\n");
caller = session:getVariable("caller_id_number");
session:setVariable("main_site_caller", caller);
caller_name_display = session:getVariable("caller_id_name");
callee = session:getVariable("destination_number");
--是否是跨地域的opensips集群，is_opensips_cluster为true代表现在处于第二台fs
is_opensips_cluster = session:getVariable("is_opensips_cluster");

session:setVariable("is_landline_final","false");
session:setVariable("is_only_smarthome","1");

-- 生成trace_id
trace_id = akcs_util.generateTraceId(18)
session:setVariable("session_trace_id", trace_id);

local dbh = freeswitch.Dbh("freeswitch","dbuser01","Ak@56@<EMAIL>");

caller_name = caller_name_display;
caller_num = caller;
local my_query_caller = string.format("select groupname,opensipsNode from userinfo where username='%s' limit 1 UNION select groupname,opensipsNode from smarthome_userinfo where username='%s' limit 1", caller, caller);
local my_query_callee = string.format("select groupname,opensipsNode from smarthome_userinfo where username='%s' limit 1", callee);

--查询数据库找到对应的节点信息
assert(dbh:connected());
dbh:query(my_query_callee,function(row)
   req_callee_groupname=string.format("%s",row.groupname);
   req_callee_sipEnable=string.format("%s",row.sipEnable);
   req_callee_opensips_node=string.format("%s",row.opensipsNode);
end);
dbh:query(my_query_caller,function(row)
   req_caller_groupname=string.format("%s",row.groupname);
   req_caller_sipEnable=string.format("%s",row.sipEnable);
   req_caller_opensips_node=string.format("%s",row.opensipsNode);
end);
dbh:release();

--akcs呼叫规则
call_enable = 0;
if (req_callee_groupname == req_caller_groupname and req_caller_groupname ~= "0") then	
	call_enable = 1;
else
    freeswitch.consoleLog("notice", "caller group:"..req_caller_groupname.." callee group:"..req_callee_groupname.." is different\n");
end

if (call_enable == 1) then
    --呼叫	
    if(req_caller_opensips_node == "" or req_callee_opensips_node == "" or is_opensips_cluster == "true" or req_callee_opensips_node  == req_caller_opensips_node)then
        callstring = "user/"..callee;
        session:execute("bridge",callstring);
    else
        callstring = "sofia/gateway/"..req_callee_opensips_node.."/"..callee;
        session:execute("bridge",callstring);
    end
end

<configuration name="avmd.conf" description="AVMD config">
    <settings>

    <!-- Edit these settings to change default behaviour
         of each avmd session. Settings can be overwritten
         by values passed dynamically per each session -->


        <!-- Global settings -->

            <!-- define/undefine this to enable/disable logging of avmd
                 intermediate computations to log -->
            <param name="debug" value="0"/>

            <!-- define/undef this to enable/disable verbose logging (and reporting to the console)
                 of detection status and other diagnostics like parameters avmd session has been started with,
                 change of configuration parameters, beep detection status after session ended
                 (stop event is fired independently of this setting and beep status included there) -->
            <param name="report_status" value="1"/>

            <!-- define/undefine this to enable/disable faster computation
                 of arcus cosine - table will be created mapping floats
                 to integers and returning arc cos values given these integer
                 indices into table -->
            <param name="fast_math" value="0"/>
        <!-- Global settings end -->


        <!-- Per call (session) settings. These settings can be overwritten
             with custom/different values per each avmd session -->

            <!-- define/undefine this to classify avmd beep detection as valid
                 only when there is required number of consecutive elements
                 in the SMA buffer without reset -->
            <param name="require_continuous_streak" value="1"/>

            <!-- required number of consecutive elements in the SMA buffer
                 without reset. This parameter helps to avoid false beeps, bigger this value is
                smaller the probability of getting false detection -->
            <param name="sample_n_continuous_streak" value="15"/>

            <!-- define number of samples to skip starting from the beginning
                 of the frame and/or after reset  has happened. This serves the purpose of skipping first few
                estimations on each frame, as these estimations may be inaccurate. This parameter also helps
                to give more robust detections when it's value is increased (up to scertain limit of about 60). -->
            <param name="sample_n_to_skip" value="15"/>

            <!-- define/undefine this to enable/disable simplified estimation
                 of frequency based on approximation of sin(x) with (x)
                 in the range x=[0,PI/2] -->
            <param name="simplified_estimation" value="1"/>

            <!-- define/undefine to enable/disable avmd on internal channel -->
            <param name="inbound_channel" value="0"/>

            <!-- define/undefine to enable/disable avmd on external channel -->
            <param name="outbound_channel" value="1"/>
        <!-- Per call settings end -->
    </settings>
</configuration>


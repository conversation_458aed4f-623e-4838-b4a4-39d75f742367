#!/bin/bash
PROCESS_FREESWITCH_NAME="freeswitch -nonat"
PROCESS_FREESWITCH_PATH=/usr/local/freeswitch/bin/
LOG_FILE=/var/log/freeswitchrun.log
FREESWITCH_PROFILE_PORT=$FS_INTERNAL_SIP_PORT
FREESWITCH_EXTERNAL_PROFILE_PORT=$FS_EXTERNAL_SIP_PORT

APP_STOP_EMAIL_LIST=/usr/local/freeswitch/scripts/notify_email.conf

FIRST_RUN=1 #第一次启动这个程序 不需要通知运维。
MAIL_LIST=`cat $APP_STOP_EMAIL_LIST | grep APP_STOP_EMAIL_LIST | awk -F'=' '{ print $2 }'`

EMAIL_TIMES=60
DEATH_DETECT_TIME_FILE=/tmp/.$PROCESS_FREESWITCH_NAME

#指定odbc库路径 不然会出现odbc连接不到库问题
export  LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/local/odbc/lib
#export SOFIA_DEBUG=9
#export NUA_DEBUG=9
#export NTA_DEBUG=9
#export NEA_DEBUG=9
#export TPORT_DEBUG=9
#export TPORT_LOG=1
#export TPORT_DUMP=/usr/local/freeswitch/log/tport_sip.log
#export SOA_DEBUG=9
#export IPTSEC_DEBUG=9
#export SU_DEBUG=9

app_stop_email() 
{
    email=0
    if [ -f ${DEATH_DETECT_TIME_FILE}* ];then
        time=`ls ${DEATH_DETECT_TIME_FILE}* | awk -F '_' '{print $NF}'`
        unix=`date +%s`
        let time=$time+$EMAIL_TIMES
        if [ $time -lt $unix ];then
            #报警  重新计算时间
            rm  /tmp/.$1*
            touch ${DEATH_DETECT_TIME_FILE}_`date +%s`
            email=1
        fi
    else
        touch ${DEATH_DETECT_TIME_FILE}_`date +%s`
        email=1
    fi
    if [ $email -eq 1 ];then
        processname=$1
        echo "sending email...." >> $LOG_FILE
        echo "${processname} is stopped，请及时排查原因。\nIP:${SERVERIP}" | mutt -s "应用程序停止警告"  ${MAIL_LIST}
    fi
}

check_external_port()
{
    status=0
    for((i=1;i<=3;i++))  
    do   
        ExternalTCPListeningnum=`netstat -nult | egrep ":${FREESWITCH_EXTERNAL_PROFILE_PORT}" | awk '$1 == "tcp" && $NF == "LISTEN" {print $0}' |wc -l`
        ExternalUDPListeningnum=`netstat -nult | egrep ":${FREESWITCH_EXTERNAL_PROFILE_PORT}" | awk '$1 == "udp" && $NF == "0.0.0.0:*" {print $0}' |wc -l`   
        if [ ${ExternalTCPListeningnum} -lt 1 -o ${ExternalUDPListeningnum} -lt 1 ];then
            echo `date` " Port:${FREESWITCH_EXTERNAL_PROFILE_PORT} not be listend normally,restart external profile by fs_cli" >> $LOG_FILE
            fs_cli -x "sofia status"|tee -a $LOG_FILE
            fs_cli -x "sofia profile external start"|tee -a $LOG_FILE
			echo "Freeswitch external port not be listend normally, 已经重启端口,如果持续出现请排查原因。\nIP:${SERVERIP}" | mutt -s "Freeswitch port:8523 not be listend"  ${MAIL_LIST}
            sleep 3
        else
            status=1
            break		
        fi 		
    done
    if [ $status -eq 0 ];then
        freeswitchpid=`ps -fe|grep "${processpath}" |grep -v grep |awk '{print $2}'`
        if [ -n "${freeswitchpid}" ];then
            echo `date` " Port:${FREESWITCH_EXTERNAL_PROFILE_PORT} not be listend normally,restart freeswitch" >> $LOG_FILE
            kill -9 ${freeswitchpid}
        fi
    fi
}

run() {
	processname=$1
    processpath=$2
	ps -fe|grep "${processname}" |grep -v grep	
	if [ $? -ne 0 ]
	then
		date >> $LOG_FILE
		echo "$1 is stopped..." >> $LOG_FILE
		if [ $FIRST_RUN -eq 1 ];then
			sleep 10
		fi
		$processpath & > /dev/null
		if [ $FIRST_RUN -ne 1 ];then
			echo "${processname} stop alarm"  >> $LOG_FILE
			app_stop_email ${processname}
		fi
		sleep 30
	fi

	#netstat -napl |grep ${FREESWITCH_PROFILE_PORT}
	TCPListeningnum=`netstat -nult | egrep ":${FREESWITCH_PROFILE_PORT}" | awk '$1 == "tcp" && $NF == "LISTEN" {print $0}' |wc -l`
	UDPListeningnum=`netstat -nult | egrep ":${FREESWITCH_PROFILE_PORT}" | awk '$1 == "udp" && $NF == "0.0.0.0:*" {print $0}' |wc -l`
	#TotalListeningnum=$((${TCPListeningnum}+${UDPListeningnum}))
	if [ ${TCPListeningnum} -lt 1 -o ${UDPListeningnum} -lt 1 ]
	then
		freeswitchpid=`ps -fe|grep "${processpath}" |grep -v grep |awk '{print $2}'`
		echo `date` " Port:${FREESWITCH_PROFILE_PORT} not be listend normally" >> $LOG_FILE
		if [ -n "${freeswitchpid}" ];then
			kill -9 ${freeswitchpid}
		fi
	fi
	##external port
	check_external_port
}

install_odbc() {
    ODBC_INI_PATH=/etc/odbc.ini
    sed -i "s/Server.*/Server=${DATABASEIP}/g" ${ODBC_INI_PATH}
    sed -i "s/PORT.*/PORT=${DATABASEPORT}/g" ${ODBC_INI_PATH}
}
install_odbc

replace_config_file() {
    INTRENAL_FILE=/usr/local/freeswitch/conf/sip_profiles/internal.xml
    sed -i "s/param name=\"ext-rtp-ip\" value=.*/param name=\"ext-rtp-ip\" value=\"${SERVERIP}\"\/>/g" ${INTRENAL_FILE}
    sed -i "s/param name=\"ext-sip-ip\" value=.*/param name=\"ext-sip-ip\" value=\"${SERVERIP}\"\/>/g" ${INTRENAL_FILE}
    sed -i "s/param name=\"outbound-proxy\" value=\"opensips:5070\"\/>/param name=\"outbound-proxy\" value=\"${OPENSIPS_EXTERNAL_IP}:5070\"\/>/g" ${INTRENAL_FILE}
    sed -i "s/param name=\"sip-ip\" value=\"LISTEN_INNER_IP\"\/>/param name=\"sip-ip\" value=\"${SERVER_INNER_IP}\"\/>/g" ${INTRENAL_FILE}
    
    EXTERNAL_FILE=/usr/local/freeswitch/conf/sip_profiles/external.xml
    sed -i "s/EXTERNAL_RTPSIP_IP/${SERVER_INNER_IP}/g" ${EXTERNAL_FILE}
    sed -i "s/EXTERNAL_RTPSIP_IP/${SERVER_INNER_IP}/g" ${EXTERNAL_FILE}
    sed -i "s/param name=\"ext-rtp-ip\" value=.*/param name=\"ext-rtp-ip\" value=\"${SERVERIP}\"\/>/g" ${EXTERNAL_FILE}
    sed -i "s/param name=\"ext-sip-ip\" value=.*/param name=\"ext-sip-ip\" value=\"${SERVERIP}\"\/>/g" ${EXTERNAL_FILE}	
}
replace_config_file
ldconfig

write_server_conf() {
   echo "ETCD_SERVER_ADDR = ${ETCD_SERVER_ADDR}" > /usr/local/freeswitch/server.conf
   echo "BEANSTALKD_INNER_IP = ${BEANSTALKD_INNER_IP}" >> /usr/local/freeswitch/server.conf
   echo "BEANSTALKD_PORT = ${BEANSTALKD_PORT}" >> /usr/local/freeswitch/server.conf
   echo "DATABASEIP = ${DATABASEIP}" >> /usr/local/freeswitch/server.conf
   echo "DATABASEPORT = ${DATABASEPORT}" >> /usr/local/freeswitch/server.conf
   echo "OPENSIPS_EXTERNAL_IP = ${OPENSIPS_EXTERNAL_IP}" >> /usr/local/freeswitch/server.conf
   echo "OPENSIPS_INNER_IP = ${OPENSIPS_INNER_IP}" >> /usr/local/freeswitch/server.conf
   echo "LOCAL_INNER_IP = ${LOCAL_INNER_IP}" >> /usr/local/freeswitch/server.conf
   echo "LOCAL_EXTERNAL_IP= ${SERVERIP}"  >> /usr/local/freeswitch/server.conf
}
write_server_conf

change_port() {
   VARS_FILE=/usr/local/freeswitch/conf/vars.xml
   sed -i "s/internal_sip_port=5060/internal_sip_port=${FS_INTERNAL_SIP_PORT}/g" ${VARS_FILE}
   sed -i "s/internal_tls_port=5061/internal_tls_port=${FS_INTERNAL_TLS_PORT}/g" ${VARS_FILE}
   sed -i "s/external_sip_port=8523/external_sip_port=${FS_EXTERNAL_SIP_PORT}/g" ${VARS_FILE}
   sed -i "s/external_tls_port=8524/external_tls_port=${FS_EXTERNAL_TLS_PORT}/g" ${VARS_FILE}
   
   ELS_FILE=/usr/local/freeswitch/conf/autoload_configs/event_socket.conf.xml
   sed -i "s/param name=\"listen-port\" value=.*/param name=\"listen-port\" value=\"${FS_ESL_PORT}\"\/>/g" ${ELS_FILE} 
   
   SWITCH_CONF_FILE=/usr/local/freeswitch/conf/autoload_configs/switch.conf.xml
   sed -i "s/.*<param name=\"rtp-start-port\" value=.*/<param name=\"rtp-start-port\" value=\"${FS_RTP_START_PORT}\"\/>/g" ${SWITCH_CONF_FILE}    
   sed -i "s/.*<param name=\"rtp-end-port\" value=.*/<param name=\"rtp-end-port\" value=\"${FS_RTP_END_PORT}\"\/>/g" ${SWITCH_CONF_FILE}
}
change_port

while [ 1 ]
do
	run "$PROCESS_FREESWITCH_NAME" "$PROCESS_FREESWITCH_PATH$PROCESS_FREESWITCH_NAME"
	FIRST_RUN=0
	sleep 3
done
